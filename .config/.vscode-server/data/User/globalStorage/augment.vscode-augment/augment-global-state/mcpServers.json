[{"name": "Context7 MCP - Up-to-date Code Docs For Any API and Prompt", "command": "npx -y @upstash/context7-mcp@latest", "arguments": "", "useShellInterpolation": true, "env": {"XDG_CONFIG_HOME": "/home/<USER>/.config"}, "id": "87a04996-7e79-49a2-802a-5b8cf351dd9d", "disabled": false, "tools": ["resolve-library-id", "get-library-docs"], "disabledTools": []}, {"name": "Supabase Admin MCP Server", "command": "npx -y @supabase/mcp-server-supabase@latest", "arguments": "", "useShellInterpolation": true, "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "XDG_CONFIG_HOME": "/home/<USER>/.config"}, "id": "cab937c2-51ff-497e-bdd4-934285cecfbe", "disabled": false, "tools": ["list_organizations", "get_organization", "list_projects", "get_project", "get_cost", "confirm_cost", "create_project", "pause_project", "restore_project", "list_tables", "list_extensions", "list_migrations", "apply_migration", "execute_sql", "list_edge_functions", "deploy_edge_function", "get_logs", "get_project_url", "get_anon_key", "generate_typescript_types", "create_branch", "list_branches", "delete_branch", "merge_branch", "reset_branch", "rebase_branch"], "disabledTools": []}]