{"/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/ae83046c-54d1-453c-9506-00da83afa63f": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/ae83046c-54d1-453c-9506-00da83afa63f"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e6885490-49cc-45b6-9b58-dbdffc9e0764": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e6885490-49cc-45b6-9b58-dbdffc9e0764"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/6k1I.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/6k1I.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/entries.json"}, "/home/<USER>/workspace/.env.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".env.example"}, "/home/<USER>/workspace/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}, "/home/<USER>/workspace/Makefile": {"rootPath": "/home/<USER>/workspace", "relPath": "<PERSON><PERSON><PERSON>"}, "/home/<USER>/workspace/components.json": {"rootPath": "/home/<USER>/workspace", "relPath": "components.json"}, "/home/<USER>/workspace/drizzle.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "drizzle.config.ts"}, "/home/<USER>/workspace/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package-lock.json"}, "/home/<USER>/workspace/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "/home/<USER>/workspace/postcss.config.js": {"rootPath": "/home/<USER>/workspace", "relPath": "postcss.config.js"}, "/home/<USER>/workspace/tailwind.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "tailwind.config.ts"}, "/home/<USER>/workspace/tsconfig.json": {"rootPath": "/home/<USER>/workspace", "relPath": "tsconfig.json"}, "/home/<USER>/workspace/vite.config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "vite.config.ts"}, "/home/<USER>/workspace/client/src/pages/not-found.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/not-found.tsx"}, "/home/<USER>/workspace/client/src/lib/queryClient.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/queryClient.ts"}, "/home/<USER>/workspace/client/src/lib/utils.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/utils.ts"}, "/home/<USER>/workspace/client/src/hooks/use-mobile.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-mobile.tsx"}, "/home/<USER>/workspace/client/src/hooks/use-toast.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/use-toast.ts"}, "/home/<USER>/workspace/client/src/components/ui/accordion.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/accordion.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert-dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert-dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/alert.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/alert.tsx"}, "/home/<USER>/workspace/client/src/components/ui/aspect-ratio.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/aspect-ratio.tsx"}, "/home/<USER>/workspace/client/src/components/ui/avatar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/avatar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/badge.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/badge.tsx"}, "/home/<USER>/workspace/client/src/components/ui/breadcrumb.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/breadcrumb.tsx"}, "/home/<USER>/workspace/client/src/components/ui/calendar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/calendar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/carousel.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/carousel.tsx"}, "/home/<USER>/workspace/client/src/components/ui/chart.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/chart.tsx"}, "/home/<USER>/workspace/client/src/components/ui/collapsible.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/collapsible.tsx"}, "/home/<USER>/workspace/client/src/components/ui/command.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/command.tsx"}, "/home/<USER>/workspace/client/src/components/ui/context-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/context-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dialog.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dialog.tsx"}, "/home/<USER>/workspace/client/src/components/ui/drawer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/drawer.tsx"}, "/home/<USER>/workspace/client/src/components/ui/dropdown-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/dropdown-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/form.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/form.tsx"}, "/home/<USER>/workspace/client/src/components/ui/hover-card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/hover-card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input-otp.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input-otp.tsx"}, "/home/<USER>/workspace/client/src/components/ui/input.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/input.tsx"}, "/home/<USER>/workspace/client/src/components/ui/label.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/label.tsx"}, "/home/<USER>/workspace/client/src/components/ui/menubar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/menubar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/navigation-menu.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/navigation-menu.tsx"}, "/home/<USER>/workspace/client/src/components/ui/pagination.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/pagination.tsx"}, "/home/<USER>/workspace/client/src/components/ui/popover.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/popover.tsx"}, "/home/<USER>/workspace/client/src/components/ui/progress.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/progress.tsx"}, "/home/<USER>/workspace/client/src/components/ui/radio-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/radio-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/resizable.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/resizable.tsx"}, "/home/<USER>/workspace/client/src/components/ui/scroll-area.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/scroll-area.tsx"}, "/home/<USER>/workspace/client/src/components/ui/select.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/select.tsx"}, "/home/<USER>/workspace/client/src/components/ui/separator.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/separator.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sheet.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sheet.tsx"}, "/home/<USER>/workspace/client/src/components/ui/sidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/sidebar.tsx"}, "/home/<USER>/workspace/client/src/components/ui/skeleton.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/skeleton.tsx"}, "/home/<USER>/workspace/client/src/components/ui/slider.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/slider.tsx"}, "/home/<USER>/workspace/client/src/components/ui/table.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/table.tsx"}, "/home/<USER>/workspace/client/src/components/ui/textarea.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/textarea.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toast.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toast.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toaster.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toaster.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle-group.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle-group.tsx"}, "/home/<USER>/workspace/client/src/components/ui/toggle.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/toggle.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tooltip.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tooltip.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.replit": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "/home/<USER>/workspace/supabase/migrations/20240101000000_create_study_documents_table.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20240101000000_create_study_documents_table.sql"}, "/home/<USER>/workspace/supabase/migrations/20240101000001_setup_study_materials_storage.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20240101000001_setup_study_materials_storage.sql"}, "/home/<USER>/workspace/supabase/migrations/20241201000000_add_select_all_question_type.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20241201000000_add_select_all_question_type.sql"}, "/home/<USER>/workspace/supabase/migrations/20250126000000_create_user_completions_table.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20250126000000_create_user_completions_table.sql"}, "/home/<USER>/workspace/supabase/migrations/YYYYMMDDHHMMSS_create_quiz_questions_table.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/YYYYMMDDHHMMSS_create_quiz_questions_table.sql"}, "/home/<USER>/workspace/supabase/migrations/YYYYMMDDHHMMSS_create_quizzes_table.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/YYYYMMDDHHMMSS_create_quizzes_table.sql"}, "/home/<USER>/workspace/supabase/functions/upload-study-document/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/functions/upload-study-document/index.ts"}, "/home/<USER>/workspace/supabase/functions/generate-quiz-questions/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/functions/generate-quiz-questions/index.ts"}, "/home/<USER>/workspace/supabase/functions/_shared/cors.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/functions/_shared/cors.ts"}, "/home/<USER>/workspace/shared/schema.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}, "/home/<USER>/workspace/shared/types.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/types.ts"}, "/home/<USER>/workspace/shared/types/completion.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/types/completion.ts"}, "/home/<USER>/workspace/shared/types/flashcards.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/types/flashcards.ts"}, "/home/<USER>/workspace/shared/types/quiz.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/types/quiz.ts"}, "/home/<USER>/workspace/shared/types/supabase.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/types/supabase.ts"}, "/home/<USER>/workspace/server/Dockerfile": {"rootPath": "/home/<USER>/workspace", "relPath": "server/Dockerfile"}, "/home/<USER>/workspace/server/Dockerfile.prod": {"rootPath": "/home/<USER>/workspace", "relPath": "server/Dockerfile.prod"}, "/home/<USER>/workspace/server/config.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/config.ts"}, "/home/<USER>/workspace/server/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "/home/<USER>/workspace/server/routes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "/home/<USER>/workspace/server/storage.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}, "/home/<USER>/workspace/server/vite.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "/home/<USER>/workspace/server/routes/aiRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/aiRoutes.ts"}, "/home/<USER>/workspace/server/routes/documentRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/documentRoutes.ts"}, "/home/<USER>/workspace/server/routes/flashcardDeckRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardDeckRoutes.ts"}, "/home/<USER>/workspace/server/routes/flashcardRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardRoutes.ts"}, "/home/<USER>/workspace/server/routes/flashcardSetRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardSetRoutes.ts"}, "/home/<USER>/workspace/server/routes/flashcards.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcards.ts"}, "/home/<USER>/workspace/server/routes/healthRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/healthRoutes.ts"}, "/home/<USER>/workspace/server/routes/quizExpressRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/quizExpressRoutes.ts"}, "/home/<USER>/workspace/server/routes/testRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/testRoutes.ts"}, "/home/<USER>/workspace/server/middleware/supabaseMiddleware.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/supabaseMiddleware.ts"}, "/home/<USER>/workspace/server/db/drizzle.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/db/drizzle.ts"}, "/home/<USER>/workspace/client/.env.example": {"rootPath": "/home/<USER>/workspace", "relPath": "client/.env.example"}, "/home/<USER>/workspace/client/Dockerfile": {"rootPath": "/home/<USER>/workspace", "relPath": "client/Dockerfile"}, "/home/<USER>/workspace/client/Dockerfile.prod": {"rootPath": "/home/<USER>/workspace", "relPath": "client/Dockerfile.prod"}, "/home/<USER>/workspace/client/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": "client/index.html"}, "/home/<USER>/workspace/client/nginx.conf": {"rootPath": "/home/<USER>/workspace", "relPath": "client/nginx.conf"}, "/home/<USER>/workspace/client/src/App.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}, "/home/<USER>/workspace/client/src/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}, "/home/<USER>/workspace/client/src/pages/AIConfig.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/AIConfig.tsx"}, "/home/<USER>/workspace/client/src/pages/Dashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Dashboard.tsx"}, "/home/<USER>/workspace/client/src/pages/DashboardPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/DashboardPage.tsx"}, "/home/<USER>/workspace/client/src/pages/FlashcardEditPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardEditPage.tsx"}, "/home/<USER>/workspace/client/src/pages/FlashcardReview.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardReview.tsx"}, "/home/<USER>/workspace/client/src/pages/QuizEditPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/QuizEditPage.tsx"}, "/home/<USER>/workspace/client/src/pages/QuizzesPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/QuizzesPage.tsx"}, "/home/<USER>/workspace/client/src/lib/ai-provider.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/ai-provider.ts"}, "/home/<USER>/workspace/client/src/lib/animations.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/animations.ts"}, "/home/<USER>/workspace/client/src/lib/dataSync.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/dataSync.ts"}, "/home/<USER>/workspace/client/src/components/ui/Spinner.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/Spinner.tsx"}, "/home/<USER>/workspace/client/src/components/ui/ThemeToggle.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/ThemeToggle.tsx"}, "/home/<USER>/workspace/client/src/components/ui/button.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/button.tsx"}, "/home/<USER>/workspace/client/src/components/ui/switch.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/switch.tsx"}, "/home/<USER>/workspace/client/src/components/ui/tabs.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/tabs.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/AiQuestionGenerator.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/AiQuestionGenerator.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/AiQuizGenerationOptions.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/AiQuizGenerationOptions.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuizQuestionManager.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizQuestionManager.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuizSettingsToggle.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizSettingsToggle.tsx"}, "/home/<USER>/workspace/client/src/components/layout/AnimatedRoute.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/AnimatedRoute.tsx"}, "/home/<USER>/workspace/client/src/components/layout/AppLayout.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/AppLayout.tsx"}, "/home/<USER>/workspace/client/src/components/layout/ErrorBoundary.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/ErrorBoundary.tsx"}, "/home/<USER>/workspace/client/src/components/layout/Header.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/Header.tsx"}, "/home/<USER>/workspace/client/src/components/layout/MobileNav.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/MobileNav.tsx"}, "/home/<USER>/workspace/client/src/components/layout/Sidebar.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/layout/Sidebar.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/AiFlashcardGenerator.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/AiFlashcardGenerator.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/CreateFlashcardSetForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/CreateFlashcardSetForm.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardEditManager.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardEditManager.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardForm.tsx"}, "/home/<USER>/workspace/client/src/components/auth/authStyles.module.css": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/auth/authStyles.module.css"}, "/home/<USER>/workspace/client/src/components/ai/AIConfigurationSection.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ai/AIConfigurationSection.tsx"}, "/home/<USER>/workspace/attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "attached_assets/Pasted-Skip-to-content-Files-Commands-Search-Packager-files-Config-files-6m-6-minutes-ago-1-po-1748845692114.txt"}, "/home/<USER>/workspace/.windsurf/rules/chat_rules.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".windsurf/rules/chat_rules.md"}, "/home/<USER>/workspace/.windsurf/rules/development_guidelines.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".windsurf/rules/development_guidelines.md"}, "/home/<USER>/workspace/.windsurf/rules/prd.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".windsurf/rules/prd.md"}, "/home/<USER>/workspace/.vscode/settings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".vscode/settings.json"}, "/home/<USER>/workspace/.upm/store.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/store.json"}, "/home/<USER>/workspace/.roo/mcp.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".roo/mcp.json"}, "/home/<USER>/workspace/.cursor/rules/chat_rules.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/chat_rules.mdc"}, "/home/<USER>/workspace/.cursor/rules/chewyai_development_patterns.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/chewyai_development_patterns.mdc"}, "/home/<USER>/workspace/.cursor/rules/coding_standards.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/coding_standards.mdc"}, "/home/<USER>/workspace/.cursor/rules/development_guidelines.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/development_guidelines.mdc"}, "/home/<USER>/workspace/.cursor/rules/prd.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/prd.mdc"}, "/home/<USER>/workspace/.cursor/rules/supabase_mcp_workflow.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/supabase_mcp_workflow.mdc"}, "/home/<USER>/workspace/.config/.vscode-server/.cli.848b80aeb52026648a8ff9f7c45a9b0a80641e2e.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/.cli.848b80aeb52026648a8ff9f7c45a9b0a80641e2e.log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/extensions.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/extensions.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.tr.json"}, "/home/<USER>/workspace/server/routes/quizRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/quizRoutes.ts"}, "/home/<USER>/workspace/client/src/main.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/main.tsx"}, "/home/<USER>/workspace/client/src/types/index.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/index.ts"}, "/home/<USER>/workspace/client/src/types/quiz-settings.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/quiz-settings.ts"}, "/home/<USER>/workspace/client/src/types/supabase.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/types/supabase.ts"}, "/home/<USER>/workspace/client/src/pages/DocumentViewPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/DocumentViewPage.tsx"}, "/home/<USER>/workspace/client/src/pages/FlashcardsPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/FlashcardsPage.tsx"}, "/home/<USER>/workspace/client/src/pages/QuizViewPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/QuizViewPage.tsx"}, "/home/<USER>/workspace/client/src/pages/TestPage.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/TestPage.tsx"}, "/home/<USER>/workspace/client/src/lib/api.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api.ts"}, "/home/<USER>/workspace/client/src/lib/docx-parser.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/docx-parser.ts"}, "/home/<USER>/workspace/client/src/lib/file-parser.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/file-parser.ts"}, "/home/<USER>/workspace/client/src/lib/notifications.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/notifications.ts"}, "/home/<USER>/workspace/client/src/lib/pdf-parser.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/pdf-parser.ts"}, "/home/<USER>/workspace/client/src/lib/quiz-settings.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/quiz-settings.ts"}, "/home/<USER>/workspace/client/src/lib/srs.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/srs.ts"}, "/home/<USER>/workspace/client/src/lib/storage.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/storage.ts"}, "/home/<USER>/workspace/client/src/lib/supabaseClient.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/supabaseClient.ts"}, "/home/<USER>/workspace/client/src/lib/api/quizApi.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api/quizApi.ts"}, "/home/<USER>/workspace/client/src/hooks/useAuth.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useAuth.tsx"}, "/home/<USER>/workspace/client/src/hooks/useDocuments.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useDocuments.tsx"}, "/home/<USER>/workspace/client/src/hooks/useKeyboardNavigation.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useKeyboardNavigation.ts"}, "/home/<USER>/workspace/client/src/hooks/useSRSStats.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useSRSStats.ts"}, "/home/<USER>/workspace/client/src/contexts/QuizSettingsContext.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/contexts/QuizSettingsContext.tsx"}, "/home/<USER>/workspace/client/src/contexts/ThemeContext.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/contexts/ThemeContext.tsx"}, "/home/<USER>/workspace/client/src/components/ui/LoadingStates.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/LoadingStates.tsx"}, "/home/<USER>/workspace/client/src/components/ui/card.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/card.tsx"}, "/home/<USER>/workspace/client/src/components/ui/checkbox.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/ui/checkbox.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/CreateQuizForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/CreateQuizForm.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/DashboardQuizGenerationPopup.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/DashboardQuizGenerationPopup.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuestionForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuestionForm.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuestionsList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuestionsList.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuizList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizList.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/QuizPlayer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/QuizPlayer.tsx"}, "/home/<USER>/workspace/client/src/components/quiz/SRSQuizMode.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/SRSQuizMode.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardGenerationPopup.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardGenerationPopup.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardItem.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardItem.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardManager.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardManager.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardReviewSection.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardReviewSection.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardSetList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardSetList.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardViewer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardViewer.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/FlashcardsList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardsList.tsx"}, "/home/<USER>/workspace/client/src/components/flashcards/QuizGenerationPopup.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/QuizGenerationPopup.tsx"}, "/home/<USER>/workspace/client/src/components/export/ExportSection.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/export/ExportSection.tsx"}, "/home/<USER>/workspace/client/src/components/document/DocumentList.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/DocumentList.tsx"}, "/home/<USER>/workspace/client/src/components/document/DocumentViewer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/DocumentViewer.tsx"}, "/home/<USER>/workspace/client/src/components/document/EnhancedDocumentUpload.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/EnhancedDocumentUpload.tsx"}, "/home/<USER>/workspace/client/src/components/document/InlineDocumentViewer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/InlineDocumentViewer.tsx"}, "/home/<USER>/workspace/client/src/components/document/MarkdownRenderer.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/MarkdownRenderer.tsx"}, "/home/<USER>/workspace/client/src/components/dashboard/DashboardOverview.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/DashboardOverview.tsx"}, "/home/<USER>/workspace/client/src/components/dashboard/SRSDashboard.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/SRSDashboard.tsx"}, "/home/<USER>/workspace/client/src/components/dashboard/StudySection.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/StudySection.tsx"}, "/home/<USER>/workspace/client/src/components/dashboard/UploadSection.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/UploadSection.tsx"}, "/home/<USER>/workspace/client/src/components/auth/SignInForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/auth/SignInForm.tsx"}, "/home/<USER>/workspace/client/src/components/auth/SignUpForm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/auth/SignUpForm.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/pr_webview.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/pr_webview.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/dark/pr_webview.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/resources/icons/dark/pr_webview.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/bing.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/bing.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/debug-icon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/debug-icon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-insiders.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-insiders.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-stable.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-stable.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/NOTICE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/NOTICE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/syntaxes/ref.tmGrammar.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/syntaxes/ref.tmGrammar.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.1596/assets/status/documentation.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.1596/assets/status/documentation.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.gitpod.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.gitpod.yml"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/NpmIntellisense.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/NpmIntellisense.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/PackageCompletionItem.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/PackageCompletionItem.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/State.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/State.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/command-import.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/command-import.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/config.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/config.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/fs-functions.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/fs-functions.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.github/workflows/main.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.github/workflows/main.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/provide.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/provide.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/shouldProvide.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/shouldProvide.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.github/workflows/publish.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/.github/workflows/publish.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.semgrep/settings.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.semgrep/settings.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.cache/replit/nix/dotreplitenv.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/dotreplitenv.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.cache/replit/modules/nodejs-20.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/nodejs-20.res"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.semgrep/semgrep.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.semgrep/semgrep.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.cache/replit/modules/replit.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit.res"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.cache/replit/env/latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/lru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/lru.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.cache/replit/modules/web.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/web.res"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/output_logging_20250602T162434/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/output_logging_20250602T162434/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/machineid": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/machineid"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/.vscode/launch.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/.vscode/launch.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/util.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/christian-kohler.npm-intellisense-1.4.5/out/src/util.js"}, "/home/<USER>/workspace/.cache/replit/toolchain.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/toolchain.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/deviceid": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/deviceid"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.vscode-pull-request-github-0.110.0/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.cache/replit/modules.stamp": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules.stamp"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.cache/replit/env/latest": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/output_logging_20250602T162434/2-HTML Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/output_logging_20250602T162434/2-HTML Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/50c0f256-2dfd-464e-ac21-b1563c2ec04d": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/50c0f256-2dfd-464e-ac21-b1563c2ec04d"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bd8bdf8b-0771-415b-94cf-5fc4407f66c8": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/bd8bdf8b-0771-415b-94cf-5fc4407f66c8"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/Chewy42/ChewyAI.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.vscode-pull-request-github/assignableUsers/Chewy42/ChewyAI.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilot-debug": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilot-debug"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilotDebugCommand.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/debugCommand/copilotDebugCommand.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/91aS.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/91aS.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c0bf1c4d-bc9c-4c33-8751-8a97bb77639b": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/c0bf1c4d-bc9c-4c33-8751-8a97bb77639b"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e0bfff4a-189e-485d-aa5c-27ae98da7861": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/e0bfff4a-189e-485d-aa5c-27ae98da7861"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/IPsr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/IPsr.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/FuIS.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/FuIS.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/KEpF.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/KEpF.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/github.copilot-chat/commandEmbeddings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/github.copilot-chat/commandEmbeddings.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/43SW.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/43SW.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-71d52f56/22ID.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-71d52f56/22ID.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-71d52f56/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-71d52f56/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-7bf218ba-5306-4b00-bced-189d9e7b9554.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-7bf218ba-5306-4b00-bced-189d9e7b9554.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/23a5d850/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/23a5d850/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-0-e806dc9b-b794-4139-9a8f-de090657434f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-0-e806dc9b-b794-4139-9a8f-de090657434f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-1748882825799-9dfff320-ca4e-40e5-96d3-77b49122de4c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-1748882825799-9dfff320-ca4e-40e5-96d3-77b49122de4c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/23a5d850/Vwuz.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/23a5d850/Vwuz.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e57ae2c/2sxP.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e57ae2c/2sxP.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/1e57ae2c/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/1e57ae2c/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/72e7e008/BuaO.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/72e7e008/BuaO.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/72e7e008/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/72e7e008/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-528f3925/3DvH.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-528f3925/3DvH.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-528f3925/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-528f3925/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-781b0da3/hwZd": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-781b0da3/hwZd"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-781b0da3/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-781b0da3/entries.json"}, "/home/<USER>/workspace/docs/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-781b0da3/1fi5": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-781b0da3/1fi5"}, "/home/<USER>/workspace/docs/RULES.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/RULES.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-781b0da3/b6oK": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-781b0da3/b6oK"}, "/home/<USER>/workspace/docs/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.markdown-language-features/Markdown.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T162433/exthost1/vscode.markdown-language-features/Markdown.log"}, "/home/<USER>/workspace/docs/DEPLOYMENT.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/DEPLOYMENT.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1c30e72/O7nF.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1c30e72/O7nF.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1c30e72/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1c30e72/entries.json"}, "/home/<USER>/workspace/docs/MEMORIES.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/MEMORIES.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-1c30e72/bORo.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-1c30e72/bORo.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/b6B8.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/b6B8.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-b447d83/s3q4.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-b447d83/s3q4.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-b447d83/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-b447d83/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/9OZp.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/9OZp.example"}, "/home/<USER>/workspace/docs/API.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/API.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7812e3c7/XzjF": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7812e3c7/XzjF"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7812e3c7/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7812e3c7/entries.json"}, "/home/<USER>/workspace/docs/PRODUCTION_SETUP_SUMMARY.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/PRODUCTION_SETUP_SUMMARY.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7812e3c7/qfY6": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7812e3c7/qfY6"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-b447d83/9dIG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-b447d83/9dIG.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-71d52f56/17gO.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-71d52f56/17gO.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/sNUq.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/sNUq.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/entries.json"}, "/home/<USER>/workspace/server/middleware/apiKeyStorage.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/apiKeyStorage.ts"}, "/home/<USER>/workspace/supabase/migrations/20250602_create_user_ai_credentials.sql": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20250602_create_user_ai_credentials.sql"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/63f295fb/jTS7.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/63f295fb/jTS7.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/63f295fb/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/63f295fb/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/zWxM.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/zWxM.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/2uPF.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/2uPF.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/178884fc/oweG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/178884fc/oweG.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/178884fc/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/178884fc/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/178884fc/FAhs.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/178884fc/FAhs.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/178884fc/ZIQ1.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/178884fc/ZIQ1.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-b447d83/MZoq.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-b447d83/MZoq.md"}, "/home/<USER>/workspace/server/routes/credentialsRoutes.ts": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/credentialsRoutes.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6f08eac4/gbSo.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6f08eac4/gbSo.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6f08eac4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6f08eac4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-6f08eac4/8z7h.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-6f08eac4/8z7h.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6bf29b4/OIya.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6bf29b4/OIya.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6bf29b4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6bf29b4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-17b907e3/u82I.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-17b907e3/u82I.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/qPYA.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/qPYA.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/Spn3.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/Spn3.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/MtA6.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/MtA6.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/Gg0p.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/Gg0p.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/fiAD.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/fiAD.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47b0ae77/zo8G.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47b0ae77/zo8G.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47b0ae77/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47b0ae77/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/47b0ae77/KSws.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/47b0ae77/KSws.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/650f2066/y1BG.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/650f2066/y1BG.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/650f2066/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/650f2066/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/650f2066/GiXT.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/650f2066/GiXT.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2f04ca25/73g1.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2f04ca25/73g1.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2f04ca25/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2f04ca25/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-2f04ca25/1DB1.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-2f04ca25/1DB1.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2efe5a43/QxoP.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2efe5a43/QxoP.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2efe5a43/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2efe5a43/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/2efe5a43/QgON.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/2efe5a43/QgON.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/RCj5.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/RCj5.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-151cc23a/kmh8.example": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-151cc23a/kmh8.example"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/DyMf.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/DyMf.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/KH4h.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/KH4h.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/178884fc/zo3a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/178884fc/zo3a.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/B1vv.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/B1vv.ts"}, "/home/<USER>/workspace/.cache/typescript/5.8/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/5t4B.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/5t4B.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/13e9b580/0Sx0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/13e9b580/0Sx0.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/13e9b580/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/13e9b580/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/facbcc2a-0b52-468d-8828-7d739eccb3d3": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/facbcc2a-0b52-468d-8828-7d739eccb3d3"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d4a881/DPmE.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d4a881/DPmE.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d4a881/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d4a881/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-46e7634b-5e73-4fbd-b798-2b0e1f22e474.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-46e7634b-5e73-4fbd-b798-2b0e1f22e474.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-0-25066e6c-1b2e-49e1-97fc-572b02542ff0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-0-25066e6c-1b2e-49e1-97fc-572b02542ff0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-1748885380081-99559d90-117f-46f0-8707-e0a906c7c9ed.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-1748885380081-99559d90-117f-46f0-8707-e0a906c7c9ed.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/Kp3y.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/Kp3y.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-0-b8ea2ee4-829f-4af5-9e9e-3baa1e572802.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-0-b8ea2ee4-829f-4af5-9e9e-3baa1e572802.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885399461-36b48003-b066-43cc-a00a-dc29416ade8c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885399461-36b48003-b066-43cc-a00a-dc29416ade8c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/FaGt.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/FaGt.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885409826-c331b6d6-296a-4d2b-8d64-10759b37553f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885409826-c331b6d6-296a-4d2b-8d64-10759b37553f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/z8aX.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/z8aX.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885424238-0cec4bae-ca48-4111-bf10-2dbfdc15a98a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885424238-0cec4bae-ca48-4111-bf10-2dbfdc15a98a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/Z0hY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/Z0hY.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885445110-9165b4a0-90e1-408f-a67d-99c9e34b2ab8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885445110-9165b4a0-90e1-408f-a67d-99c9e34b2ab8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/0pPd.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/0pPd.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885470366-fc48c18d-f506-4eb8-a11f-999724ad7510.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885470366-fc48c18d-f506-4eb8-a11f-999724ad7510.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/Rr7h.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/Rr7h.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885482591-b42f7949-7d6f-4c4e-b855-78b772f6a449.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885482591-b42f7949-7d6f-4c4e-b855-78b772f6a449.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4e6709b4/VFG5.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4e6709b4/VFG5.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885494165-7b6bc484-c736-4ce8-86cc-badf9c80ffd4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748885494165-7b6bc484-c736-4ce8-86cc-badf9c80ffd4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ec837b3/4ERU.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ec837b3/4ERU.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ec837b3/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ec837b3/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-0-3999f935-b083-4c5f-8ec2-70311ae4d6e3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-0-3999f935-b083-4c5f-8ec2-70311ae4d6e3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885516033-e62ba566-26a7-4846-ad2b-865a740199f7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885516033-e62ba566-26a7-4846-ad2b-865a740199f7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ec837b3/8OGH.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ec837b3/8OGH.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885954974-b9eca9ef-c665-4a01-aeaf-79be030c0ff7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885954974-b9eca9ef-c665-4a01-aeaf-79be030c0ff7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ec837b3/HNPU.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ec837b3/HNPU.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885973712-d9ce549c-a8cd-476b-b7f2-07ff4dfa1234.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885973712-d9ce549c-a8cd-476b-b7f2-07ff4dfa1234.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4ec837b3/otBH.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4ec837b3/otBH.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885995219-39ca958d-a396-46f0-b0d4-af7881109f98.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748885995219-39ca958d-a396-46f0-b0d4-af7881109f98.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51ae1c15/DFih.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51ae1c15/DFih.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51ae1c15/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51ae1c15/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-0-69202b78-522b-4f5b-9dae-34a4db77f72f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-0-69202b78-522b-4f5b-9dae-34a4db77f72f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886125453-3d0eabc0-e73d-4f24-8206-f879d7dfd340.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886125453-3d0eabc0-e73d-4f24-8206-f879d7dfd340.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51ae1c15/B8Ar.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51ae1c15/B8Ar.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886142277-f7c794ed-16d6-4561-9aa0-a4b1b9478192.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886142277-f7c794ed-16d6-4561-9aa0-a4b1b9478192.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/output_logging_20250602T174530/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/output_logging_20250602T174530/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886220888-8de5d0e6-8473-4d7f-ad23-4800aef42956.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886220888-8de5d0e6-8473-4d7f-ad23-4800aef42956.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7e3bb95/eSEm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7e3bb95/eSEm.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886238456-5b60f471-e47c-4576-84ba-8f51b6d7147b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886238456-5b60f471-e47c-4576-84ba-8f51b6d7147b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7e3bb95/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7e3bb95/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/activitybar.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/activitybar.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/panel-icon-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/panel-icon-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/panel-icon-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/panel-icon-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-gray-hook.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-gray-hook.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-gray-line.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-gray-line.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-inactive-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-inactive-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-inactive-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-inactive-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/bg-next-edit-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/left-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-addition-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-available-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-available-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-available-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-available-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-change-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-deletion-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-rejected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-rejected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-rejected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-rejected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-unavailable-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-unavailable-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-unavailable-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-unavailable-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-complete-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-complete-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-complete-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-complete-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-disabled-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-disabled-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-disabled-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-disabled-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/nextedit-update-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/next-edit/right-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/light/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json"}, "/home/<USER>/workspace/.cache/typescript/5.8/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/typescript/5.8/package-lock.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7e3bb95/E5b0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7e3bb95/E5b0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VirtualizedMessageList-BJn8K74l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VirtualizedMessageList-BJn8K74l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/_basePickBy-DiH5splf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/_basePickBy-DiH5splf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/_baseUniq-BZgk2Caz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/_baseUniq-BZgk2Caz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/abap-BrlRCFwh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/abap-BrlRCFwh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/abap-CRCWOmpq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/abap-CRCWOmpq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/apex-BE2Kqs_0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/apex-BE2Kqs_0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/apex-DFVco9Dq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/apex-DFVco9Dq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/arc-B_nq00OU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/arc-B_nq00OU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/arrow-up-right-from-square-CuUnyQRL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/arrow-up-right-from-square-CuUnyQRL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/augment-logo-D_UKSkj8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/augment-logo-D_UKSkj8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-CX5hiNi4.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-CX5hiNi4.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-state-d-ymFdyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-state-d-ymFdyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/azcli-1IWB1ccx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/azcli-1IWB1ccx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/azcli-CBeeoD2V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/azcli-CBeeoD2V.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bat-CtWuqYvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bat-CtWuqYvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bat-DPkNLes8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bat-DPkNLes8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/media/keyboard/dark/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/autofix.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/autofix.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/diff-view.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/diff-view.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/history.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/history.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/main-panel.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/main-panel.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/memories.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/memories.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/next-edit-suggestions.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/next-edit-suggestions.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/preference.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/preference.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/remote-agent-diff.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/remote-agent-diff.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/remote-agent-home.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/remote-agent-home.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/rules.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/rules.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/settings.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/AugmentMessage-LUamDQhY.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/AugmentMessage-LUamDQhY.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/BaseButton-C6Dhmpxa.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/BaseButton-C6Dhmpxa.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/BaseButton-DvMdfQ3F.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/BaseButton-DvMdfQ3F.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ButtonAugment-HnJOGilM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ButtonAugment-HnJOGilM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/CardAugment-BAo8Ti0V.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/CardAugment-BAo8Ti0V.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/CardAugment-BxTO-shY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/CardAugment-BxTO-shY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Content-Czt02SJi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Content-Czt02SJi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Content-D0WttAzY.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Content-D0WttAzY.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Drawer-CihA8FWD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Drawer-CihA8FWD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Drawer-DwFbLE28.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Drawer-DwFbLE28.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Filespan-BC4kxbfx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Filespan-BC4kxbfx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Filespan-tclW2Ian.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Filespan-tclW2Ian.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconButtonAugment-BTu-iglL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconButtonAugment-BTu-iglL.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconButtonAugment-Certjadv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconButtonAugment-Certjadv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconFilePath-C-3qORpY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconFilePath-C-3qORpY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconFilePath-CiKel2Kp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/IconFilePath-CiKel2Kp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Keybindings-CFCfDdvf.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Keybindings-CFCfDdvf.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/LanguageIcon-BH9BM7T7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/LanguageIcon-BH9BM7T7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/LanguageIcon-D78BqCXT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/LanguageIcon-D78BqCXT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MarkdownEditor-DWj1HgDp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MarkdownEditor-DWj1HgDp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MaterialIcon-DIlB9c-0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MaterialIcon-DIlB9c-0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MessageList-BCW_PhFd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MessageList-BCW_PhFd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MessageList-DRTeF5X0.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/MessageList-DRTeF5X0.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/OpenFileButton-DWw6TNYm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/OpenFileButton-DWw6TNYm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentRetry-CgKZWHFz.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentRetry-CoFGWWoM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentRetry-CoFGWWoM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentSetup-BbgdHLVf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentSetup-BbgdHLVf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RulesDropdown-XGjfPruR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RulesDropdown-XGjfPruR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/StatusIndicator-BRrHlRmq.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/StatusIndicator-BRrHlRmq.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/StatusIndicator-CAJYwjQb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/StatusIndicator-CAJYwjQb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextAreaAugment-Cj5jK817.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextAreaAugment-Cj5jK817.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextTooltipAugment-Bkzart3o.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextTooltipAugment-Bkzart3o.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flow9-DFOiqFq1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flow9-DFOiqFq1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/architectureDiagram-UYN6MBPD-CeM6jcgs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/architectureDiagram-UYN6MBPD-CeM6jcgs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-Dd5rMFOC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/autofix-Dd5rMFOC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/await_block-CvQ_3xaW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/await_block-CvQ_3xaW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bicep-BZbtZWRn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bicep-BZbtZWRn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bicep-C6yweCii.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/bicep-C6yweCii.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/blockDiagram-ZHA2E4KO-B0bt2Wzv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/blockDiagram-ZHA2E4KO-B0bt2Wzv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/c4Diagram-6F5ED5ID-Dq-bVffB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/c4Diagram-6F5ED5ID-Dq-bVffB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cameligo-CGrWLZr3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cameligo-CGrWLZr3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cameligo-hfF0gFWA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cameligo-hfF0gFWA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/channel-C8geQ-6M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/channel-C8geQ-6M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chat-flags-model-IiDhbRsI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chat-flags-model-IiDhbRsI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chat-types-NgqNgjwU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chat-types-NgqNgjwU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chevron-down-B88L5wkj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chevron-down-B88L5wkj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-5HRBRIJM-Mrrbxno1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-5HRBRIJM-Mrrbxno1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-7U56Z5CX-Bl10bKCx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-7U56Z5CX-Bl10bKCx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-ASOPGD6M-B2i-rdzk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-ASOPGD6M-B2i-rdzk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-KFBOBJHC-DrjJz5wB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-KFBOBJHC-DrjJz5wB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-T2TOU4HS-D0R-fmOu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-T2TOU4HS-D0R-fmOu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-TMUBEWPD-BTz4HvC5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/chunk-TMUBEWPD-BTz4HvC5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/classDiagram-LNE6IOMH-Dh8vqs6T.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/classDiagram-LNE6IOMH-Dh8vqs6T.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-Dh8vqs6T.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-Dh8vqs6T.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clojure-BhAVYYK7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clojure-BhAVYYK7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clojure-D9WOWImG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clojure-D9WOWImG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clone-BkzPDDdR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/clone-BkzPDDdR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/coffee-B7EJu28W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/coffee-B7EJu28W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/coffee-D3gVwdtb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/coffee-D3gVwdtb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cpp-B6k-yq-r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cpp-B6k-yq-r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cpp-DghbrAFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cpp-DghbrAFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csharp-BoL64M5l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csharp-BoL64M5l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csp-C46ZqvIl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csp-C46ZqvIl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Keybindings-4L2d2tRE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/Keybindings-4L2d2tRE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/NextEditSuggestions-Dep7yNEf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/NextEditSuggestions-Dep7yNEf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/RemoteAgentSetup-B1m5qM5w.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/SpinnerAugment-BJ4-L7QR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/SpinnerAugment-BJ4-L7QR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/TextTooltipAugment-BIMZ5dVo.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VSCodeCodicon-CvBJfpPi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VSCodeCodicon-CvBJfpPi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/elixir-BRjLKONM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/elixir-BRjLKONM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/exclamation-triangle-Dn4fXX3v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/exclamation-triangle-Dn4fXX3v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-reader-B7W_DzJn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-reader-B7W_DzJn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-reader-ChKpCF92.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-reader-ChKpCF92.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flow9-Cac8vKd7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flow9-Cac8vKd7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flowDiagram-7ASYPVHJ-Cn0aUQ5j.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/flowDiagram-7ASYPVHJ-Cn0aUQ5j.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/freemarker2-DoNuTueB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/freemarker2-DoNuTueB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/fsharp-BpBzFqoi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/fsharp-BpBzFqoi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ganttDiagram-NTVNEXSI-CPMZUFzs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ganttDiagram-NTVNEXSI-CPMZUFzs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/html-Dv6uDOCE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/html-Dv6uDOCE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/html-gnlaprsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/html-gnlaprsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/htmlMode-BuVpxTb-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/htmlMode-BuVpxTb-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/java-DBwYS35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/java-DBwYS35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kotlin-CUUhw8ZM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kotlin-CUUhw8ZM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lexon-Canl7DCW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lexon-Canl7DCW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/objective-c-BdAIHrxl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/objective-c-BdAIHrxl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascal-DVjYFmSU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascal-DVjYFmSU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csharp-1bC6NAu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csharp-1bC6NAu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csp-ZI2qu8Le.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/csp-ZI2qu8Le.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/css-BkD51DMU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/css-BkD51DMU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/css-DQU6DXDx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/css-DQU6DXDx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cssMode-BBxcXVId.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cssMode-BBxcXVId.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cssMode-Cf0wo1J6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cssMode-Cf0wo1J6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cypher-D84EuPTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cypher-D84EuPTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cypher-DQ3GyGCv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cypher-DQ3GyGCv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dagre-4EVJKHTY-CI5-bT7Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dagre-4EVJKHTY-CI5-bT7Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dart-CQal6Qht.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dart-CQal6Qht.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dart-D8lhlL1r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dart-D8lhlL1r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-CRmW_T8r.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-CRmW_T8r.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-DA68MSAy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-DA68MSAy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-DKcAect8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/design-system-init-DKcAect8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diagram-QW4FP2JN-z4f6t8jP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diagram-QW4FP2JN-z4f6t8jP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-utils-CiAPKcVt.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-utils-CiAPKcVt.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-utils-y96qaWKK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-utils-y96qaWKK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-view-C1U5Q3f_.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-view-C1U5Q3f_.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dockerfile-CuMHdPl5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dockerfile-CuMHdPl5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dockerfile-DLk6rpji.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/dockerfile-DLk6rpji.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ecl-BO6FnfXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ecl-BO6FnfXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ecl-DrG4DZS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ecl-DrG4DZS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/elixir-nOQiPlLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/elixir-nOQiPlLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ellipsis-BWy9xWah.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ellipsis-BWy9xWah.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/erDiagram-6RL3IURR-C7DdJr44.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/erDiagram-6RL3IURR-C7DdJr44.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/expand--BB_Hn_b.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/expand--BB_Hn_b.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-paths-BcSg4gks.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/file-paths-BcSg4gks.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-BJI1Q8_7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-BJI1Q8_7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-CiVHUelA.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-CiVHUelA.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-opened-DzrGzNBt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/folder-opened-DzrGzNBt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/freemarker2-CG76HvIH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/freemarker2-CG76HvIH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/fsharp-fd1GTHhf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/fsharp-fd1GTHhf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/github-C1PQK5DH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/github-C1PQK5DH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/github-DDCjb6F1.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/github-DDCjb6F1.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/globals-D0QH3NT1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/globals-D0QH3NT1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/handlebars-BJSeNQ27.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/handlebars-BJSeNQ27.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/handlebars-CNQw3EXp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/handlebars-CNQw3EXp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/hcl-CVzGlmMO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/hcl-CVzGlmMO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/hcl-DxDQ3s82.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/hcl-DxDQ3s82.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/history-CJM98PYL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/history-CJM98PYL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/history-DLzhBtGZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/history-DLzhBtGZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/htmlMode-CIRhgpF_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/htmlMode-CIRhgpF_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-9HWdRmiB.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-9HWdRmiB.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-C-g0ZorP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-C-g0ZorP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-CGbmuyBX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-CGbmuyBX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-McRKs1sU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-McRKs1sU.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-eY12-hdZ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-eY12-hdZ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/infoDiagram-A4XQUW5V-DXAfsszZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/infoDiagram-A4XQUW5V-DXAfsszZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ini-BvajGCUy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ini-BvajGCUy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ini-COn9E3gi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ini-COn9E3gi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/init-g68aIKmP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/init-g68aIKmP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/isObjectLike-DflaizF0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/isObjectLike-DflaizF0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/java-SYsfObOQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/java-SYsfObOQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/javascript-BM_VUh7x.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/javascript-BM_VUh7x.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/javascript-BrEubtUq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/javascript-BrEubtUq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/jsonMode-B6_b0_sN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/jsonMode-B6_b0_sN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/jsonMode-Dek7wPyh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/jsonMode-Dek7wPyh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/julia-DQXNmw_w.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/julia-DQXNmw_w.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kanban-definition-QRCXZQQD-C7ysreIX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kanban-definition-QRCXZQQD-C7ysreIX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/less-CW-yd8b8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/less-CW-yd8b8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/less-GGFNNJHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/less-GGFNNJHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lexon-DwtVlf1I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lexon-DwtVlf1I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/linear-Bbnt6wzV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/linear-Bbnt6wzV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/liquid-CPIgs5dT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/liquid-CPIgs5dT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/liquid-DOEm5dbE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/liquid-DOEm5dbE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lodash-ChYFUhWY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lodash-ChYFUhWY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lua-BdjVVLHC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lua-BdjVVLHC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lua-D28Ae8-K.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/lua-D28Ae8-K.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/m3-B3V054Zg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/m3-B3V054Zg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/m3-Bu4mmWhs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/m3-Bu4mmWhs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/magnifying-glass-LWYs47rB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/magnifying-glass-LWYs47rB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/main-panel-B54j0lBe.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/main-panel-B54j0lBe.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/markdown-9NNSJ0ww.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/markdown-9NNSJ0ww.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/markdown-B811l8j2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/markdown-B811l8j2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mcp-logo-B9nTLE-q.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mcp-logo-B9nTLE-q.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mdx-BaH_mmJD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mdx-BaH_mmJD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mdx-DuAILtAS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mdx-DuAILtAS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/memories-CMFr_HGh.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/memories-CMFr_HGh.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/memories-DhI6zWWP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/memories-DhI6zWWP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/open-in-new-window-DMlqLwqy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/open-in-new-window-DMlqLwqy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ordinal-_rw2EY4v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ordinal-_rw2EY4v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascal-BhNW15KB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascal-BhNW15KB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascaligo-5jv8CcQD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascaligo-5jv8CcQD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascaligo-LOm9cWIk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pascaligo-LOm9cWIk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pen-to-square-Bm4lF9Yl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pen-to-square-Bm4lF9Yl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pen-to-square-Dvw-pMXw.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pen-to-square-Dvw-pMXw.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pgsql-Dy0bjov7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pgsql-Dy0bjov7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/php-120yhfDK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/php-120yhfDK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pieDiagram-YF2LJOPJ-Cj3K28_W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pieDiagram-YF2LJOPJ-Cj3K28_W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pla-B-trYkKT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pla-B-trYkKT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pla-CjnFlu4u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pla-CjnFlu4u.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/postiats-CQpG440k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/postiats-CQpG440k.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/postiats-ToQhlN1R.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/postiats-ToQhlN1R.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powerquery-BLkMU_zt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powerquery-BLkMU_zt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powerquery-DdJtto1Z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powerquery-DdJtto1Z.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powershell-Bu_VLpJB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powershell-Bu_VLpJB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powershell-Cz-ePiwW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/powershell-Cz-ePiwW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preload-helper-Dv6uf1Os.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preload-helper-Dv6uf1Os.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/python-CZ67Wo4I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/python-CZ67Wo4I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/qsharp-YKUDF0Oj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/qsharp-YKUDF0Oj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/qsharp-q7JyzKFN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/qsharp-q7JyzKFN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-C9m8xMAX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-C9m8xMAX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/r-BIFz-_sK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/r-BIFz-_sK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/r-DShZCeRJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/r-DShZCeRJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/razor-BxlDHIuM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/razor-BxlDHIuM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/razor-iCuOooJL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/razor-iCuOooJL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redis-DJMpkPfA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redis-DJMpkPfA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redshift-6xAzNskS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redshift-6xAzNskS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scheme-Ecrf_Zyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scheme-Ecrf_Zyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-view-6vqytIG7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/diff-view-6vqytIG7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/gitGraph-YCYPL57B-Di_2i6IF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/gitGraph-YCYPL57B-Di_2i6IF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-CXzQHM_O.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-CXzQHM_O.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/go-CHYgS3dC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/go-CHYgS3dC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/go-O9LJTZXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/go-O9LJTZXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graph-Ds8GaKdA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graph-Ds8GaKdA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graphql-LQdxqEYJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graphql-LQdxqEYJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graphql-csByOneL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/graphql-csByOneL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-yERhhNs7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/index-yERhhNs7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/journeyDiagram-G5WM74LC-COk3iWeJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/journeyDiagram-G5WM74LC-COk3iWeJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/katex-BAVf198l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/katex-BAVf198l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/keypress-DD1aQVr0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/keypress-DD1aQVr0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kotlin-qQ0MG-9I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/kotlin-qQ0MG-9I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layer-group-CZFSGU8L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layer-group-CZFSGU8L.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layer-group-Dnu6blpM.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layer-group-Dnu6blpM.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layout-rofJ78-X.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/layout-rofJ78-X.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mindmap-definition-GWI6TPTV-DfjtxT5Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mindmap-definition-GWI6TPTV-DfjtxT5Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mips-CdjsipkG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mips-CdjsipkG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sophia-RYC1BQQz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sophia-RYC1BQQz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sophia-dWwzI90F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sophia-dWwzI90F.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sparql-KEyrF7De.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sparql-KEyrF7De.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sql-BV61QDTH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sql-BV61QDTH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sql-BdTr02Mf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sql-BdTr02Mf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/st-BZ7aq21L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/st-BZ7aq21L.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/st-C7iG7M4S.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/st-C7iG7M4S.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/stateDiagram-MAYHULR4-BgZ2KuBj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/stateDiagram-MAYHULR4-BgZ2KuBj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-BI0LvJjy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-BI0LvJjy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/swift-D7IUmUK8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/swift-D7IUmUK8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/swift-DqwpnxQL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/swift-DqwpnxQL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/systemverilog-CeZ7LPTL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/systemverilog-CeZ7LPTL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/systemverilog-DgMryOEJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/systemverilog-DgMryOEJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tcl-Bl2hYPt-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tcl-Bl2hYPt-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/terminal-BQIj5vJ0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/terminal-BQIj5vJ0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-LfaCSdmF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-LfaCSdmF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xml-C-C41Cin.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xml-C-C41Cin.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-0-82ccea30-148d-47e9-8999-c6e4997603db.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-0-82ccea30-148d-47e9-8999-c6e4997603db.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-1748884824445-f26ae645-5cb3-4a1e-a5d2-51af3cc81f57.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-1748884824445-f26ae645-5cb3-4a1e-a5d2-51af3cc81f57.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-1748884837482-49b5b531-cda7-44c1-9bf8-60596b1c8306.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_quiz_DashboardQuizGenerationPopup.tsx-1748884837482-49b5b531-cda7-44c1-9bf8-60596b1c8306.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-0-04bc879a-2aed-4410-9800-9d66f93a78fe.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-0-04bc879a-2aed-4410-9800-9d66f93a78fe.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883441109-4028eb07-10bf-4e64-993b-13dac950ca03.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883441109-4028eb07-10bf-4e64-993b-13dac950ca03.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883508491-c3da29fd-0e1f-4a26-a53f-de1ac4702b80.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883508491-c3da29fd-0e1f-4a26-a53f-de1ac4702b80.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883528741-0a96c602-1a28-479e-9fd4-1c12e5260f7f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748883528741-0a96c602-1a28-479e-9fd4-1c12e5260f7f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_supabaseClient.ts-0-b4069707-e0cd-48c0-a0af-1513c168ae0c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_supabaseClient.ts-0-b4069707-e0cd-48c0-a0af-1513c168ae0c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_supabaseClient.ts-1748882842181-3e2b6a68-6840-4c87-a3d8-2d2100d370aa.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_lib_supabaseClient.ts-1748882842181-3e2b6a68-6840-4c87-a3d8-2d2100d370aa.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_API.md-1748883088555-4bab5a7e-e712-422f-8290-f94bba478b08.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_API.md-1748883088555-4bab5a7e-e712-422f-8290-f94bba478b08.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_API.md-1748883713713-a21fd260-fb05-453a-99dd-98fa709a5667.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_API.md-1748883713713-a21fd260-fb05-453a-99dd-98fa709a5667.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_DEPLOYMENT.md-1748882988988-8fea6914-7745-4b79-b376-b86c9eaca474.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_DEPLOYMENT.md-1748882988988-8fea6914-7745-4b79-b376-b86c9eaca474.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_MEMORIES.md-1748883016260-8d635f12-0eee-482b-a0e8-ebda3d1e2925.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_MEMORIES.md-1748883016260-8d635f12-0eee-482b-a0e8-ebda3d1e2925.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_MEMORIES.md-1748883622476-8d3b344d-ba89-4764-bf5c-14fd6a17b11a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_MEMORIES.md-1748883622476-8d3b344d-ba89-4764-bf5c-14fd6a17b11a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_PRODUCTION_SETUP_SUMMARY.md-1748883156929-74554625-9f4a-4133-a07b-3550c01b57b9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_PRODUCTION_SETUP_SUMMARY.md-1748883156929-74554625-9f4a-4133-a07b-3550c01b57b9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_RULES.md-1748882942617-5fc35e27-0f79-4743-95b1-a4dd3630ae99.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_RULES.md-1748882942617-5fc35e27-0f79-4743-95b1-a4dd3630ae99.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748882964433-3fddee2e-6ef6-4c81-b5e1-4cd4a52f0a2b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748882964433-3fddee2e-6ef6-4c81-b5e1-4cd4a52f0a2b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883561036-7963d59d-6fa1-4879-ac00-15a86c47bcf9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883561036-7963d59d-6fa1-4879-ac00-15a86c47bcf9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883576435-34c76bc2-1527-4611-86fe-f19135a0a0cc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883576435-34c76bc2-1527-4611-86fe-f19135a0a0cc.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883605028-56a00c9b-bed2-46da-bbc8-98209466d19b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748883605028-56a00c9b-bed2-46da-bbc8-98209466d19b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748884954474-23eec07c-4f6a-42e4-bdb8-e6c78eeff2c3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_SECURITY.md-1748884954474-23eec07c-4f6a-42e4-bdb8-e6c78eeff2c3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_package.json-0-156e37f1-624f-459b-8f66-8981c503821f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_package.json-0-156e37f1-624f-459b-8f66-8981c503821f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_package.json-1748882894506-a2a331be-9fba-4421-b73f-821f46073063.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_package.json-1748882894506-a2a331be-9fba-4421-b73f-821f46073063.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-1748883428028-b9ac8056-c563-4972-a5c9-74aa715146e3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_config.ts-1748883428028-b9ac8056-c563-4972-a5c9-74aa715146e3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-0-5ccd7c82-4dff-4295-889c-38c4b0e125a3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-0-5ccd7c82-4dff-4295-889c-38c4b0e125a3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-1748883682704-db3a6c9f-3e71-4b20-ab25-4cefbee3ca41.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-1748883682704-db3a6c9f-3e71-4b20-ab25-4cefbee3ca41.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-1748883695209-beb4773b-2f3a-43e6-ab88-9271fe418654.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_index.ts-1748883695209-beb4773b-2f3a-43e6-ab88-9271fe418654.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748883467508-7689f7a5-d070-44f3-8233-6b8194a03db4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748883467508-7689f7a5-d070-44f3-8233-6b8194a03db4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_supabaseMiddleware.ts-0-2443955f-d70b-4587-bafc-9d6059baf1a1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_supabaseMiddleware.ts-0-2443955f-d70b-4587-bafc-9d6059baf1a1.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_supabaseMiddleware.ts-1748883493247-c3a6d94f-102f-416f-a758-cf929c18d5b7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_middleware_supabaseMiddleware.ts-1748883493247-c3a6d94f-102f-416f-a758-cf929c18d5b7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_vite.ts-1748882879360-5b09aeee-cf60-4dc5-b59a-38facf17da96.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_vite.ts-1748882879360-5b09aeee-cf60-4dc5-b59a-38facf17da96.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_supabase_migrations_20250602_create_user_ai_credentials.sql-1748883482080-d28465fb-d6e2-4084-a5b3-e35a0bc7d9c7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_supabase_migrations_20250602_create_user_ai_credentials.sql-1748883482080-d28465fb-d6e2-4084-a5b3-e35a0bc7d9c7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748884975460-0d7bf0bf-3e3e-4671-b8a7-ba3e6fdefcde.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748884975460-0d7bf0bf-3e3e-4671-b8a7-ba3e6fdefcde.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748885049275-b0ed6c69-20dc-4b6d-b5e7-6794afb7b7f1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748885049275-b0ed6c69-20dc-4b6d-b5e7-6794afb7b7f1.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748885049565-af56c8db-fe93-4b68-ae09-7729b82f3189.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748885049565-af56c8db-fe93-4b68-ae09-7729b82f3189.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_vite.config.ts-0-2d74c55c-aeda-4dee-82c2-a77295544fa1.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_vite.config.ts-0-2d74c55c-aeda-4dee-82c2-a77295544fa1.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_vite.config.ts-1748882856113-53ae7439-34aa-45bf-ac65-b326a64a789a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_vite.config.ts-1748882856113-53ae7439-34aa-45bf-ac65-b326a64a789a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/mcpServers.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/mcpServers.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/perl-DlYyT36c.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/perl-DlYyT36c.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/perl-UpK8AUhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/perl-UpK8AUhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pgsql-cWj3SLw2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pgsql-cWj3SLw2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/php-C92L-r_Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/php-C92L-r_Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preference-DQCL_iws.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preference-DQCL_iws.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preference-Dn6mpF6J.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/preference-Dn6mpF6J.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/protobuf-BQ74DTcm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/protobuf-BQ74DTcm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/protobuf-CZXszgil.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/protobuf-CZXszgil.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pug-8ix3pnNZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pug-8ix3pnNZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pug-kFxLfcjb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/pug-kFxLfcjb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/python-DY2G-JB8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/python-DY2G-JB8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redis-CHOsPHWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redis-CHOsPHWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redshift-CBifECDb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/redshift-CBifECDb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-diff-BjMXM2c7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-diff-BjMXM2c7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-home-DXGXtJVn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-home-DXGXtJVn.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-home-DwwH9vEz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-home-DwwH9vEz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/resize-observer-DdAtcrRr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/resize-observer-DdAtcrRr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/restructuredtext-CQoPj0uC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/restructuredtext-CQoPj0uC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/restructuredtext-CghPJEOS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/restructuredtext-CghPJEOS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ruby-1H8dtvFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ruby-1H8dtvFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ruby-CYWGW-b1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/ruby-CYWGW-b1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rules-DecO7AHT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rules-DecO7AHT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rules-RgpmxPUb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rules-RgpmxPUb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rust-APfvjYow.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rust-APfvjYow.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rust-DMDD0SHb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/rust-DMDD0SHb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-DkRyo0W8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-DkRyo0W8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sb-BYAiYHFx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sb-BYAiYHFx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sb-Ddgo-Lel.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sb-Ddgo-Lel.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scala-Bqvq8jcR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scala-Bqvq8jcR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scala-Bzjcj0lf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scala-Bzjcj0lf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scheme-Dhb-2j9p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scheme-Dhb-2j9p.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scss-CTwUZ5N7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scss-CTwUZ5N7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scss-DuQSCaUL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/scss-DuQSCaUL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-D3MqgnEk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-D3MqgnEk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/settings-BUgdOl3i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/settings-BUgdOl3i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/julia-ClS8lr_N.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/julia-ClS8lr_N.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-CKieFv1T.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-CKieFv1T.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-qtKhY-GQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-suggestions-qtKhY-GQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-types-904A5ehg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/next-edit-types-904A5ehg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/objective-c-DCIC4Ga8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/objective-c-DCIC4Ga8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/settings-y9LVuOMz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/settings-y9LVuOMz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/shell-CNhb_Zkf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/shell-CNhb_Zkf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/shell-CsDZo4DB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/shell-CsDZo4DB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/solidity-C4mwTkrB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/solidity-C4mwTkrB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/solidity-CME5AdoB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/solidity-CME5AdoB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sparql-CouE6pZG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/sparql-CouE6pZG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tcl-PloMZuKG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tcl-PloMZuKG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/test_service_pb-B6vKXZrG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/test_service_pb-B6vKXZrG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/timeline-definition-U7ZMHBDA-INrEwZRc.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/timeline-definition-U7ZMHBDA-INrEwZRc.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tsMode-BXDgwTrJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tsMode-BXDgwTrJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tsMode-DM5zRHFn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/tsMode-DM5zRHFn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/twig-BfRIq3la.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/twig-BfRIq3la.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/twig-h6VuAx0U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/twig-h6VuAx0U.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-BSMhNRWH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-BSMhNRWH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-a569v5Ol.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/types-a569v5Ol.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typescript-DT13XRSV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typescript-DT13XRSV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typescript-DTP-A_Zf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typescript-DTP-A_Zf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typespec-5IKh-a8s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typespec-5IKh-a8s.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typespec-DKGjpBXL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/typespec-DKGjpBXL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/vb-BwAE3J76.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/vb-BwAE3J76.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/vb-CS586MRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/vb-CS586MRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/wgsl-DCafy-vX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/wgsl-DCafy-vX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/wgsl-Du36xR5C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/wgsl-Du36xR5C.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xml-_u1XISHN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xml-_u1XISHN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xychartDiagram-6QU3TZC5-CI2aZIj-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/xychartDiagram-6QU3TZC5-CI2aZIj-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/yaml-CRGTkk5g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/yaml-CRGTkk5g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/yaml-Cr3uXDXT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/yaml-Cr3uXDXT.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T174521/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-0-e0d300cc-c8ac-43dd-8e69-4e3ef351d9c0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-0-e0d300cc-c8ac-43dd-8e69-4e3ef351d9c0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748883034763-febf1747-b9d6-44d0-bfbd-4ceb14a58734.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748883034763-febf1747-b9d6-44d0-bfbd-4ceb14a58734.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748883055285-b26dfb10-585b-4d37-aaba-a6acb287e2b5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748883055285-b26dfb10-585b-4d37-aaba-a6acb287e2b5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748884891549-441d93d1-391b-41a8-8844-ae7b00dbf834.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748884891549-441d93d1-391b-41a8-8844-ae7b00dbf834.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748884904781-c95862e9-9bd5-423c-9517-881945047ae9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.env.example-1748884904781-c95862e9-9bd5-423c-9517-881945047ae9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.replit-0-436cd4a2-ddc1-4774-8510-6a1563aca457.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.replit-0-436cd4a2-ddc1-4774-8510-6a1563aca457.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.replit-1748882908387-83d40335-b58d-4a56-9604-1d721b0d51c6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_.replit-1748882908387-83d40335-b58d-4a56-9604-1d721b0d51c6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-0-be824397-cbcd-49d4-b383-0b1964d9a06a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-0-be824397-cbcd-49d4-b383-0b1964d9a06a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-1748884760917-d273872e-e3b2-4eb1-bd20-be62cdbbdf6a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-1748884760917-d273872e-e3b2-4eb1-bd20-be62cdbbdf6a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-1748884777963-48ddbb8c-9e82-4cba-aa95-0857abb809ba.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_document_DocumentViewer.tsx-1748884777963-48ddbb8c-9e82-4cba-aa95-0857abb809ba.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-0-d0e9e6ed-98f8-4cc2-81f9-6ee81b1a9194.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-0-d0e9e6ed-98f8-4cc2-81f9-6ee81b1a9194.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-1748884793416-b57ce650-bc30-45b7-af10-b2c5eaa1ad7f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-1748884793416-b57ce650-bc30-45b7-af10-b2c5eaa1ad7f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-1748884809597-28b3db42-4510-472c-87c7-f14f661183ce.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_FlashcardGenerationPopup.tsx-1748884809597-28b3db42-4510-472c-87c7-f14f661183ce.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-0-64801b7a-4b19-4753-8a54-ca530862c29b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-0-64801b7a-4b19-4753-8a54-ca530862c29b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-1748884852574-de011ae7-a2e3-4107-ab9e-205679299d58.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-1748884852574-de011ae7-a2e3-4107-ab9e-205679299d58.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-1748884866771-ff9f05a3-c8f1-46ef-b383-76b8aa78918a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_client_src_components_flashcards_QuizGenerationPopup.tsx-1748884866771-ff9f05a3-c8f1-46ef-b383-76b8aa78918a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_README.md-1748882925942-63bda143-c643-4187-9d76-df8c3db9b8e7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_docs_README.md-1748882925942-63bda143-c643-4187-9d76-df8c3db9b8e7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748883653274-f441292f-06b4-4dc8-8a6e-8a195b5c9242.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748883653274-f441292f-06b4-4dc8-8a6e-8a195b5c9242.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_vite.ts-0-17e1cc3e-ecde-4ae8-aa78-345b8b3920f6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_server_vite.ts-0-17e1cc3e-ecde-4ae8-aa78-345b8b3920f6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748884996281-7e173940-a03d-460c-8e7b-d6c62b60e429.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/7bf218ba-5306-4b00-bced-189d9e7b9554/document-_home_runner_workspace_test-document-endpoint.js-1748884996281-7e173940-a03d-460c-8e7b-d6c62b60e429.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-diff-BLXYVTqm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/remote-agent-diff-BLXYVTqm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-LAgth8oY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-LAgth8oY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mips-Cu7FWeYr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mips-Cu7FWeYr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/msdax-CYqgjx_P.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/msdax-CYqgjx_P.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/msdax-DBX3bZkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/msdax-DBX3bZkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mysql-BHd6q0vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mysql-BHd6q0vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mysql-CMGNIvT0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.470.1/common-webviews/assets/mysql-CMGNIvT0.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886361500-80759362-c782-4e4f-86cb-07fa027a1cca.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886361500-80759362-c782-4e4f-86cb-07fa027a1cca.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748886425751-0cfbdd02-ada7-4e4a-a4b0-21c018a8599d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748886425751-0cfbdd02-ada7-4e4a-a4b0-21c018a8599d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748886466754-132f1818-8ded-4798-a73f-b928caac6c3a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748886466754-132f1818-8ded-4798-a73f-b928caac6c3a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7e3bb95/rTRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7e3bb95/rTRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886670545-5be8736e-393f-415e-afbc-cae016369ebd.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886670545-5be8736e-393f-415e-afbc-cae016369ebd.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748886754128-6c66dcb1-9af2-483c-90f5-b102688f9312.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748886754128-6c66dcb1-9af2-483c-90f5-b102688f9312.json"}, "/home/<USER>/workspace/start-server-clean.js": {"rootPath": "/home/<USER>/workspace", "relPath": "start-server-clean.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_start-server-clean.js-1748886797407-54b17b9b-df7b-4c06-9bbf-97df0d442331.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_start-server-clean.js-1748886797407-54b17b9b-df7b-4c06-9bbf-97df0d442331.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748886805191-c527824e-792e-4abd-8721-91425474b901.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748886805191-c527824e-792e-4abd-8721-91425474b901.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748886805518-e9a43d40-ef7a-47b5-a32e-a6ef4bd45399.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748886805518-e9a43d40-ef7a-47b5-a32e-a6ef4bd45399.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748886805707-76f44825-4331-40db-b8a7-cfb6a3e57695.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748886805707-76f44825-4331-40db-b8a7-cfb6a3e57695.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886804503-560831ad-5860-458a-9cbb-4f4d6971c1e3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748886804503-560831ad-5860-458a-9cbb-4f4d6971c1e3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-174**********-9607817e-bec6-489b-b847-3a50b8420cf7.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-174**********-9607817e-bec6-489b-b847-3a50b8420cf7.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/oSLg.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/oSLg.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-0-3b155e16-a0d0-4fc7-9002-602b99ddd829.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-0-3b155e16-a0d0-4fc7-9002-602b99ddd829.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748886870521-98c13ecb-4ce5-40df-92c0-2320b43bae73.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748886870521-98c13ecb-4ce5-40df-92c0-2320b43bae73.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/OhkY.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/OhkY.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-0-fb513c30-cb8a-4d82-89c9-cdb0b99b64c0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-0-fb513c30-cb8a-4d82-89c9-cdb0b99b64c0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748886886603-3884be73-1a5c-4e34-9f47-4fdc3adb8c47.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748886886603-3884be73-1a5c-4e34-9f47-4fdc3adb8c47.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d10ab0d/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d10ab0d/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-0-70849750-7aba-4e64-901e-459540d79db3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-0-70849750-7aba-4e64-901e-459540d79db3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748886900116-2766eb80-4270-4bf9-8879-6f8b4d9e3039.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748886900116-2766eb80-4270-4bf9-8879-6f8b4d9e3039.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d10ab0d/DADN.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d10ab0d/DADN.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/BfVo.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/BfVo.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-0-772235bd-0c30-4d49-9b52-f9ac90f78a3f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-0-772235bd-0c30-4d49-9b52-f9ac90f78a3f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-174**********-55d2a91f-0219-4fe6-acde-a17afcc68aed.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-174**********-55d2a91f-0219-4fe6-acde-a17afcc68aed.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-531c72db/OGg5.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-531c72db/OGg5.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748886946644-26349f46-0fb5-4fd2-b708-868007e6b82f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748886946644-26349f46-0fb5-4fd2-b708-868007e6b82f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51ae1c15/Y9xV.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51ae1c15/Y9xV.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886982587-354a269c-8823-40b6-b239-0243293a3b75.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748886982587-354a269c-8823-40b6-b239-0243293a3b75.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-51ae1c15/vqve.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-51ae1c15/vqve.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748887003476-22aa5f8c-4f6d-478b-bf7a-768dcbc6b7e9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748887003476-22aa5f8c-4f6d-478b-bf7a-768dcbc6b7e9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/0vVK.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/0vVK.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-0-f454d8b4-fc40-4e24-a2e9-b8acf059b543.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-0-f454d8b4-fc40-4e24-a2e9-b8acf059b543.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887020547-d39e9802-5b6b-41a3-85cb-76b2753ba3d8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887020547-d39e9802-5b6b-41a3-85cb-76b2753ba3d8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/s3Be.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/s3Be.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887034727-581af607-5e0f-46dc-8bfc-543b8e533ecc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887034727-581af607-5e0f-46dc-8bfc-543b8e533ecc.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/b2Jm.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/b2Jm.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887054601-33cfda07-a7f5-4202-acb9-aaad76b022eb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887054601-33cfda07-a7f5-4202-acb9-aaad76b022eb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/91K8.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/91K8.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887087339-ac6d53af-3d75-42a4-9b21-80656b405230.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887087339-ac6d53af-3d75-42a4-9b21-80656b405230.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/MMNz.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/MMNz.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887099284-662902e9-f1ae-4a1f-abcc-0be0d63373c2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887099284-662902e9-f1ae-4a1f-abcc-0be0d63373c2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/I61L.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/I61L.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887112054-c3383ebc-ce53-42e6-9099-0bc6b770518b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887112054-c3383ebc-ce53-42e6-9099-0bc6b770518b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/ZwWZ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/ZwWZ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887129254-9e4c15cc-804f-4117-a16e-37e9ce14e184.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887129254-9e4c15cc-804f-4117-a16e-37e9ce14e184.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/85uq.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/85uq.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887150940-e395d4d3-3f16-4239-922a-042f2eb2e2f9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887150940-e395d4d3-3f16-4239-922a-042f2eb2e2f9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/ov0L.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/ov0L.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887163754-dc0fd7fc-16d5-4848-b88b-5cdd5959cda9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887163754-dc0fd7fc-16d5-4848-b88b-5cdd5959cda9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/oBGt.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/oBGt.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887176817-33a1f5a5-3835-46e8-9525-87ac619c8074.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887176817-33a1f5a5-3835-46e8-9525-87ac619c8074.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/ZIap.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/ZIap.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887424264-7757b84c-ff9f-4bf2-a129-67d31dc8a3ea.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887424264-7757b84c-ff9f-4bf2-a129-67d31dc8a3ea.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/7GAh.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/7GAh.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887453381-2b99c950-d93a-4d6c-9dcf-7ba36a1eb4ab.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887453381-2b99c950-d93a-4d6c-9dcf-7ba36a1eb4ab.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/xssx.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/xssx.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887466761-9cb0461e-8be3-4655-989e-df7468265761.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887466761-9cb0461e-8be3-4655-989e-df7468265761.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/WHSm.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/WHSm.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887479794-9d4b671d-37af-4df0-8d53-f5568fadc3ad.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887479794-9d4b671d-37af-4df0-8d53-f5568fadc3ad.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/SBTh.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/SBTh.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-0-1e936bae-4de9-4ef3-8b69-a7b5f830e23c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-0-1e936bae-4de9-4ef3-8b69-a7b5f830e23c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887501740-e757ba7d-9e24-4da0-b477-e6c57d81a3f8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887501740-e757ba7d-9e24-4da0-b477-e6c57d81a3f8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/4tby.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/4tby.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887523167-ef332b12-7b72-4904-b046-f551e7d00040.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887523167-ef332b12-7b72-4904-b046-f551e7d00040.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/1LMj.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/1LMj.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887538799-04c77974-e8c2-4a70-98c2-ec6d05d56d8c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887538799-04c77974-e8c2-4a70-98c2-ec6d05d56d8c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-7812e3c7/sylN": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-7812e3c7/sylN"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/6EPd.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/6EPd.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75b2296a/h72b.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75b2296a/h72b.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d88e286a-341c-492a-a944-97f4a236d4d4": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d88e286a-341c-492a-a944-97f4a236d4d4"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748887570854-e7954a23-4fa3-4f93-b49d-2d12b584ca01.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_apply-migration.js-1748887570854-e7954a23-4fa3-4f93-b49d-2d12b584ca01.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748887570854-f8652a81-5ce9-401f-8a4b-4a492962b9b6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_ai_AIConfigurationSection.tsx-1748887570854-f8652a81-5ce9-401f-8a4b-4a492962b9b6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887570854-3e9b0107-c710-41d7-9d1f-a94674e4b5c3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748887570854-3e9b0107-c710-41d7-9d1f-a94674e4b5c3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887570854-80012863-c6e3-4a6f-8507-ecacadb1d35a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887570854-80012863-c6e3-4a6f-8507-ecacadb1d35a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748887570854-63e8501a-a26a-4650-8f40-9df1e89642aa.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_ai-provider.ts-1748887570854-63e8501a-a26a-4650-8f40-9df1e89642aa.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-1748887570854-bb3c4f41-14ec-476e-a870-17eb5ccd90de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_lib_api.ts-1748887570854-bb3c4f41-14ec-476e-a870-17eb5ccd90de.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887570854-2a110d1f-44bd-422b-b8db-cb72bc627ab6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887570854-2a110d1f-44bd-422b-b8db-cb72bc627ab6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748887570854-8ff623fb-bbb4-4a37-9ddc-575f2f24dd1c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_create-table.js-1748887570854-8ff623fb-bbb4-4a37-9ddc-575f2f24dd1c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748887570854-597085b5-4b35-4c04-bac7-38407441422f.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_middleware_apiKeyStorage.ts-1748887570854-597085b5-4b35-4c04-bac7-38407441422f.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887570854-d92e06a0-39a1-4a5b-a19f-ba31c14ddda8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887570854-d92e06a0-39a1-4a5b-a19f-ba31c14ddda8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748887570854-49f94d7e-59b6-40b1-bcc5-0295361e1ee9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_credentialsRoutes.ts-1748887570854-49f94d7e-59b6-40b1-bcc5-0295361e1ee9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887570854-77936e10-0e4c-41c1-84fe-01df27efd727.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_server_routes_quizRoutes.ts-1748887570854-77936e10-0e4c-41c1-84fe-01df27efd727.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_start-server-clean.js-1748887570854-22a9f4f4-be43-492a-a791-95d45320edc3.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_start-server-clean.js-1748887570854-22a9f4f4-be43-492a-a791-95d45320edc3.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748887570854-b5358bd4-912f-4d0f-b035-1ff874a879af.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-api-endpoint.js-1748887570854-b5358bd4-912f-4d0f-b035-1ff874a879af.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748887570854-22cc5ff3-c633-4276-bef1-3b025b29e47b.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/46e7634b-5e73-4fbd-b798-2b0e1f22e474/document-_home_runner_workspace_test-credentials.js-1748887570854-22cc5ff3-c633-4276-bef1-3b025b29e47b.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/rhjJ.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/rhjJ.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.git/Git.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.git/Git.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/output_logging_20250602T181641/1-GitHub Copilot Log (Code References).log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/output_logging_20250602T181641/1-GitHub Copilot Log (Code References).log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.vscode-pull-request-github/GitHub Pull Request.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.copilot-chat/GitHub Copilot Chat.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/Augment.vscode-augment/Augment.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/Augment.vscode-augment/Augment.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/remoteexthost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/remoteexthost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d10ab0d/GzoM.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d10ab0d/GzoM.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/758e74cb/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/758e74cb/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887688906-4fc81c0a-2fd2-4fe5-9090-50d0fd672bb5.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887688906-4fc81c0a-2fd2-4fe5-9090-50d0fd672bb5.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d10ab0d/QXFH.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d10ab0d/QXFH.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-0-f9d2aeb2-cca0-4c20-a015-3e139b509722.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-0-f9d2aeb2-cca0-4c20-a015-3e139b509722.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/78446345/i5eE.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/78446345/i5eE.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887807043-72028d2d-ba18-4f55-a97b-c3eb654c6d32.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887807043-72028d2d-ba18-4f55-a97b-c3eb654c6d32.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887717884-92617b07-5721-46a7-8763-0d41994a1d87.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887717884-92617b07-5721-46a7-8763-0d41994a1d87.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/iRyZ.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/iRyZ.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/FDyZ.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/FDyZ.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887759256-de81b393-06b6-4c7c-85ea-f827d064333d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887759256-de81b393-06b6-4c7c-85ea-f827d064333d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-37d6205e/gEBV.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-37d6205e/gEBV.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887970910-94c06bac-3657-4f45-9df8-75f29e2773c9.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887970910-94c06bac-3657-4f45-9df8-75f29e2773c9.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d4a881/KwdR.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d4a881/KwdR.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/WR1f.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/WR1f.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887919137-64aa95f2-b0de-4a56-a7f7-ab69e56df852.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887919137-64aa95f2-b0de-4a56-a7f7-ab69e56df852.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887943999-e0727966-3ef8-47f9-b80e-31e0857bccc0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887943999-e0727966-3ef8-47f9-b80e-31e0857bccc0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_CreateQuizForm.tsx-1748887839732-4530473e-04b9-43e9-9518-d6de30cd2f33.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_CreateQuizForm.tsx-1748887839732-4530473e-04b9-43e9-9518-d6de30cd2f33.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/4c2B.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/4c2B.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887645405-2aafb5fb-5219-4afd-9a91-ff15b5bbc97e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887645405-2aafb5fb-5219-4afd-9a91-ff15b5bbc97e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887730689-cb8c6cc0-0400-403a-ac47-0ca0b7509f16.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887730689-cb8c6cc0-0400-403a-ac47-0ca0b7509f16.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887675680-1e539acf-8c1b-4788-8ee9-95c814108267.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887675680-1e539acf-8c1b-4788-8ee9-95c814108267.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-0-577f2111-6e78-48ae-a895-33ed8f0ed0d4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-0-577f2111-6e78-48ae-a895-33ed8f0ed0d4.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887794182-907c5fef-d9d9-45a8-95d5-a5810c8402a6.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887794182-907c5fef-d9d9-45a8-95d5-a5810c8402a6.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/2wD1.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/2wD1.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887706725-d1e8790d-c2d3-4d48-b610-e0fdce67925a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes_aiRoutes.ts-1748887706725-d1e8790d-c2d3-4d48-b610-e0fdce67925a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/KyXh.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/KyXh.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_AiQuestionGenerator.tsx-0-37ffee81-70b8-49e3-908b-17cacf24cd22.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_AiQuestionGenerator.tsx-0-37ffee81-70b8-49e3-908b-17cacf24cd22.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-37d6205e/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-37d6205e/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887990172-71c56d5c-8a22-43fa-af6b-95b32db142f0.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887990172-71c56d5c-8a22-43fa-af6b-95b32db142f0.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_AiQuestionGenerator.tsx-1748887864934-f501251f-7835-4f29-9f19-fef7c07dd9bf.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_AiQuestionGenerator.tsx-1748887864934-f501251f-7835-4f29-9f19-fef7c07dd9bf.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887956194-b3e7d30d-3db5-43c8-b3d5-e7e2af42967d.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-1748887956194-b3e7d30d-3db5-43c8-b3d5-e7e2af42967d.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_CreateQuizForm.tsx-0-66e10b4a-f417-4365-ba61-168518687291.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_quiz_CreateQuizForm.tsx-0-66e10b4a-f417-4365-ba61-168518687291.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887906841-0cfd9900-f769-4c13-9eca-fd48613126ac.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887906841-0cfd9900-f769-4c13-9eca-fd48613126ac.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d4a881/VgI2.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d4a881/VgI2.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/78446345/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/78446345/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6d4a881/2PPD.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6d4a881/2PPD.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/r9DX.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/r9DX.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/225404bc/7Xw9.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/225404bc/7Xw9.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/2rja.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/2rja.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-0-e5f4d7b9-6cde-4111-b546-af2649f99903.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-0-e5f4d7b9-6cde-4111-b546-af2649f99903.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/4d10ab0d/gxw5.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/4d10ab0d/gxw5.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-0-5cf02733-a30a-4d63-abdd-99d1159d4dda.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_pages_FlashcardsPage.tsx-0-5cf02733-a30a-4d63-abdd-99d1159d4dda.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887818843-66d91e47-7cb6-4290-9e71-fb4519fecb57.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_flashcards_AiFlashcardGenerator.tsx-1748887818843-66d91e47-7cb6-4290-9e71-fb4519fecb57.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes.ts-1748887746811-4d968531-8fc5-4179-9f30-aa0283ca8f3e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes.ts-1748887746811-4d968531-8fc5-4179-9f30-aa0283ca8f3e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes.ts-0-b5e4fe80-698d-40ed-b7fa-7faef4ae87e2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_server_routes.ts-0-b5e4fe80-698d-40ed-b7fa-7faef4ae87e2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887780973-a955f669-cba1-487e-b8dc-43997623f00e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_lib_api.ts-1748887780973-a955f669-cba1-487e-b8dc-43997623f00e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-28adec09/zIkv.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-28adec09/zIkv.ts"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/758e74cb/KPVo.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/758e74cb/KPVo.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-0-cadc1413-a446-4140-b62a-5b5e7e9acc57.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-0-cadc1413-a446-4140-b62a-5b5e7e9acc57.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888016766-08752054-5ba2-4248-9f92-88865ee82155.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888016766-08752054-5ba2-4248-9f92-88865ee82155.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888028283-4ef39601-22d1-476e-9dd5-18fbcf29a315.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888028283-4ef39601-22d1-476e-9dd5-18fbcf29a315.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888039340-9dc0d6e2-d4fe-46e6-b644-9a6b437ac0fe.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888039340-9dc0d6e2-d4fe-46e6-b644-9a6b437ac0fe.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e72b8545-dde6-4c9b-becf-87b041a29f85.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-e72b8545-dde6-4c9b-becf-87b041a29f85.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/27Yc.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/27Yc.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/TTtC.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/TTtC.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/moJP.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/moJP.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/ptyhost.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/ptyhost.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/remoteTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/remoteTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/remoteagent.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/remoteagent.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/remoteExtHostTelemetry.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/remoteExtHostTelemetry.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.typescript-language-features/TypeScript.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.typescript-language-features/TypeScript.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.json-language-features/JSON Language Server.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.json-language-features/JSON Language Server.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.github/GitHub.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/vscode.github/GitHub.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.copilot/GitHub Copilot.log": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/logs/20250602T181638/exthost1/GitHub.copilot/GitHub Copilot.log"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/QGMe.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/QGMe.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888232342-105ccde5-7c01-4175-8133-883e761eee35.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888232342-105ccde5-7c01-4175-8133-883e761eee35.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedProfilesData/__default__profile__/extensions.user.cache"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/9AQr.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/9AQr.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888243321-80086ce5-1448-4044-aa4e-98db3cb0ca8e.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888243321-80086ce5-1448-4044-aa4e-98db3cb0ca8e.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/ka40.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/ka40.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888254281-38dc681a-fb3f-4aa8-9ac3-4d91c87dae11.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888254281-38dc681a-fb3f-4aa8-9ac3-4d91c87dae11.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/bgcC.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/bgcC.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888265480-4e3b71b3-e2dc-4308-91c2-0606b3f6cbfb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888265480-4e3b71b3-e2dc-4308-91c2-0606b3f6cbfb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/U096.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/U096.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888276877-6c46c14e-24be-4147-ad9c-2c4c9516a9d8.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888276877-6c46c14e-24be-4147-ad9c-2c4c9516a9d8.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/Df0e.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/Df0e.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888291219-6bec83dc-05b3-4d8e-abad-67f0b8173f4c.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888291219-6bec83dc-05b3-4d8e-abad-67f0b8173f4c.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/75f7691f/DkCF.tsx": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/75f7691f/DkCF.tsx"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888301809-69db07e7-36b2-4ed2-8de1-95947c20ee96.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_client_src_components_dashboard_UploadSection.tsx-1748888301809-69db07e7-36b2-4ed2-8de1-95947c20ee96.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/6bf29b4/2RA4.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/6bf29b4/2RA4.md"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_docs_API.md-0-b9eb99ee-cc16-48ea-a636-397f0b07863a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_docs_API.md-0-b9eb99ee-cc16-48ea-a636-397f0b07863a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_docs_API.md-1748888316929-cb70cee5-4437-49a5-b58c-a21851909d0a.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/checkpoint-documents/e72b8545-dde6-4c9b-becf-87b041a29f85/document-_home_runner_workspace_docs_API.md-1748888316929-cb70cee5-4437-49a5-b58c-a21851909d0a.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/5f841ef3-b7a0-4fa9-afdf-9a0cc8d868b4": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/task-storage/tasks/5f841ef3-b7a0-4fa9-afdf-9a0cc8d868b4"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/3bb2cc9e091ec5784053ba805c0df47023c01d5681580a7459c0d8912a940047.png": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/Augment.vscode-augment/augment-user-assets/3bb2cc9e091ec5784053ba805c0df47023c01d5681580a7459c0d8912a940047.png"}}