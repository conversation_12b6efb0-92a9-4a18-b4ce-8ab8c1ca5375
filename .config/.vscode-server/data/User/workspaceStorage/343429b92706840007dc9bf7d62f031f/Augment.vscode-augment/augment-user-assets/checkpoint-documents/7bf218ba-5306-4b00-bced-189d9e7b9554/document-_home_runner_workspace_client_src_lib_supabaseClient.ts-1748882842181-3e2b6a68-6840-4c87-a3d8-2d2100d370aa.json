{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/supabaseClient.ts"}, "originalCode": "import { createClient } from \"@supabase/supabase-js\";\nimport { Database } from \"../types/supabase\"; // Assuming you will generate this type\n\n// Use environment variables if available, otherwise fallback to configured values\nconst supabaseUrl =\n  import.meta.env.VITE_SUPABASE_URL ||\n  \"https://hrdjfukhzbzksqaupqie.supabase.co\";\nconst supabaseAnonKey =\n  import.meta.env.VITE_SUPABASE_ANON_KEY ||\n  \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDI5NDksImV4cCI6MjA2MjgxODk0OX0.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g\";\n\nif (!supabaseUrl) {\n  throw new Error(\"VITE_SUPABASE_URL is not set and no fallback available\");\n}\n\nif (!supabaseAnonKey) {\n  throw new Error(\n    \"VITE_SUPABASE_ANON_KEY is not set and no fallback available\"\n  );\n}\n\nconsole.log(\"✓ Supabase client initialized\");\nconsole.log(\"✓ URL:\", supabaseUrl);\nconsole.log(\"✓ Using anon key:\", supabaseAnonKey ? \"Configured\" : \"Missing\");\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);\n", "modifiedCode": "import { createClient } from \"@supabase/supabase-js\";\nimport { Database } from \"../types/supabase\"; // Assuming you will generate this type\n\n// Use environment variables - fallback values only for development\nconst supabaseUrl = import.meta.env.VITE_SUPABASE_URL;\nconst supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;\n\n// In production, these should always be set via environment variables\nif (!supabaseUrl) {\n  throw new Error(\"VITE_SUPABASE_URL environment variable is required\");\n}\n\nif (!supabaseAnonKey) {\n  throw new Error(\"VITE_SUPABASE_ANON_KEY environment variable is required\");\n}\n\nconsole.log(\"✓ Supabase client initialized\");\nconsole.log(\"✓ URL:\", supabaseUrl);\nconsole.log(\"✓ Using anon key:\", supabaseAnonKey ? \"Configured\" : \"Missing\");\n\nexport const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);\n"}