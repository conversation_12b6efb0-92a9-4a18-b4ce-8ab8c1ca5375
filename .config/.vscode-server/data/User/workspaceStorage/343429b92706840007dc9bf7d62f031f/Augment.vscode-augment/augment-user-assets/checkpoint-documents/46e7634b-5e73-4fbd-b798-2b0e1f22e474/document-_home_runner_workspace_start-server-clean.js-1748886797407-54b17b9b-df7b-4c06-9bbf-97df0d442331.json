{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "start-server-clean.js"}, "modifiedCode": "#!/usr/bin/env node\n\n// <PERSON><PERSON><PERSON> to start the server with filtered console output\nimport { spawn } from 'child_process';\n\n// Set environment variables\nprocess.env.SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nprocess.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\nprocess.env.VITE_DATABASE_PASSWORD = 'Aloha808!';\nprocess.env.NODE_ENV = 'development';\n\nconsole.log('🚀 Starting ChewyAI server with filtered output...\\n');\n\n// Start the server process\nconst serverProcess = spawn('npx', ['tsx', 'server/index.ts'], {\n  stdio: ['inherit', 'pipe', 'pipe'],\n  cwd: process.cwd()\n});\n\n// Filter function to reduce noise\nfunction shouldShowLog(line) {\n  const noisePatterns = [\n    /babel program\\.body/,\n    /babel.*enter/,\n    /babel.*exit/,\n    /babel.*Recursing/,\n    /vite:config/,\n    /express:router/,\n    /\\[background\\/carbon\\]/,\n    /CarbonApi request/,\n    /CONNECTION_REFUSED.*24678/\n  ];\n  \n  return !noisePatterns.some(pattern => pattern.test(line));\n}\n\n// Handle stdout\nserverProcess.stdout.on('data', (data) => {\n  const lines = data.toString().split('\\n');\n  lines.forEach(line => {\n    if (line.trim() && shouldShowLog(line)) {\n      console.log(line);\n    }\n  });\n});\n\n// Handle stderr\nserverProcess.stderr.on('data', (data) => {\n  const lines = data.toString().split('\\n');\n  lines.forEach(line => {\n    if (line.trim() && shouldShowLog(line)) {\n      console.error(line);\n    }\n  });\n});\n\n// Handle process exit\nserverProcess.on('close', (code) => {\n  console.log(`\\n🛑 Server process exited with code ${code}`);\n  process.exit(code);\n});\n\n// Handle Ctrl+C\nprocess.on('SIGINT', () => {\n  console.log('\\n🛑 Received SIGINT, shutting down server...');\n  serverProcess.kill('SIGINT');\n});\n\nprocess.on('SIGTERM', () => {\n  console.log('\\n🛑 Received SIGTERM, shutting down server...');\n  serverProcess.kill('SIGTERM');\n});\n"}