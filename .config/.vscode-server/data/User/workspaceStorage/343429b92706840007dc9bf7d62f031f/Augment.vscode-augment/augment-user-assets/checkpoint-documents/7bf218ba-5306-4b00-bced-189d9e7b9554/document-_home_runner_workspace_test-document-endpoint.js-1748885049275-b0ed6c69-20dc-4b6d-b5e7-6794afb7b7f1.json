{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-document-endpoint.js"}, "originalCode": "// Simple test script to verify document content endpoint\n// Run with: node test-document-endpoint.js\n\nasync function testDocumentEndpoint() {\n  try {\n    console.log('🧪 Testing Document Content Endpoint...');\n    \n    // Test health endpoint first\n    console.log('\\n1. Testing health endpoint...');\n    const healthResponse = await fetch('http://localhost:5000/api/health');\n    const healthData = await healthResponse.json();\n    console.log('✅ Health check:', healthData.status);\n    \n    // Test document endpoint without auth (should fail)\n    console.log('\\n2. Testing document endpoint without auth...');\n    const noAuthResponse = await fetch('http://localhost:5000/api/documents/test-id/content');\n    console.log('📄 Response status:', noAuthResponse.status);\n    console.log('📄 Expected: 401 (Unauthorized)');\n    \n    if (noAuthResponse.status === 401) {\n      console.log('✅ Authentication protection working correctly');\n    } else {\n      console.log('❌ Authentication protection may not be working');\n    }\n    \n    // Test with invalid document ID but valid structure\n    console.log('\\n3. Testing endpoint structure...');\n    const structureResponse = await fetch('http://localhost:5000/api/documents/invalid-id/content', {\n      headers: {\n        'Authorization': 'Bearer invalid-token'\n      }\n    });\n    console.log('📄 Response status:', structureResponse.status);\n    console.log('📄 Expected: 401 or 404');\n    \n    console.log('\\n✅ Document endpoint tests completed');\n    console.log('\\n📋 Summary:');\n    console.log('- Health endpoint: Working');\n    console.log('- Authentication: Protected');\n    console.log('- Endpoint structure: Accessible');\n    console.log('\\n💡 To test with real documents, use the frontend with proper authentication.');\n    \n  } catch (error) {\n    console.error('❌ Test failed:', error.message);\n    console.log('\\n🔧 Make sure the server is running with: npm run dev:server');\n  }\n}\n\ntestDocumentEndpoint();\n"}