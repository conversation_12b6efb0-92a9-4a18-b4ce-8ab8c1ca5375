{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/AiQuestionGenerator.tsx"}, "originalCode": "import React, { useState, useEffect } from \"react\";\nimport { Tables } from \"../../types/supabase\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport {\n  generateAiQuizAPI,\n  addQuestionsToQuizAPI,\n  getDocumentsAPI,\n  deleteQuizAPI,\n  GenerateAiQuizApiResponse,\n  GenerateAiQuizApiPayload,\n  QuizQuestionBatchInsertPayload\n} from \"../../lib/api\";\nimport {\n  AiQuizGenerationOptions,\n  GenerationOptions,\n} from \"./AiQuizGenerationOptions\";\nimport { getAIProviderSettings } from \"@/lib/ai-provider\";\nimport { AIProviderConfig } from \"@shared/types/quiz\";\nimport { Label } from \"@/components/ui/label\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface AiQuestionGeneratorProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onGenerationComplete: () => void;\n}\n\nexport const AiQuestionGenerator: React.FC<AiQuestionGeneratorProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onGenerationComplete,\n}) => {\n  const { user } = useAuth();\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationError, setGenerationError] = useState<string | null>(null);\n  const [aiGenerationOptions, setAiGenerationOptions] =\n    useState<GenerationOptions>({\n      numberOfQuestions: 5,\n      questionTypes: [\"multiple_choice\"],\n    });\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [\n    selectedDocumentIdsForGeneration,\n    setSelectedDocumentIdsForGeneration,\n  ] = useState<string[]>([]);\n  const [customPrompt, setCustomPrompt] = useState(\"\");\n  const [loadingDocuments, setLoadingDocuments] = useState(false);\n\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      setLoadingDocuments(true);\n      try {\n        const docs = await getDocumentsAPI();\n        setAvailableDocuments(docs || []);\n        if (\n          studyDocumentId &&\n          docs.some(\n            (doc: Tables<\"study_documents\">) => doc.id === studyDocumentId\n          )\n        ) {\n          setSelectedDocumentIdsForGeneration([studyDocumentId]);\n        }\n      } catch (err: any) {\n        console.error(\"Failed to fetch documents for AI generation:\", err);\n      } finally {\n        setLoadingDocuments(false);\n      }\n    };\n    fetchDocuments();\n  }, [studyDocumentId]);\n\n  const handleGenerateQuestions = async () => {\n    if (!user || !selectedQuizId) {\n      setGenerationError(\"User not logged in or quiz not selected.\");\n      return;\n    }\n\n    if (selectedDocumentIdsForGeneration.length === 0) {\n      setGenerationError(\n        \"Please select at least one document to generate questions from.\"\n      );\n      return;\n    }\n\n    setIsGenerating(true);\n    setGenerationError(null);\n\n    try {\n      const clientAiSettings = getAIProviderSettings();\n      if (!clientAiSettings.apiKey) {\n        throw new Error(\"AI Provider API Key is not configured.\");\n      }\n\n      const apiAiConfig: AIProviderConfig = {\n        apiKey: clientAiSettings.apiKey,\n        baseUrl: clientAiSettings.baseUrl,\n        model: clientAiSettings.generationModel, // Ensure generationModel is used\n      };\n\n      // Create a temporary quiz name for the generation process\n      const tempQuizName = `${selectedQuizName}_temp_${Date.now()}`;\n      \n      const payload: GenerateAiQuizApiPayload = {\n        documentIds: selectedDocumentIdsForGeneration,\n        quizName: tempQuizName,\n        customPrompt: customPrompt || undefined,\n        generationOptions: aiGenerationOptions,\n        aiConfig: apiAiConfig,\n      };\n\n      // Generate a new quiz first (we'll extract its questions)\n      const generationResult: GenerateAiQuizApiResponse =\n        await generateAiQuizAPI(payload);\n\n      console.log(\"AI Quiz Generation API result:\", generationResult);\n\n      if (\n        generationResult &&\n        generationResult.success &&\n        generationResult.quiz &&\n        generationResult.quiz.questions &&\n        generationResult.quiz.questions.length > 0\n      ) {\n        // Questions are directly available in generationResult.quiz.questions\n        const questionsFromTempQuiz = generationResult.quiz.questions;\n\n        const questionsToAdd: QuizQuestionBatchInsertPayload[] = questionsFromTempQuiz.map(\n          (q: any) => ({\n            // The /generate endpoint maps db fields to client-facing names like questionText\n            // We need to map them back to db field names for batch insert\n            question_text: q.questionText || q.question_text, \n            type: q.type,\n            options: q.options,\n            correct_answer: q.correctAnswer || q.correct_answer,\n            explanation: q.explanation,\n          })\n        );\n\n        // Add these questions to the original quiz\n        const addedQuestions = await addQuestionsToQuizAPI(\n          selectedQuizId,\n          questionsToAdd\n        );\n        console.log(\n          `Added ${questionsToAdd.length} questions to quiz ${selectedQuizId}. Response:`,\n          addedQuestions\n        );\n\n        // Delete the temporary quiz since we've moved its questions\n        if (generationResult.quizId) { // Ensure quizId is present\n          try {\n            await deleteQuizAPI(generationResult.quizId);\n            console.log(`Deleted temporary quiz ${generationResult.quizId}`);\n          } catch (deleteError: any) {\n            console.warn(`Failed to delete temporary quiz ${generationResult.quizId}: ${deleteError.message}`);\n          }\n        } else {\n          console.warn(\"Temporary quizId not found in generationResult, cannot delete temporary quiz.\");\n        }\n        onGenerationComplete();\n      } else if (\n        generationResult &&\n        !generationResult.success\n      ) {\n        throw new Error(\n          \"AI Question generation was not successful according to the server.\"\n        );\n      } else if (generationResult && generationResult.quiz && (!generationResult.quiz.questions || generationResult.quiz.questions.length === 0)) {\n        // Attempt to delete the empty temporary quiz if it was created\n        if (generationResult.quizId) {\n          try {\n            await deleteQuizAPI(generationResult.quizId);\n            console.log(`Deleted empty temporary quiz ${generationResult.quizId}`);\n          }\n          catch (deleteError: any) {\n            console.warn(`Failed to delete empty temporary quiz ${generationResult.quizId}: ${deleteError.message}`);\n          }\n        }\n        throw new Error(\"AI generated a quiz structure, but it contained no questions.\");\n      } else {\n        throw new Error(\"Generation request was unsuccessful\");\n      }\n    } catch (err: any) {\n      console.error(\"Error during AI question generation:\", err);\n      setGenerationError(err.message || \"Failed to generate questions.\");\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const handleDocumentSelectionChange = (\n    documentId: string,\n    isSelected: boolean\n  ) => {\n    setSelectedDocumentIdsForGeneration((prev) =>\n      isSelected\n        ? [...prev, documentId]\n        : prev.filter((id) => id !== documentId)\n    );\n  };\n\n  return (\n    <div className=\"bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4 mb-6\">\n        <h5 className=\"text-lg font-semibold text-purple-400 flex items-center\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 20 20\"\n            fill=\"currentColor\"\n            className=\"w-5 h-5 mr-2 text-purple-400\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              d=\"M10 2a8 8 0 100 16 8 8 0 000-16zM8.5 6.5a.5.5 0 00-1 0V8h-1.5a.5.5 0 000 1H8v1.5a.5.5 0 001 0V9h1.5a.5.5 0 000-1H9V6.5zm2.5 6a.5.5 0 00-1 0v.097A2.004 2.004 0 0010 13a2 2 0 100-4 .5.5 0 000 1 1 1 0 110 2 .5.5 0 00.5-.5V12.5z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          AI Question Generation\n        </h5>\n      </div>\n\n      {generationError && (\n        <div className=\"bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg mb-4\">\n          {generationError}\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        <div>\n          <Label\n            htmlFor=\"documentSelect\"\n            className=\"text-slate-300 font-medium\"\n          >\n            Select Document(s) for AI Generation*\n          </Label>\n          {loadingDocuments ? (\n            <p className=\"text-sm text-slate-400 mt-2\">Loading documents...</p>\n          ) : availableDocuments.length === 0 ? (\n            <p className=\"text-sm text-slate-400 mt-2\">\n              No documents available. Please upload a study document first.\n            </p>\n          ) : (\n            <div className=\"max-h-40 overflow-y-auto bg-slate-800 border border-slate-600 rounded-md p-2 space-y-2 mt-2\">\n              {availableDocuments.map((doc) => (\n                <div key={doc.id} className=\"flex items-center\">\n                  <input\n                    id={`doc-${doc.id}`}\n                    type=\"checkbox\"\n                    checked={selectedDocumentIdsForGeneration.includes(doc.id)}\n                    onChange={(e) =>\n                      handleDocumentSelectionChange(doc.id, e.target.checked)\n                    }\n                    className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                  />\n                  <label\n                    htmlFor={`doc-${doc.id}`}\n                    className=\"ml-2 block text-sm text-slate-300 truncate cursor-pointer\"\n                    title={doc.file_name}\n                  >\n                    {doc.file_name}\n                  </label>\n                </div>\n              ))}\n            </div>\n          )}\n          {selectedDocumentIdsForGeneration.length === 0 &&\n            !loadingDocuments &&\n            availableDocuments.length > 0 && (\n              <p className=\"text-xs text-yellow-400 mt-1\">\n                Please select at least one document.\n              </p>\n            )}\n        </div>\n\n        <div>\n          <Label htmlFor=\"customPrompt\" className=\"text-slate-300 font-medium\">\n            Custom Instructions for AI{\" \"}\n            <span className=\"text-slate-500\">(Optional)</span>\n          </Label>\n          <textarea\n            id=\"customPrompt\"\n            value={customPrompt}\n            onChange={(e) => setCustomPrompt(e.target.value)}\n            rows={3}\n            placeholder=\"e.g., Focus on definitions, compare and contrast X and Y, generate questions suitable for a final exam...\"\n            className=\"block w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none resize-none mt-2\"\n          />\n        </div>\n\n        <AiQuizGenerationOptions\n          generationOptions={aiGenerationOptions}\n          setGenerationOptions={setAiGenerationOptions}\n          isGenerating={isGenerating}\n          onGenerate={handleGenerateQuestions}\n          documentIds={selectedDocumentIdsForGeneration}\n        />\n      </div>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useState, useEffect } from \"react\";\nimport { Tables } from \"../../types/supabase\";\nimport { useAuth } from \"../../hooks/useAuth\";\nimport {\n  generateAiQuizAPI,\n  addQuestionsToQuizAPI,\n  getDocumentsAPI,\n  deleteQuizAPI,\n  GenerateAiQuizApiResponse,\n  GenerateAiQuizApiPayload,\n  QuizQuestionBatchInsertPayload\n} from \"../../lib/api\";\nimport {\n  AiQuizGenerationOptions,\n  GenerationOptions,\n} from \"./AiQuizGenerationOptions\";\nimport { getAIProviderSettings } from \"@/lib/ai-provider\";\nimport { AIProviderConfig } from \"@shared/types/quiz\";\nimport { Label } from \"@/components/ui/label\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface AiQuestionGeneratorProps {\n  selectedQuizId: string;\n  selectedQuizName: string;\n  studyDocumentId?: string;\n  onGenerationComplete: () => void;\n}\n\nexport const AiQuestionGenerator: React.FC<AiQuestionGeneratorProps> = ({\n  selectedQuizId,\n  selectedQuizName,\n  studyDocumentId,\n  onGenerationComplete,\n}) => {\n  const { user } = useAuth();\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [generationError, setGenerationError] = useState<string | null>(null);\n  const [aiGenerationOptions, setAiGenerationOptions] =\n    useState<GenerationOptions>({\n      numberOfQuestions: 5,\n      questionTypes: [\"multiple_choice\"],\n    });\n  const [availableDocuments, setAvailableDocuments] = useState<\n    Tables<\"study_documents\">[]\n  >([]);\n  const [\n    selectedDocumentIdsForGeneration,\n    setSelectedDocumentIdsForGeneration,\n  ] = useState<string[]>([]);\n  const [customPrompt, setCustomPrompt] = useState(\"\");\n  const [loadingDocuments, setLoadingDocuments] = useState(false);\n\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      setLoadingDocuments(true);\n      try {\n        const docs = await getDocumentsAPI();\n        setAvailableDocuments(docs || []);\n        if (\n          studyDocumentId &&\n          docs.some(\n            (doc: Tables<\"study_documents\">) => doc.id === studyDocumentId\n          )\n        ) {\n          setSelectedDocumentIdsForGeneration([studyDocumentId]);\n        }\n      } catch (err: any) {\n        console.error(\"Failed to fetch documents for AI generation:\", err);\n      } finally {\n        setLoadingDocuments(false);\n      }\n    };\n    fetchDocuments();\n  }, [studyDocumentId]);\n\n  const handleGenerateQuestions = async () => {\n    if (!user || !selectedQuizId) {\n      setGenerationError(\"User not logged in or quiz not selected.\");\n      return;\n    }\n\n    if (selectedDocumentIdsForGeneration.length === 0) {\n      setGenerationError(\n        \"Please select at least one document to generate questions from.\"\n      );\n      return;\n    }\n\n    setIsGenerating(true);\n    setGenerationError(null);\n\n    try {\n      const clientAiSettings = getAIProviderSettings();\n      if (!clientAiSettings.apiKey) {\n        throw new Error(\"AI Provider API Key is not configured.\");\n      }\n\n      const apiAiConfig: AIProviderConfig = {\n        apiKey: clientAiSettings.apiKey,\n        baseUrl: clientAiSettings.baseUrl,\n        model: clientAiSettings.generationModel, // Ensure generationModel is used\n      };\n\n      // Create a temporary quiz name for the generation process\n      const tempQuizName = `${selectedQuizName}_temp_${Date.now()}`;\n      \n      const payload: GenerateAiQuizApiPayload = {\n        documentIds: selectedDocumentIdsForGeneration,\n        quizName: tempQuizName,\n        customPrompt: customPrompt || undefined,\n        generationOptions: aiGenerationOptions,\n        aiConfig: apiAiConfig,\n      };\n\n      // Generate a new quiz first (we'll extract its questions)\n      const generationResult: GenerateAiQuizApiResponse =\n        await generateAiQuizAPI(payload);\n\n      console.log(\"AI Quiz Generation API result:\", generationResult);\n\n      if (\n        generationResult &&\n        generationResult.success &&\n        generationResult.quiz &&\n        generationResult.quiz.questions &&\n        generationResult.quiz.questions.length > 0\n      ) {\n        // Questions are directly available in generationResult.quiz.questions\n        const questionsFromTempQuiz = generationResult.quiz.questions;\n\n        const questionsToAdd: QuizQuestionBatchInsertPayload[] = questionsFromTempQuiz.map(\n          (q: any) => ({\n            // The /generate endpoint maps db fields to client-facing names like questionText\n            // We need to map them back to db field names for batch insert\n            question_text: q.questionText || q.question_text, \n            type: q.type,\n            options: q.options,\n            correct_answer: q.correctAnswer || q.correct_answer,\n            explanation: q.explanation,\n          })\n        );\n\n        // Add these questions to the original quiz\n        const addedQuestions = await addQuestionsToQuizAPI(\n          selectedQuizId,\n          questionsToAdd\n        );\n        console.log(\n          `Added ${questionsToAdd.length} questions to quiz ${selectedQuizId}. Response:`,\n          addedQuestions\n        );\n\n        // Delete the temporary quiz since we've moved its questions\n        if (generationResult.quizId) { // Ensure quizId is present\n          try {\n            await deleteQuizAPI(generationResult.quizId);\n            console.log(`Deleted temporary quiz ${generationResult.quizId}`);\n          } catch (deleteError: any) {\n            console.warn(`Failed to delete temporary quiz ${generationResult.quizId}: ${deleteError.message}`);\n          }\n        } else {\n          console.warn(\"Temporary quizId not found in generationResult, cannot delete temporary quiz.\");\n        }\n        onGenerationComplete();\n      } else if (\n        generationResult &&\n        !generationResult.success\n      ) {\n        throw new Error(\n          \"AI Question generation was not successful according to the server.\"\n        );\n      } else if (generationResult && generationResult.quiz && (!generationResult.quiz.questions || generationResult.quiz.questions.length === 0)) {\n        // Attempt to delete the empty temporary quiz if it was created\n        if (generationResult.quizId) {\n          try {\n            await deleteQuizAPI(generationResult.quizId);\n            console.log(`Deleted empty temporary quiz ${generationResult.quizId}`);\n          }\n          catch (deleteError: any) {\n            console.warn(`Failed to delete empty temporary quiz ${generationResult.quizId}: ${deleteError.message}`);\n          }\n        }\n        throw new Error(\"AI generated a quiz structure, but it contained no questions.\");\n      } else {\n        throw new Error(\"Generation request was unsuccessful\");\n      }\n    } catch (err: any) {\n      console.error(\"Error during AI question generation:\", err);\n      setGenerationError(err.message || \"Failed to generate questions.\");\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  const handleDocumentSelectionChange = (\n    documentId: string,\n    isSelected: boolean\n  ) => {\n    setSelectedDocumentIdsForGeneration((prev) =>\n      isSelected\n        ? [...prev, documentId]\n        : prev.filter((id) => id !== documentId)\n    );\n  };\n\n  return (\n    <div className=\"bg-slate-900 border border-slate-600 rounded-xl p-6 shadow-lg\">\n      <div className=\"flex items-center justify-between border-b border-slate-700 pb-4 mb-6\">\n        <h5 className=\"text-lg font-semibold text-purple-400 flex items-center\">\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 20 20\"\n            fill=\"currentColor\"\n            className=\"w-5 h-5 mr-2 text-purple-400\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              d=\"M10 2a8 8 0 100 16 8 8 0 000-16zM8.5 6.5a.5.5 0 00-1 0V8h-1.5a.5.5 0 000 1H8v1.5a.5.5 0 001 0V9h1.5a.5.5 0 000-1H9V6.5zm2.5 6a.5.5 0 00-1 0v.097A2.004 2.004 0 0010 13a2 2 0 100-4 .5.5 0 000 1 1 1 0 110 2 .5.5 0 00.5-.5V12.5z\"\n              clipRule=\"evenodd\"\n            />\n          </svg>\n          AI Question Generation\n        </h5>\n      </div>\n\n      {generationError && (\n        <div className=\"bg-red-900/20 border border-red-500/30 text-red-400 text-sm p-3 rounded-lg mb-4\">\n          {generationError}\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        <div>\n          <Label\n            htmlFor=\"documentSelect\"\n            className=\"text-slate-300 font-medium\"\n          >\n            Select Document(s) for AI Generation*\n          </Label>\n          {loadingDocuments ? (\n            <p className=\"text-sm text-slate-400 mt-2\">Loading documents...</p>\n          ) : availableDocuments.length === 0 ? (\n            <p className=\"text-sm text-slate-400 mt-2\">\n              No documents available. Please upload a study document first.\n            </p>\n          ) : (\n            <div className=\"max-h-40 overflow-y-auto bg-slate-800 border border-slate-600 rounded-md p-2 space-y-2 mt-2\">\n              {availableDocuments.map((doc) => (\n                <div key={doc.id} className=\"flex items-center\">\n                  <input\n                    id={`doc-${doc.id}`}\n                    type=\"checkbox\"\n                    checked={selectedDocumentIdsForGeneration.includes(doc.id)}\n                    onChange={(e) =>\n                      handleDocumentSelectionChange(doc.id, e.target.checked)\n                    }\n                    className=\"h-4 w-4 text-purple-600 border-slate-500 rounded focus:ring-purple-500\"\n                  />\n                  <label\n                    htmlFor={`doc-${doc.id}`}\n                    className=\"ml-2 block text-sm text-slate-300 truncate cursor-pointer\"\n                    title={doc.file_name}\n                  >\n                    {doc.file_name}\n                  </label>\n                </div>\n              ))}\n            </div>\n          )}\n          {selectedDocumentIdsForGeneration.length === 0 &&\n            !loadingDocuments &&\n            availableDocuments.length > 0 && (\n              <p className=\"text-xs text-yellow-400 mt-1\">\n                Please select at least one document.\n              </p>\n            )}\n        </div>\n\n        <div>\n          <Label htmlFor=\"customPrompt\" className=\"text-slate-300 font-medium\">\n            Custom Instructions for AI{\" \"}\n            <span className=\"text-slate-500\">(Optional)</span>\n          </Label>\n          <textarea\n            id=\"customPrompt\"\n            value={customPrompt}\n            onChange={(e) => setCustomPrompt(e.target.value)}\n            rows={3}\n            placeholder=\"e.g., Focus on definitions, compare and contrast X and Y, generate questions suitable for a final exam...\"\n            className=\"block w-full px-3 py-2 bg-slate-800 border border-slate-600 rounded-md text-slate-200 placeholder-slate-400 focus:border-purple-500 focus:ring-1 focus:ring-purple-500 outline-none resize-none mt-2\"\n          />\n        </div>\n\n        <AiQuizGenerationOptions\n          generationOptions={aiGenerationOptions}\n          setGenerationOptions={setAiGenerationOptions}\n          isGenerating={isGenerating}\n          onGenerate={handleGenerateQuestions}\n          documentIds={selectedDocumentIdsForGeneration}\n        />\n      </div>\n    </div>\n  );\n};\n"}