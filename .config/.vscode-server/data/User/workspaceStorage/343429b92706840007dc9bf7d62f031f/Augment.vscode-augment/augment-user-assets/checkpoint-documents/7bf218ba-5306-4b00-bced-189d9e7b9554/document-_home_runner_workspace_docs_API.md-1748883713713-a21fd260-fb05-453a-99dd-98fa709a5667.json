{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/API.md"}, "originalCode": "# ChewyAI API Documentation\n\n## 🌐 Base URL\n- **Development**: `http://localhost:5000/api`\n- **Production**: `https://your-replit-domain.com/api`\n\n## 🔐 Authentication\nMost endpoints require authentication using JWT tokens from Supabase Auth.\n\n### Headers\n```\nAuthorization: Bearer <jwt_token>\nContent-Type: application/json\n```\n\n## 📋 API Endpoints\n\n### Health Check\n#### GET `/api/health`\nCheck server status and health.\n\n**Response:**\n```json\n{\n  \"status\": \"healthy\",\n  \"timestamp\": \"2024-01-01T00:00:00.000Z\",\n  \"environment\": \"production\",\n  \"version\": \"1.0.0\"\n}\n```\n\n### AI Integration\n#### POST `/api/flashcards/generate`\nGenerate flashcards from text content using AI.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"textContent\": \"Your study material text...\",\n  \"documentId\": \"uuid-string\",\n  \"deckTitle\": \"Optional deck title\",\n  \"count\": 10,\n  \"customPrompt\": \"Optional custom prompt\",\n  \"aiSettings\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"apiKey\": \"user-api-key\",\n    \"generationModel\": \"google/gemini-2.5-pro\"\n  }\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"flashcards\": [\n    {\n      \"question\": \"What is the capital of France?\",\n      \"answer\": \"Paris\"\n    }\n  ],\n  \"deckId\": \"generated-deck-id\"\n}\n```\n\n#### POST `/api/extract-and-format`\nExtract and format text using AI for better readability.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"rawText\": \"Unformatted text content...\",\n  \"fileName\": \"document.pdf\",\n  \"aiSettings\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"apiKey\": \"user-api-key\",\n    \"extractionModel\": \"google/gemini-2.5-flash\"\n  }\n}\n```\n\n### Document Management\n#### POST `/api/documents`\nCreate a new document.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"title\": \"Document Title\",\n  \"content\": \"Document content...\",\n  \"type\": \"pdf|docx|txt\",\n  \"metadata\": {}\n}\n```\n\n#### POST `/api/documents/secure-upload`\nSecurely upload a document through the backend.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"fileName\": \"document.pdf\",\n  \"fileContent\": \"base64-encoded-content\",\n  \"contentType\": \"application/pdf\"\n}\n```\n\n### Flashcard Management\n#### GET `/api/decks`\nGet all flashcard decks for the authenticated user.\n\n**Authentication:** Required\n\n**Response:**\n```json\n[\n  {\n    \"id\": 1,\n    \"name\": \"Deck Name\",\n    \"description\": \"Deck description\",\n    \"userId\": \"user-id\",\n    \"createdAt\": 1640995200000\n  }\n]\n```\n\n#### POST `/api/decks`\nCreate a new flashcard deck.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"name\": \"New Deck\",\n  \"description\": \"Deck description\"\n}\n```\n\n#### GET `/api/decks/:deckId/flashcards`\nGet all flashcards in a specific deck.\n\n**Authentication:** Required\n\n**Response:**\n```json\n[\n  {\n    \"id\": 1,\n    \"question\": \"Question text\",\n    \"answer\": \"Answer text\",\n    \"deckId\": 1,\n    \"createdAt\": 1640995200000\n  }\n]\n```\n\n#### POST `/api/decks/:deckId/flashcards`\nAdd a new flashcard to a deck.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"question\": \"Question text\",\n  \"answer\": \"Answer text\"\n}\n```\n\n### Quiz Management\n#### GET `/api/quizzes`\nGet all quizzes for the authenticated user.\n\n**Authentication:** Required\n\n#### POST `/api/quizzes/generate`\nGenerate a quiz from document content.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"textContent\": \"Source material...\",\n  \"documentId\": \"uuid-string\",\n  \"quizTitle\": \"Quiz Title\",\n  \"numberOfQuestions\": 10,\n  \"aiConfig\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"apiKey\": \"user-api-key\",\n    \"model\": \"google/gemini-2.5-pro\"\n  }\n}\n```\n\n#### GET `/api/quizzes/:quizId`\nGet a specific quiz with its questions.\n\n**Authentication:** Required\n\n#### PATCH `/api/quizzes/questions/:questionId/srs`\nUpdate SRS (Spaced Repetition System) data for a quiz question.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"srs_level\": 2,\n  \"due_at\": \"2024-01-02T00:00:00.000Z\",\n  \"last_reviewed_at\": \"2024-01-01T00:00:00.000Z\",\n  \"srs_interval\": 3,\n  \"srs_ease_factor\": 2.5,\n  \"srs_repetitions\": 1,\n  \"srs_correct_streak\": 1\n}\n```\n\n## 🚨 Error Responses\n\n### Standard Error Format\n```json\n{\n  \"error\": \"Error message\",\n  \"message\": \"Detailed error description\",\n  \"timestamp\": \"2024-01-01T00:00:00.000Z\"\n}\n```\n\n### Common HTTP Status Codes\n- `200` - Success\n- `201` - Created\n- `400` - Bad Request (validation error)\n- `401` - Unauthorized (missing/invalid token)\n- `403` - Forbidden (insufficient permissions)\n- `404` - Not Found\n- `500` - Internal Server Error\n\n## 🔒 Security Considerations\n\n### API Key Handling\n- User AI provider credentials are handled ephemerally\n- No persistent storage of user API keys\n- Keys are used in-memory only for the duration of the request\n- No logging of sensitive credentials\n\n### Rate Limiting\n- Consider implementing rate limiting for production\n- Monitor API usage patterns\n- Protect against abuse and excessive usage\n\n### Input Validation\n- All inputs validated using Zod schemas\n- SQL injection prevention with parameterized queries\n- XSS prevention with proper output encoding\n\n## 📊 Response Times\n- Target response time: < 500ms for most endpoints\n- AI generation endpoints may take longer (5-30 seconds)\n- Health check should respond in < 100ms\n", "modifiedCode": "# ChewyAI API Documentation\n\n## 🌐 Base URL\n- **Development**: `http://localhost:5000/api`\n- **Production**: `https://your-replit-domain.com/api`\n\n## 🔐 Authentication\nMost endpoints require authentication using JWT tokens from Supabase Auth.\n\n### Headers\n```\nAuthorization: Bearer <jwt_token>\nContent-Type: application/json\n```\n\n## 📋 API Endpoints\n\n### Health Check\n#### GET `/api/health`\nCheck server status and health.\n\n**Response:**\n```json\n{\n  \"status\": \"healthy\",\n  \"timestamp\": \"2024-01-01T00:00:00.000Z\",\n  \"environment\": \"production\",\n  \"version\": \"1.0.0\"\n}\n```\n\n### User Credentials Management\n#### POST `/api/credentials`\nStore user's AI provider credentials securely in encrypted database.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"provider\": \"openrouter\",\n  \"apiKey\": \"user-api-key\",\n  \"baseUrl\": \"https://openrouter.ai/api/v1\",\n  \"extractionModel\": \"google/gemini-2.5-flash\",\n  \"generationModel\": \"google/gemini-2.5-pro\"\n}\n```\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"message\": \"Credentials stored securely\"\n}\n```\n\n#### GET `/api/credentials/:provider`\nGet user's stored configuration (without API key).\n\n**Authentication:** Required\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"hasCredentials\": true,\n  \"configuration\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"extractionModel\": \"google/gemini-2.5-flash\",\n    \"generationModel\": \"google/gemini-2.5-pro\"\n  }\n}\n```\n\n#### DELETE `/api/credentials/:provider`\nDelete user's stored credentials.\n\n**Authentication:** Required\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"message\": \"Credentials deleted successfully\"\n}\n```\n\n### AI Integration\n#### POST `/api/flashcards/generate`\nGenerate flashcards from text content using AI.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"textContent\": \"Your study material text...\",\n  \"documentId\": \"uuid-string\",\n  \"deckTitle\": \"Optional deck title\",\n  \"count\": 10,\n  \"customPrompt\": \"Optional custom prompt\",\n  \"provider\": \"openrouter\"\n}\n```\n\n**Note:** AI credentials are retrieved from secure storage automatically.\n\n**Response:**\n```json\n{\n  \"success\": true,\n  \"flashcards\": [\n    {\n      \"question\": \"What is the capital of France?\",\n      \"answer\": \"Paris\"\n    }\n  ],\n  \"deckId\": \"generated-deck-id\"\n}\n```\n\n#### POST `/api/extract-and-format`\nExtract and format text using AI for better readability.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"rawText\": \"Unformatted text content...\",\n  \"fileName\": \"document.pdf\",\n  \"aiSettings\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"apiKey\": \"user-api-key\",\n    \"extractionModel\": \"google/gemini-2.5-flash\"\n  }\n}\n```\n\n### Document Management\n#### POST `/api/documents`\nCreate a new document.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"title\": \"Document Title\",\n  \"content\": \"Document content...\",\n  \"type\": \"pdf|docx|txt\",\n  \"metadata\": {}\n}\n```\n\n#### POST `/api/documents/secure-upload`\nSecurely upload a document through the backend.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"fileName\": \"document.pdf\",\n  \"fileContent\": \"base64-encoded-content\",\n  \"contentType\": \"application/pdf\"\n}\n```\n\n### Flashcard Management\n#### GET `/api/decks`\nGet all flashcard decks for the authenticated user.\n\n**Authentication:** Required\n\n**Response:**\n```json\n[\n  {\n    \"id\": 1,\n    \"name\": \"Deck Name\",\n    \"description\": \"Deck description\",\n    \"userId\": \"user-id\",\n    \"createdAt\": 1640995200000\n  }\n]\n```\n\n#### POST `/api/decks`\nCreate a new flashcard deck.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"name\": \"New Deck\",\n  \"description\": \"Deck description\"\n}\n```\n\n#### GET `/api/decks/:deckId/flashcards`\nGet all flashcards in a specific deck.\n\n**Authentication:** Required\n\n**Response:**\n```json\n[\n  {\n    \"id\": 1,\n    \"question\": \"Question text\",\n    \"answer\": \"Answer text\",\n    \"deckId\": 1,\n    \"createdAt\": 1640995200000\n  }\n]\n```\n\n#### POST `/api/decks/:deckId/flashcards`\nAdd a new flashcard to a deck.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"question\": \"Question text\",\n  \"answer\": \"Answer text\"\n}\n```\n\n### Quiz Management\n#### GET `/api/quizzes`\nGet all quizzes for the authenticated user.\n\n**Authentication:** Required\n\n#### POST `/api/quizzes/generate`\nGenerate a quiz from document content.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"textContent\": \"Source material...\",\n  \"documentId\": \"uuid-string\",\n  \"quizTitle\": \"Quiz Title\",\n  \"numberOfQuestions\": 10,\n  \"aiConfig\": {\n    \"provider\": \"openrouter\",\n    \"baseUrl\": \"https://openrouter.ai/api/v1\",\n    \"apiKey\": \"user-api-key\",\n    \"model\": \"google/gemini-2.5-pro\"\n  }\n}\n```\n\n#### GET `/api/quizzes/:quizId`\nGet a specific quiz with its questions.\n\n**Authentication:** Required\n\n#### PATCH `/api/quizzes/questions/:questionId/srs`\nUpdate SRS (Spaced Repetition System) data for a quiz question.\n\n**Authentication:** Required\n\n**Request Body:**\n```json\n{\n  \"srs_level\": 2,\n  \"due_at\": \"2024-01-02T00:00:00.000Z\",\n  \"last_reviewed_at\": \"2024-01-01T00:00:00.000Z\",\n  \"srs_interval\": 3,\n  \"srs_ease_factor\": 2.5,\n  \"srs_repetitions\": 1,\n  \"srs_correct_streak\": 1\n}\n```\n\n## 🚨 Error Responses\n\n### Standard Error Format\n```json\n{\n  \"error\": \"Error message\",\n  \"message\": \"Detailed error description\",\n  \"timestamp\": \"2024-01-01T00:00:00.000Z\"\n}\n```\n\n### Common HTTP Status Codes\n- `200` - Success\n- `201` - Created\n- `400` - Bad Request (validation error)\n- `401` - Unauthorized (missing/invalid token)\n- `403` - Forbidden (insufficient permissions)\n- `404` - Not Found\n- `500` - Internal Server Error\n\n## 🔒 Security Considerations\n\n### API Key Handling\n- User AI provider credentials are handled ephemerally\n- No persistent storage of user API keys\n- Keys are used in-memory only for the duration of the request\n- No logging of sensitive credentials\n\n### Rate Limiting\n- Consider implementing rate limiting for production\n- Monitor API usage patterns\n- Protect against abuse and excessive usage\n\n### Input Validation\n- All inputs validated using Zod schemas\n- SQL injection prevention with parameterized queries\n- XSS prevention with proper output encoding\n\n## 📊 Response Times\n- Target response time: < 500ms for most endpoints\n- AI generation endpoints may take longer (5-30 seconds)\n- Health check should respond in < 100ms\n"}