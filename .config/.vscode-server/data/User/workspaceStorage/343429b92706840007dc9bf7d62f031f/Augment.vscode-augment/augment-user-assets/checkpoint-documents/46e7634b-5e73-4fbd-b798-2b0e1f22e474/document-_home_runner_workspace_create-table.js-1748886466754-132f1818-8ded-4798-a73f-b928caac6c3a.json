{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "create-table.js"}, "modifiedCode": "#!/usr/bin/env node\n\n// <PERSON><PERSON><PERSON> to create the user_ai_credentials table\nimport { createClient } from '@supabase/supabase-js';\n\n// Environment variables\nconst SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\n\n// Create Supabase client\nconst supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);\n\nconst createTableSQL = `\n-- Create table for securely storing user AI provider credentials\nCREATE TABLE IF NOT EXISTS user_ai_credentials (\n    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n    provider VARCHAR(50) NOT NULL,\n    encrypted_api_key TEXT NOT NULL,\n    encryption_iv VARCHAR(32) NOT NULL,\n    encryption_tag VARCHAR(32) NOT NULL,\n    base_url TEXT NOT NULL,\n    extraction_model VARCHAR(100),\n    generation_model VARCHAR(100),\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    \n    -- Ensure one credential set per user per provider\n    UNIQUE(user_id, provider)\n);\n\n-- Enable Row Level Security\nALTER TABLE user_ai_credentials ENABLE ROW LEVEL SECURITY;\n\n-- Create RLS policies\n-- Users can only access their own credentials\nCREATE POLICY \"Users can view own AI credentials\" ON user_ai_credentials\n    FOR SELECT USING (auth.uid() = user_id);\n\nCREATE POLICY \"Users can insert own AI credentials\" ON user_ai_credentials\n    FOR INSERT WITH CHECK (auth.uid() = user_id);\n\nCREATE POLICY \"Users can update own AI credentials\" ON user_ai_credentials\n    FOR UPDATE USING (auth.uid() = user_id);\n\nCREATE POLICY \"Users can delete own AI credentials\" ON user_ai_credentials\n    FOR DELETE USING (auth.uid() = user_id);\n\n-- Create indexes for performance\nCREATE INDEX IF NOT EXISTS idx_user_ai_credentials_user_id ON user_ai_credentials(user_id);\nCREATE INDEX IF NOT EXISTS idx_user_ai_credentials_provider ON user_ai_credentials(provider);\nCREATE INDEX IF NOT EXISTS idx_user_ai_credentials_user_provider ON user_ai_credentials(user_id, provider);\n`;\n\nasync function createTable() {\n  console.log('🗄️ Creating user_ai_credentials table...');\n  \n  try {\n    // Split the SQL into individual statements and execute them\n    const statements = createTableSQL\n      .split(';')\n      .map(stmt => stmt.trim())\n      .filter(stmt => stmt.length > 0);\n    \n    for (const statement of statements) {\n      console.log('Executing:', statement.substring(0, 50) + '...');\n      \n      const { error } = await supabase.rpc('exec_sql', { \n        sql: statement + ';' \n      });\n      \n      if (error) {\n        console.error('❌ SQL Error:', error);\n        return false;\n      }\n    }\n    \n    console.log('✅ Table created successfully!');\n    \n    // Test if the table exists now\n    console.log('🔍 Verifying table creation...');\n    const { data: tableData, error: tableError } = await supabase\n      .from('user_ai_credentials')\n      .select('id')\n      .limit(1);\n    \n    if (tableError) {\n      console.error('❌ Table verification failed:', tableError);\n      return false;\n    }\n    \n    console.log('✅ Table verified successfully!');\n    return true;\n    \n  } catch (error) {\n    console.error('❌ Error creating table:', error);\n    return false;\n  }\n}\n\nasync function main() {\n  console.log('🚀 Starting table creation...\\n');\n  \n  const success = await createTable();\n  \n  if (success) {\n    console.log('\\n🎉 Table creation completed successfully!');\n    console.log('The user_ai_credentials table is now ready for use.');\n  } else {\n    console.log('\\n❌ Table creation failed. Please check the errors above.');\n  }\n}\n\nmain().catch(console.error);\n"}