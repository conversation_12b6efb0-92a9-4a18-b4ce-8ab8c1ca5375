{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-credentials.js"}, "originalCode": "#!/usr/bin/env node\n\n// Test script to verify credentials API functionality\nimport { createClient } from '@supabase/supabase-js';\nimport crypto from 'crypto';\n\n// Environment variables\nconst SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\n\n// Create Supabase client\nconst supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);\n\n// Encryption setup\nconst ENCRYPTION_KEY = 'default-key-change-in-production-32-chars';\nconst ALGORITHM = 'aes-256-cbc';\n\nconst getEncryptionKey = () => {\n  return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);\n};\n\nfunction encryptApiKey(apiKey) {\n  const iv = crypto.randomBytes(16);\n  const key = getEncryptionKey();\n  const cipher = crypto.createCipher(ALGORITHM, key);\n  \n  let encrypted = cipher.update(apiKey, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n  \n  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity\n  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');\n  \n  return {\n    encrypted,\n    iv: iv.toString('hex'),\n    tag\n  };\n}\n\nfunction decryptApiKey(encryptedData) {\n  const iv = Buffer.from(encryptedData.iv, 'hex');\n  const key = getEncryptionKey();\n  \n  // Verify integrity using HMAC\n  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');\n  if (expectedTag !== encryptedData.tag) {\n    throw new Error('Data integrity check failed');\n  }\n  \n  const decipher = crypto.createDecipher(ALGORITHM, key);\n  \n  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n  \n  return decrypted;\n}\n\nasync function testEncryption() {\n  console.log('🔐 Testing encryption/decryption...');\n  \n  const testApiKey = 'sk-test-api-key-12345';\n  console.log('Original API key:', testApiKey);\n  \n  try {\n    const encrypted = encryptApiKey(testApiKey);\n    console.log('Encrypted data:', encrypted);\n    \n    const decrypted = decryptApiKey(encrypted);\n    console.log('Decrypted API key:', decrypted);\n    \n    if (testApiKey === decrypted) {\n      console.log('✅ Encryption/decryption test passed!');\n      return true;\n    } else {\n      console.log('❌ Encryption/decryption test failed!');\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ Encryption test error:', error);\n    return false;\n  }\n}\n\nasync function testDatabaseConnection() {\n  console.log('🗄️ Testing database connection...');\n\n  try {\n    const { data, error } = await supabase\n      .from('user_ai_credentials')\n      .select('id')\n      .limit(1);\n\n    if (error) {\n      console.error('❌ Database connection error:', error);\n      return false;\n    }\n\n    console.log('✅ Database connection successful!');\n    return true;\n  } catch (error) {\n    console.error('❌ Database test error:', error);\n    return false;\n  }\n}\n\nasync function testCredentialsStorage() {\n  console.log('💾 Testing credentials storage...');\n  \n  const testUserId = '00000000-0000-0000-0000-000000000001';\n  const testProvider = 'test-provider';\n  const testApiKey = 'sk-test-key-12345';\n  \n  try {\n    // Encrypt the API key\n    const encryptedKey = encryptApiKey(testApiKey);\n    \n    // Store in database\n    const { error: insertError } = await supabase\n      .from('user_ai_credentials')\n      .upsert({\n        user_id: testUserId,\n        provider: testProvider,\n        encrypted_api_key: encryptedKey.encrypted,\n        encryption_iv: encryptedKey.iv,\n        encryption_tag: encryptedKey.tag,\n        base_url: 'https://test.example.com',\n        extraction_model: 'test-extraction-model',\n        generation_model: 'test-generation-model',\n        updated_at: new Date().toISOString()\n      }, {\n        onConflict: 'user_id,provider'\n      });\n\n    if (insertError) {\n      console.error('❌ Insert error:', insertError);\n      return false;\n    }\n    \n    console.log('✅ Credentials stored successfully!');\n    \n    // Retrieve and decrypt\n    const { data, error: selectError } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('user_id', testUserId)\n      .eq('provider', testProvider)\n      .single();\n\n    if (selectError) {\n      console.error('❌ Select error:', selectError);\n      return false;\n    }\n    \n    const decryptedApiKey = decryptApiKey({\n      encrypted: data.encrypted_api_key,\n      iv: data.encryption_iv,\n      tag: data.encryption_tag\n    });\n    \n    if (decryptedApiKey === testApiKey) {\n      console.log('✅ Credentials retrieval and decryption successful!');\n      \n      // Clean up test data\n      await supabase\n        .from('user_ai_credentials')\n        .delete()\n        .eq('user_id', testUserId)\n        .eq('provider', testProvider);\n      \n      return true;\n    } else {\n      console.log('❌ Decrypted API key does not match original!');\n      return false;\n    }\n    \n  } catch (error) {\n    console.error('❌ Credentials storage test error:', error);\n    return false;\n  }\n}\n\nasync function runTests() {\n  console.log('🧪 Running credentials API tests...\\n');\n  \n  const encryptionTest = await testEncryption();\n  console.log('');\n  \n  const dbTest = await testDatabaseConnection();\n  console.log('');\n  \n  const storageTest = await testCredentialsStorage();\n  console.log('');\n  \n  console.log('📊 Test Results:');\n  console.log('- Encryption/Decryption:', encryptionTest ? '✅ PASS' : '❌ FAIL');\n  console.log('- Database Connection:', dbTest ? '✅ PASS' : '❌ FAIL');\n  console.log('- Credentials Storage:', storageTest ? '✅ PASS' : '❌ FAIL');\n  \n  if (encryptionTest && dbTest && storageTest) {\n    console.log('\\n🎉 All tests passed! The credentials API should work correctly.');\n  } else {\n    console.log('\\n❌ Some tests failed. Please check the errors above.');\n  }\n}\n\nrunTests().catch(console.error);\n", "modifiedCode": "#!/usr/bin/env node\n\n// Test script to verify credentials API functionality\nimport { createClient } from '@supabase/supabase-js';\nimport crypto from 'crypto';\n\n// Environment variables\nconst SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\n\n// Create Supabase client\nconst supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);\n\n// Encryption setup\nconst ENCRYPTION_KEY = 'default-key-change-in-production-32-chars';\nconst ALGORITHM = 'aes-256-cbc';\n\nconst getEncryptionKey = () => {\n  return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);\n};\n\nfunction encryptApiKey(apiKey) {\n  const iv = crypto.randomBytes(16);\n  const key = getEncryptionKey();\n  const cipher = crypto.createCipher(ALGORITHM, key);\n  \n  let encrypted = cipher.update(apiKey, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n  \n  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity\n  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');\n  \n  return {\n    encrypted,\n    iv: iv.toString('hex'),\n    tag\n  };\n}\n\nfunction decryptApiKey(encryptedData) {\n  const iv = Buffer.from(encryptedData.iv, 'hex');\n  const key = getEncryptionKey();\n  \n  // Verify integrity using HMAC\n  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');\n  if (expectedTag !== encryptedData.tag) {\n    throw new Error('Data integrity check failed');\n  }\n  \n  const decipher = crypto.createDecipher(ALGORITHM, key);\n  \n  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n  \n  return decrypted;\n}\n\nasync function testEncryption() {\n  console.log('🔐 Testing encryption/decryption...');\n  \n  const testApiKey = 'sk-test-api-key-12345';\n  console.log('Original API key:', testApiKey);\n  \n  try {\n    const encrypted = encryptApiKey(testApiKey);\n    console.log('Encrypted data:', encrypted);\n    \n    const decrypted = decryptApiKey(encrypted);\n    console.log('Decrypted API key:', decrypted);\n    \n    if (testApiKey === decrypted) {\n      console.log('✅ Encryption/decryption test passed!');\n      return true;\n    } else {\n      console.log('❌ Encryption/decryption test failed!');\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ Encryption test error:', error);\n    return false;\n  }\n}\n\nasync function testDatabaseConnection() {\n  console.log('🗄️ Testing database connection...');\n\n  try {\n    const { data, error } = await supabase\n      .from('user_ai_credentials')\n      .select('id')\n      .limit(1);\n\n    if (error) {\n      console.error('❌ Database connection error:', error);\n      return false;\n    }\n\n    console.log('✅ Database connection successful!');\n    return true;\n  } catch (error) {\n    console.error('❌ Database test error:', error);\n    return false;\n  }\n}\n\nasync function testCredentialsStorage() {\n  console.log('💾 Testing credentials storage...');\n\n  const testUserId = '21716172-c5ec-4011-97e5-a0dcc2bfc27e'; // Real user ID from database\n  const testProvider = 'test-provider';\n  const testApiKey = 'sk-test-key-12345';\n  \n  try {\n    // Encrypt the API key\n    const encryptedKey = encryptApiKey(testApiKey);\n    \n    // Store in database\n    const { error: insertError } = await supabase\n      .from('user_ai_credentials')\n      .upsert({\n        user_id: testUserId,\n        provider: testProvider,\n        encrypted_api_key: encryptedKey.encrypted,\n        encryption_iv: encryptedKey.iv,\n        encryption_tag: encryptedKey.tag,\n        base_url: 'https://test.example.com',\n        extraction_model: 'test-extraction-model',\n        generation_model: 'test-generation-model',\n        updated_at: new Date().toISOString()\n      }, {\n        onConflict: 'user_id,provider'\n      });\n\n    if (insertError) {\n      console.error('❌ Insert error:', insertError);\n      return false;\n    }\n    \n    console.log('✅ Credentials stored successfully!');\n    \n    // Retrieve and decrypt\n    const { data, error: selectError } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('user_id', testUserId)\n      .eq('provider', testProvider)\n      .single();\n\n    if (selectError) {\n      console.error('❌ Select error:', selectError);\n      return false;\n    }\n    \n    const decryptedApiKey = decryptApiKey({\n      encrypted: data.encrypted_api_key,\n      iv: data.encryption_iv,\n      tag: data.encryption_tag\n    });\n    \n    if (decryptedApiKey === testApiKey) {\n      console.log('✅ Credentials retrieval and decryption successful!');\n      \n      // Clean up test data\n      await supabase\n        .from('user_ai_credentials')\n        .delete()\n        .eq('user_id', testUserId)\n        .eq('provider', testProvider);\n      \n      return true;\n    } else {\n      console.log('❌ Decrypted API key does not match original!');\n      return false;\n    }\n    \n  } catch (error) {\n    console.error('❌ Credentials storage test error:', error);\n    return false;\n  }\n}\n\nasync function runTests() {\n  console.log('🧪 Running credentials API tests...\\n');\n  \n  const encryptionTest = await testEncryption();\n  console.log('');\n  \n  const dbTest = await testDatabaseConnection();\n  console.log('');\n  \n  const storageTest = await testCredentialsStorage();\n  console.log('');\n  \n  console.log('📊 Test Results:');\n  console.log('- Encryption/Decryption:', encryptionTest ? '✅ PASS' : '❌ FAIL');\n  console.log('- Database Connection:', dbTest ? '✅ PASS' : '❌ FAIL');\n  console.log('- Credentials Storage:', storageTest ? '✅ PASS' : '❌ FAIL');\n  \n  if (encryptionTest && dbTest && storageTest) {\n    console.log('\\n🎉 All tests passed! The credentials API should work correctly.');\n  } else {\n    console.log('\\n❌ Some tests failed. Please check the errors above.');\n  }\n}\n\nrunTests().catch(console.error);\n"}