{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/quizRoutes.ts"}, "originalCode": "import { Hono, Context, Next } from \"hono\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\nimport express, { Request, Response } from \"express\";\nimport {\n  GenerateQuizParams,\n  GenerateQuizResponse,\n  QuizQuestion as SharedQuizQuestion,\n  QuizQuestionOption,\n  AIProviderConfig,\n  QuizQuestionBatchInsertPayload,\n  Quiz,\n} from \"../../shared/types/quiz\";\nimport {\n  UserCompletionInsert,\n  QuizCompletionData,\n  CompletionStats,\n  CompletionFilters,\n} from \"../../shared/types/completion\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport { getEphemeralUserCredentials } from \"../middleware/apiKeyStorage\";\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Fix for Node.js 18+ fetch duplex issue\nif (typeof globalThis.fetch === \"undefined\") {\n  globalThis.fetch = require(\"node-fetch\");\n}\n\ntype AppVariables = {\n  supabase: SupabaseClient;\n  user: { id: string };\n};\n\nconsole.log(\"quizRoutes.ts: Module loaded\"); // Added for debugging\n\nconst quizRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nquizRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for quizRoutes\n// This ensures that any unhandled error within quizRoutes still returns a JSON response.\nquizRoutes.onError((err, c) => {\n  console.error(\"Error in quizRoutes:\", err);\n  // Optionally include stack in development\n  // const stack = process.env.NODE_ENV === 'development' ? err.stack : undefined;\n  return c.json(\n    {\n      error: \"An unexpected error occurred in quiz routes.\",\n      message: err.message,\n      // stack\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`quizRoutes: Auth middleware triggered for path: ${c.req.path}`); // Log 1\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\n      \"quizRoutes: Auth Error - Authorization header missing or malformed.\"\n    ); // Log E1\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"quizRoutes: Auth Error - Token missing after Bearer split.\"); // Log E2\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  const supabase = c.get(\"supabase\");\n  if (!supabase) {\n    console.error(\n      \"quizRoutes: Auth Error - Supabase client not found in request context.\"\n    ); // Log E3\n    return c.json(\n      { error: \"Server configuration error: Supabase client missing\" },\n      500\n    );\n  }\n\n  console.log(\n    \"quizRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\"\n  ); // Log 2\n  try {\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\n        `quizRoutes: Auth Error - supabase.auth.getUser returned an error: ${getUserError.message}`,\n        getUserError\n      ); // Log E4\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return c.json(\n          {\n            error: \"Unauthorized: Invalid token\",\n            details: getUserError.message,\n          },\n          401\n        );\n      }\n      return c.json(\n        {\n          error: \"Server error validating token\",\n          details: getUserError.message,\n        },\n        500\n      );\n    }\n\n    const user = data?.user; // Check data.user specifically\n\n    if (!user) {\n      console.error(\n        \"quizRoutes: Auth Error - No user object found in supabase.auth.getUser response (data.user is null/undefined).\"\n      ); // Log E5\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    console.log(\n      `quizRoutes: Auth Success - User ${user.id} authenticated. Calling next().`\n    ); // Log 4\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\n      \"quizRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\",\n      err.message,\n      err.stack\n    ); // Log E6\n    // In Hono, if an error occurs after next() has been called and potentially a response sent by a later handler,\n    // this return might not execute or might cause issues if a response was already committed.\n    // However, for errors *before* or *during* c.json(), this is fine.\n    // If next() itself throws and doesn't handle its own response, this catch block will execute.\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nquizRoutes.use(\"*\", authMiddleware);\n\ninterface AIPromptPayload {\n  model: string;\n  messages: {\n    role: \"user\" | \"assistant\" | \"system\";\n    content: string;\n  }[];\n  temperature?: number;\n  max_tokens?: number;\n  // Add other parameters as supported by OpenRouter and the model\n}\n\ninterface AIResponseFormat {\n  questions: AIGeneratedQuestion[];\n}\ninterface AIGeneratedQuestion {\n  questionText: string;\n  type:\n    | \"multiple_choice\"\n    | \"select_all_that_apply\"\n    | \"true_false\"\n    | \"short_answer\";\n  options?: Array<{ text: string; is_correct?: boolean }>;\n  correctAnswer?: string | boolean | string[];\n  explanation?: string;\n}\n\n// Helper function to call AI (OpenRouter)\nasync function generateQuestionsFromAI(\n  textContent: string,\n  numberOfQuestions: number,\n  aiConfig: AIProviderConfig,\n  quizTitle: string,\n  customPromptText?: string,\n  requestedQuestionTypes?: string[]\n): Promise<AIResponseFormat | null> {\n  const { apiKey, baseUrl, model } = aiConfig;\n\n  // Validate AI configuration\n  if (!apiKey || !apiKey.trim()) {\n    console.error(\"AI API key is missing or empty\");\n    throw new Error(\n      \"AI API key is required but not provided. Please configure your AI provider settings.\"\n    );\n  }\n\n  if (!baseUrl || !baseUrl.trim()) {\n    console.error(\"AI base URL is missing or empty\");\n    throw new Error(\n      \"AI base URL is required but not provided. Please configure your AI provider settings.\"\n    );\n  }\n\n  console.log(\n    `Generating ${numberOfQuestions} questions using model: ${\n      model || \"google/gemini-2.5-pro-preview\"\n    }`\n  );\n\n  const questionTypesString =\n    requestedQuestionTypes && requestedQuestionTypes.length > 0\n      ? requestedQuestionTypes.join(\", \")\n      : \"multiple_choice, true_false, short_answer, select_all_that_apply\"; // Default if not specified\n\n  console.log(`AI Generation: Requested question types: [${questionTypesString}]`);\n\n  const basePrompt = `Based on the following text, generate a quiz titled \"${quizTitle}\" with exactly ${numberOfQuestions} questions.\n\nIMPORTANT: You MUST generate questions ONLY of the following types: ${questionTypesString}\nDo NOT generate questions of any other types. Every single question must be one of these exact types: ${questionTypesString}.\n\nFor each question, provide:\n1. \"questionText\": The text of the question (string).\n2. \"type\": The type of question (string, must be one of \"multiple_choice\", \"select_all_that_apply\", \"true_false\", \"short_answer\").\n3. \"options\": (ONLY for \"multiple_choice\" and \"select_all_that_apply\") An array of 3 to 5 option objects. Each option object must have a \"text\" (string) key. For \"multiple_choice\", one option should have \"is_correct\": true. For \"select_all_that_apply\", one or more options should have \"is_correct\": true. Other options should have \"is_correct\": false or omit it.\n4. \"correctAnswer\":\n   - For \"true_false\": The string \"true\" or \"false\".\n   - For \"short_answer\": The concise correct answer string.\n   - For \"multiple_choice\": The text of the single correct option. (This is redundant if \"is_correct\" is in options, but good for AI to provide).\n   - For \"select_all_that_apply\": An array of strings, where each string is the text of a correct option. (Also redundant if \"is_correct\" is in options).\n5. \"explanation\": (Optional) A brief explanation for why the answer is correct (string).\n\nReturn the output as a valid JSON object with a single key \"questions\". The value of \"questions\" should be an array of question objects adhering to the structure described above.`;\n\n  const customInstructions = customPromptText\n    ? `\\n\\nAdditional instructions for question generation: ${customPromptText}`\n    : \"\";\n  const prompt = `${basePrompt}${customInstructions}\\n\\nText:\\n\"\"\"\\n${textContent.substring(\n    0,\n    15000\n  )}\\n\"\"\"\\n\\nOutput only the JSON object.`;\n\n  console.log(\"AI Prompt being sent:\", prompt.substring(0, 500) + \"...\"); // Log part of the prompt\n\n  const payload: AIPromptPayload = {\n    model: model || \"google/gemini-2.5-pro-preview\", // Default to gemini-2.5-pro-preview as per OpenRouter\n    messages: [{ role: \"user\", content: prompt }],\n    // temperature: 0.7, // Adjust as needed\n    // max_tokens: 2000, // Adjust as needed, ensure it's enough for the number of questions\n  };\n\n  try {\n    console.log(`Making AI API call to: ${baseUrl}/chat/completions`);\n    const response = await fetch(`${baseUrl}/chat/completions`, {\n      method: \"POST\",\n      headers: {\n        Authorization: `Bearer ${apiKey}`,\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(payload),\n      duplex: \"half\", // Required for Node.js 18+ when sending a body\n    } as NodeRequestInit);\n\n    if (!response.ok) {\n      console.error(`AI API error: ${response.status} ${response.statusText}`);\n      const errorText = await response.text();\n      console.error(\"AI API error response:\", errorText);\n\n      // Provide more specific error messages based on status code\n      if (response.status === 401) {\n        throw new Error(\n          \"Unauthorized: Invalid API key. Please check your AI provider API key.\"\n        );\n      } else if (response.status === 403) {\n        throw new Error(\n          \"Forbidden: API key does not have permission for this operation.\"\n        );\n      } else if (response.status === 429) {\n        throw new Error(\n          \"Rate limited: Too many requests. Please try again later.\"\n        );\n      } else {\n        throw new Error(`AI provider error (${response.status}): ${errorText}`);\n      }\n    }\n\n    const responseData = await response.json();\n\n    if (\n      responseData &&\n      responseData.choices &&\n      responseData.choices[0] &&\n      responseData.choices[0].message\n    ) {\n      const content = responseData.choices[0].message.content;\n      console.log(\"AI response received, parsing JSON...\");\n\n      // The AI might return the JSON as a string, sometimes wrapped in markdown ```json ... ```\n      const jsonMatch = content.match(/```json\\n([\\s\\S]*?)\\n```/);\n      const jsonString = jsonMatch ? jsonMatch[1] : content;\n      try {\n        const result = JSON.parse(jsonString) as AIResponseFormat;\n        console.log(\n          `Successfully parsed ${\n            result.questions?.length || 0\n          } questions from AI response`\n        );\n\n        // Filter questions to only include the requested types\n        if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {\n          const originalCount = result.questions.length;\n          result.questions = result.questions.filter(q =>\n            requestedQuestionTypes.includes(q.type)\n          );\n          const filteredCount = result.questions.length;\n\n          if (originalCount !== filteredCount) {\n            console.log(\n              `AI Generation: Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +\n              `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(\", \")}]`\n            );\n          }\n        }\n\n        return result;\n      } catch (parseError) {\n        console.error(\n          \"Error parsing AI response JSON:\",\n          parseError,\n          \"Raw content:\",\n          content\n        );\n        // Attempt to find JSON within a potentially messy string\n        const relaxedJsonMatch = content.match(/{[\\s\\S]*}/);\n        if (relaxedJsonMatch && relaxedJsonMatch[0]) {\n          try {\n            const result = JSON.parse(relaxedJsonMatch[0]) as AIResponseFormat;\n            console.log(\n              `Successfully parsed ${\n                result.questions?.length || 0\n              } questions from relaxed JSON match`\n            );\n\n            // Filter questions to only include the requested types\n            if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {\n              const originalCount = result.questions.length;\n              result.questions = result.questions.filter(q =>\n                requestedQuestionTypes.includes(q.type)\n              );\n              const filteredCount = result.questions.length;\n\n              if (originalCount !== filteredCount) {\n                console.log(\n                  `AI Generation (relaxed): Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +\n                  `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(\", \")}]`\n                );\n              }\n            }\n\n            return result;\n          } catch (relaxedParseError) {\n            console.error(\n              \"Error parsing relaxed AI response JSON:\",\n              relaxedParseError,\n              \"Raw content:\",\n              content\n            );\n            throw new Error(\n              \"Failed to parse AI response as valid JSON. The AI response format was unexpected.\"\n            );\n          }\n        }\n        throw new Error(\n          \"Failed to parse AI response as valid JSON. The AI response format was unexpected.\"\n        );\n      }\n    } else {\n      console.error(\"Unexpected AI response structure:\", responseData);\n      throw new Error(\n        \"AI response did not contain expected message structure.\"\n      );\n    }\n  } catch (error) {\n    console.error(\"Error calling AI API:\", error);\n    // Re-throw the error instead of returning null so the calling function can handle it properly\n    throw error;\n  }\n}\n\n// POST /api/quizzes/generate\nquizRoutes.post(\n  \"/generate\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    try {\n      console.log(\n        `[quizRoutes] POST /generate handler reached. Path: ${c.req.path}, Method: ${c.req.method}`\n      );\n      const requestBody = await c.req.json();\n      console.log(\n        \"Quiz generation request body:\",\n        JSON.stringify(requestBody, null, 2)\n      );\n\n      // Handle both payload formats:\n      // Format 1 (UploadSection): { textContent, documentId, quizTitle, numberOfQuestions, aiConfig }\n      // Format 2 (CreateQuizForm): { documentIds, quizName, quizDescription, generationOptions }\n\n      let textContent: string;\n      let documentId: string | null;\n      let quizTitle: string;\n      let numberOfQuestions: number;\n      let aiConfig: AIProviderConfig;\n      let generationOptions: GenerateQuizParams[\"generationOptions\"];\n      let customPrompt: string | undefined;\n\n      if (requestBody.textContent && requestBody.quizTitle) {\n        // Format 1 (UploadSection) - Legacy format\n        textContent = requestBody.textContent;\n        documentId = requestBody.documentId || null;\n        quizTitle = requestBody.quizTitle;\n        numberOfQuestions = requestBody.numberOfQuestions;\n        customPrompt = requestBody.customPrompt;\n        // Infer generationOptions if not explicitly provided in this format, or use what's passed\n        generationOptions = requestBody.generationOptions || {\n          numberOfQuestions: numberOfQuestions,\n          questionTypes: requestBody.questionTypes || [\"multiple_choice\"], // Use provided questionTypes or default\n        };\n\n        // Get AI config from stored user credentials\n        const credentialsResult = await getEphemeralUserCredentials(user.id, 'OpenRouter');\n\n        if (!credentialsResult.success || !credentialsResult.credentials) {\n          return c.json(\n            {\n              error: \"AI Provider not configured. Please configure your AI settings first.\",\n              details: \"No stored credentials found for OpenRouter provider\"\n            },\n            400\n          );\n        }\n\n        aiConfig = {\n          apiKey: credentialsResult.credentials.apiKey,\n          baseUrl: credentialsResult.credentials.baseUrl,\n          model: credentialsResult.credentials.generationModel,\n        };\n      } else if (requestBody.textContent && requestBody.quizName && requestBody.generationOptions) {\n        // Format 1.5 (UploadSection) - New format with textContent and generationOptions\n        textContent = requestBody.textContent;\n        documentId = requestBody.documentId || null;\n        quizTitle = requestBody.quizName; // This is correct\n        // Ensure generationOptions is correctly picked up from requestBody for Format 1.5\n        generationOptions = requestBody.generationOptions; // This should contain numberOfQuestions and questionTypes\n        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;\n        customPrompt = requestBody.customPrompt;\n\n        // Get AI config from stored user credentials\n        const credentialsResult = await getEphemeralUserCredentials(user.id, 'OpenRouter');\n\n        if (!credentialsResult.success || !credentialsResult.credentials) {\n          return c.json(\n            {\n              error: \"AI Provider not configured. Please configure your AI settings first.\",\n              details: \"No stored credentials found for OpenRouter provider\"\n            },\n            400\n          );\n        }\n\n        aiConfig = {\n          apiKey: credentialsResult.credentials.apiKey,\n          baseUrl: credentialsResult.credentials.baseUrl,\n          model: credentialsResult.credentials.generationModel,\n        };\n      } else if (requestBody.quizName && requestBody.generationOptions) {\n        // Format 2 (CreateQuizForm) - need to get document content\n        const documentIds = requestBody.documentIds || [];\n        if (documentIds.length === 0) {\n          return c.json(\n            { error: \"No document IDs provided for AI generation\" },\n            400\n          );\n        }\n\n        // Handle multiple documents\n        let combinedTextContent = \"\";\n        for (const docId of documentIds) {\n          const { data: document, error: docError } = await supabase\n            .from(\"study_documents\") // Fetch file_name for context\n            .select(\"extracted_text_path, user_id, file_name\")\n            .eq(\"id\", docId)\n            .single();\n\n          if (docError || !document) {\n            return c.json(\n              { error: `Document not found for ID: ${docId}` },\n              404\n            );\n          }\n          if (document.user_id !== user.id) {\n            return c.json(\n              {\n                error: `Forbidden: Document ${docId} does not belong to this user`,\n              },\n              403\n            );\n          }\n          if (!document.extracted_text_path) {\n            return c.json(\n              {\n                error: `Document ${docId} (${document.file_name}) has no extracted text path.`,\n              },\n              400\n            );\n          }\n\n          const { data: fileData, error: downloadError } =\n            await supabase.storage\n              .from(\"study_materials\") // Assuming 'study_materials' is the correct bucket\n              .download(document.extracted_text_path);\n\n          if (downloadError || !fileData) {\n            console.error(\n              `Error downloading content for document ${docId}:`,\n              downloadError\n            );\n            return c.json(\n              {\n                error: `Failed to download content for document ${docId} (${document.file_name})`,\n                details: downloadError?.message,\n              },\n              500\n            );\n          }\n\n          const docTextContent = await fileData.text();\n          combinedTextContent += `Content from ${document.file_name}:\\n${docTextContent}\\n\\n`;\n        }\n        textContent = combinedTextContent.trim();\n        documentId = documentIds.join(\",\"); // Store comma-separated IDs or handle differently\n\n        quizTitle = requestBody.quizName;\n        generationOptions = requestBody.generationOptions; // FIX: Assign the generationOptions object\n        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;\n        customPrompt = requestBody.generationOptions.customPrompt;\n\n        // Get AI config from stored user credentials\n        const credentialsResult = await getEphemeralUserCredentials(user.id, 'OpenRouter');\n\n        if (!credentialsResult.success || !credentialsResult.credentials) {\n          return c.json(\n            {\n              error: \"AI Provider not configured. Please configure your AI settings first.\",\n              details: \"No stored credentials found for OpenRouter provider\"\n            },\n            400\n          );\n        }\n\n        aiConfig = {\n          apiKey: credentialsResult.credentials.apiKey,\n          baseUrl: credentialsResult.credentials.baseUrl,\n          model: credentialsResult.credentials.generationModel,\n        };\n      } else {\n        return c.json(\n          {\n            error:\n              \"Invalid request format. Expected either { textContent, quizTitle, numberOfQuestions } or { textContent, quizName, generationOptions } or { quizName, documentIds, generationOptions }\",\n          },\n          400\n        );\n      }\n\n      if (!textContent || !quizTitle || !numberOfQuestions) {\n        return c.json( // This check might be redundant if generationOptions is always present for Format 2\n          {\n            error: \"Missing required fields after processing request\",\n          },\n          400\n        );\n      }\n\n      // Debug logging for generationOptions\n      console.log(\"Backend: Received generationOptions:\", JSON.stringify(generationOptions, null, 2));\n      console.log(\"Backend: Question types received:\", generationOptions?.questionTypes);\n      console.log(\"Backend: Question types length:\", generationOptions?.questionTypes?.length);\n\n      // Validate that question types are provided and not empty\n      if (!generationOptions?.questionTypes || generationOptions.questionTypes.length === 0) {\n        console.error(\"Backend: Validation failed - no question types provided\");\n        return c.json(\n          {\n            error: \"At least one question type must be selected for AI generation\",\n          },\n          400\n        );\n      }\n\n      // Document ownership already verified above for Format 2\n      // For Format 1, verify document ownership only if we need to fetch content from the database\n      // If textContent is already provided, we don't need to verify the document exists\n      if (documentId && !requestBody.textContent) {\n        const { data: documentOwner, error: docError } = await supabase\n          .from(\"study_documents\")\n          .select(\"user_id\")\n          .eq(\"id\", documentId)\n          .single();\n\n        if (docError || !documentOwner) {\n          return c.json(\n            {\n              error: `Failed to verify document ownership or document not found for ID: ${documentId}`,\n            },\n            404\n          );\n        }\n\n        if (documentOwner.user_id !== user.id) {\n          return c.json(\n            {\n              error: `Forbidden: Document ${documentId} does not belong to this user.`,\n            },\n            403\n          );\n        }\n      }\n\n      console.log(\"🤖 Generating questions from AI...\");\n      console.log(`Quiz Generation: Requested ${numberOfQuestions} questions of types: [${generationOptions?.questionTypes?.join(\", \") || \"default\"}]`);\n\n      const aiGeneratedData = await generateQuestionsFromAI(\n        textContent,\n        numberOfQuestions,\n        aiConfig,\n        quizTitle,\n        customPrompt,\n        generationOptions?.questionTypes\n      );\n\n      if (\n        !aiGeneratedData ||\n        !aiGeneratedData.questions ||\n        aiGeneratedData.questions.length === 0\n      ) {\n        return c.json(\n          {\n            error:\n              \"Failed to generate questions from AI or AI returned no questions.\",\n          },\n          500\n        );\n      }\n\n      // Log what types were actually generated\n      const generatedTypes = aiGeneratedData.questions.map(q => q.type);\n      const typeCounts = generatedTypes.reduce((acc, type) => {\n        acc[type] = (acc[type] || 0) + 1;\n        return acc;\n      }, {} as Record<string, number>);\n      console.log(`Quiz Generation: AI generated ${aiGeneratedData.questions.length} questions with types:`, typeCounts);\n\n      // Ensure the number of questions matches the request, or handle discrepancies\n      const generatedQs = aiGeneratedData.questions.slice(0, numberOfQuestions);\n      if (generatedQs.length < numberOfQuestions) {\n        console.warn(\n          `AI generated ${generatedQs.length} questions, but ${numberOfQuestions} were requested.`\n        );\n        // Decide if this is an error or if we proceed with fewer questions\n        if (generatedQs.length === 0) {\n          return c.json({ error: \"AI generated no usable questions.\" }, 500);\n        }\n      }\n\n      console.log(\"💾 Saving quiz to database...\");\n\n      let finalStudyDocumentId: string | null = null;\n      if (documentId) {\n        // If documentId might be a comma-separated list from multiple docs, take the first one for linking.\n        // Or, decide on a strategy for linking quizzes to multiple documents (e.g., a join table or array field).\n        // For simplicity, let's assume we link to the first document if multiple are provided.\n        const firstDocId = documentId.split(\",\")[0];\n        if (firstDocId) {\n          const uuidRegex =\n            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;\n          if (uuidRegex.test(firstDocId)) {\n            const { data: existingDoc, error: docCheckError } = await supabase\n              .from(\"study_documents\")\n              .select(\"id\")\n              .eq(\"id\", firstDocId)\n              .eq(\"user_id\", user.id)\n              .maybeSingle();\n\n            if (docCheckError) {\n              console.error(\n                `Error checking for document ${firstDocId}:`,\n                docCheckError.message\n              );\n            } else if (existingDoc) {\n              finalStudyDocumentId = existingDoc.id;\n            } else {\n              console.warn(\n                `Document with ID \"${firstDocId}\" not found for user \"${user.id}\". Proceeding with null study_document_id for quiz.`\n              );\n            }\n          } else {\n            console.warn(\n              `Invalid UUID format for documentId: \"${firstDocId}\". Proceeding with null for study_document_id.`\n            );\n          }\n        } else {\n          console.warn(\n            `No valid document ID found in \"${documentId}\". Proceeding with null for study_document_id.`\n          );\n        }\n      }\n\n      // Use Supabase instead of Drizzle ORM\n      let savedQuiz;\n      let quizError;\n\n      const { data: firstAttemptQuiz, error: firstAttemptError } =\n        await supabase\n          .from(\"quizzes\")\n          .insert({\n            name: quizTitle,\n            study_document_id: finalStudyDocumentId, // Use validated and existing or null ID\n            user_id: user.id,\n          })\n          .select()\n          .single();\n\n      savedQuiz = firstAttemptQuiz;\n      quizError = firstAttemptError;\n\n      if (quizError) {\n        console.error(\"Initial error saving quiz:\", quizError);\n        // Check for specific foreign key violation on document_id\n        if (\n          quizError.code === \"23503\" &&\n          quizError.message.includes(\"quizzes_document_id_fkey\") &&\n          finalStudyDocumentId !== null\n        ) {\n          console.warn(\n            `Foreign key violation for document_id: ${finalStudyDocumentId}. Retrying quiz insert with null document_id.`\n          );\n          const { data: retryQuiz, error: retryError } = await supabase\n            .from(\"quizzes\")\n            .insert({\n              name: quizTitle,\n              study_document_id: null, // Attempt with null\n              user_id: user.id,\n            })\n            .select()\n            .single();\n\n          if (retryError) {\n            console.error(\n              \"Error saving quiz on retry with null document_id:\",\n              retryError\n            );\n            // Keep the original error for the response if retry also fails\n            quizError = retryError; // Or could be firstAttemptError, depending on desired behavior\n            savedQuiz = null;\n          } else {\n            console.log(\n              \"Successfully saved quiz on retry with null document_id.\"\n            );\n            savedQuiz = retryQuiz;\n            quizError = null; // Clear error as retry was successful\n          }\n        }\n      }\n\n      if (quizError || !savedQuiz) {\n        console.error(\n          \"Final error saving quiz after potential retry:\",\n          quizError\n        );\n        return c.json({ error: \"Failed to save quiz to database.\" }, 500);\n      }\n\n      console.log(\"💾 Attempting to save quiz questions to database...\");\n\n      // Check if quiz_questions table exists by attempting to insert\n      const questionsToInsert = generatedQs.map((q) => {\n        let correctAnswerValue: string | null = null;\n        if (typeof q.correctAnswer === \"string\")\n          correctAnswerValue = q.correctAnswer;\n        else if (typeof q.correctAnswer === \"boolean\")\n          correctAnswerValue = q.correctAnswer.toString();\n        else if (Array.isArray(q.correctAnswer))\n          correctAnswerValue = JSON.stringify(q.correctAnswer);\n        return {\n          quiz_id: savedQuiz.id,\n          question_text: q.questionText,\n          type: q.type,\n          options: q.options ?? null,\n          correct_answer: correctAnswerValue,\n          explanation: q.explanation || null,\n          user_id: user.id,\n        };\n      });\n\n      let insertedQuestions = null;\n      let questionsError = null;\n\n      // Try to save questions if table exists\n      if (questionsToInsert.length > 0) {\n        try {\n          const result = await supabase\n            .from(\"quiz_questions\")\n            .insert(questionsToInsert)\n            .select();\n\n          insertedQuestions = result.data;\n          questionsError = result.error;\n        } catch (error: any) {\n          console.warn(\n            \"Quiz questions table may not exist yet:\",\n            error.message\n          );\n          questionsError = error;\n        }\n\n        if (questionsError) {\n          console.warn(\n            \"Could not save quiz questions (table may not exist):\",\n            questionsError\n          );\n          // Don't fail the entire operation - just save the quiz without questions for now\n        }\n      }\n\n      console.log(\"✅ Quiz saved successfully!\");\n\n      // Create a simplified quiz response that matches what the frontend expects\n      const fullQuiz = {\n        id: savedQuiz.id.toString(),\n        title: savedQuiz.name,\n        questions: insertedQuestions\n          ? insertedQuestions.map((q) => ({\n              id: q.id.toString(),\n              questionText: q.question_text,\n              type: q.type,\n              options: q.options,\n              correctAnswer: q.correct_answer,\n              explanation: q.explanation || undefined,\n            }))\n          : generatedQs.map((q, index) => ({\n              id: `temp_${index}`,\n              questionText: q.questionText,\n              type: q.type,\n              options: q.options,\n              correctAnswer: q.correctAnswer,\n              explanation: q.explanation || undefined,\n            })),\n      };\n\n      // Return consistent format for both request types\n      return c.json(\n        {\n          success: true,\n          quiz: fullQuiz,\n          quizId: savedQuiz.id.toString(),\n          id: savedQuiz.id.toString(),\n          name: savedQuiz.name,\n        },\n        201\n      );\n    } catch (error: any) {\n      console.error(\"❌ Error in /generate quiz route:\", error);\n      console.error(\"Error stack:\", error.stack);\n      return c.json(\n        { error: error.message || \"An unexpected error occurred\" },\n        500\n      );\n    }\n  }\n);\n\n// Route to fetch a specific quiz and its questions\nquizRoutes.get(\"/:quizId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const quizId = c.req.param(\"quizId\");\n\n  if (!quizId) {\n    return c.json({ error: \"Invalid quiz ID\" }, 400);\n  }\n\n  try {\n    // First get the quiz basic info\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .select(\"*\")\n      .eq(\"id\", quizId)\n      .eq(\"user_id\", user.id) // Ensure user owns the quiz\n      .single();\n\n    if (quizError || !quiz) {\n      console.error(\"Error fetching quiz:\", quizError);\n      return c.json({ error: \"Quiz not found or access denied\" }, 404);\n    }\n\n    // Try to get quiz questions if the table exists\n    try {\n      const { data: questions, error: questionsError } = await supabase\n        .from(\"quiz_questions\")\n        .select(\n          \"id, question_text, type, options, correct_answer, explanation, created_at\"\n        )\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: true });\n\n      if (!questionsError && questions) {\n        quiz.quiz_questions = questions.map((q: any) => ({\n          id: q.id,\n          question_text: q.question_text,\n          type: q.type,\n          options: q.options,\n          correct_answer: q.correct_answer,\n          explanation: q.explanation,\n          created_at: q.created_at,\n        }));\n      } else {\n        console.warn(\n          \"Could not fetch quiz questions (table may not exist):\",\n          questionsError\n        );\n        quiz.quiz_questions = [];\n      }\n    } catch (error: any) {\n      console.warn(\"Quiz questions table may not exist:\", error.message);\n      quiz.quiz_questions = [];\n    }\n\n    return c.json(quiz, 200);\n  } catch (error: any) {\n    console.error(\"Error fetching quiz by ID:\", error);\n    return c.json({ error: error.message || \"Failed to fetch quiz\" }, 500);\n  }\n});\n\n// Route to fetch all quizzes for a specific document (owned by the user)\nquizRoutes.get(\n  \"/document/:documentId\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const documentId = c.req.param(\"documentId\");\n\n    if (!documentId) {\n      return c.json({ error: \"Invalid document ID\" }, 400);\n    }\n\n    try {\n      const { data: docData, error: docError } = await supabase\n        .from(\"study_documents\")\n        .select(\"id\")\n        .eq(\"id\", documentId)\n        .eq(\"user_id\", user.id)\n        .single();\n\n      if (docError || !docData) {\n        return c.json({ error: \"Document not found or access denied.\" }, 404);\n      }\n\n      const { data: quizzesData, error: quizzesError } = await supabase\n        .from(\"quizzes\")\n        .select(\"id, name, created_at, study_document_id\")\n        .eq(\"study_document_id\", documentId)\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: false });\n\n      if (quizzesError) {\n        console.error(\"Error fetching quizzes for document:\", quizzesError);\n        return c.json(\n          { error: \"Failed to fetch quizzes for the document\" },\n          500\n        );\n      }\n\n      return c.json(quizzesData || [], 200);\n    } catch (error: any) {\n      console.error(\"Error fetching quizzes by document ID:\", error);\n      return c.json({ error: error.message || \"Failed to fetch quizzes\" }, 500);\n    }\n  }\n);\n\n// Add a testing endpoint to verify the router is working properly\nquizRoutes.get(\"/test\", async (c: Context) => {\n  console.log(\"Test endpoint hit successfully!\");\n  return c.json({ status: \"success\", message: \"Quiz routes are working!\" });\n});\n\n// Route to update SRS data for a quiz question\nquizRoutes.patch(\n  \"/questions/:questionId/srs\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const questionId = c.req.param(\"questionId\");\n\n    try {\n      const body = await c.req.json();\n      const {\n        srs_level,\n        due_at,\n        last_reviewed_at,\n        srs_interval,\n        srs_ease_factor,\n        srs_repetitions,\n        srs_correct_streak,\n      } = body;\n\n      if (!questionId) {\n        return c.json({ error: \"Question ID is required\" }, 400);\n      }\n\n      // Verify the question belongs to the user\n      const { data: questionCheck, error: checkError } = await supabase\n        .from(\"quiz_questions\")\n        .select(\"user_id\")\n        .eq(\"id\", questionId)\n        .single();\n\n      if (checkError || !questionCheck) {\n        return c.json({ error: \"Question not found\" }, 404);\n      }\n\n      if (questionCheck.user_id !== user.id) {\n        return c.json(\n          { error: \"Unauthorized: Question does not belong to this user\" },\n          403\n        );\n      }\n\n      // Update the SRS fields\n      const updateData: any = {};\n      if (srs_level !== undefined) updateData.srs_level = srs_level;\n      if (due_at !== undefined) updateData.due_at = due_at;\n      if (last_reviewed_at !== undefined)\n        updateData.last_reviewed_at = last_reviewed_at;\n      if (srs_interval !== undefined) updateData.srs_interval = srs_interval;\n      if (srs_ease_factor !== undefined)\n        updateData.srs_ease_factor = srs_ease_factor;\n      if (srs_repetitions !== undefined)\n        updateData.srs_repetitions = srs_repetitions;\n      if (srs_correct_streak !== undefined)\n        updateData.srs_correct_streak = srs_correct_streak;\n\n      if (Object.keys(updateData).length === 0) {\n        return c.json({ error: \"No SRS fields provided for update\" }, 400);\n      }\n\n      const { data: updatedQuestion, error: updateError } = await supabase\n        .from(\"quiz_questions\")\n        .update(updateData)\n        .eq(\"id\", questionId)\n        .eq(\"user_id\", user.id)\n        .select()\n        .single();\n\n      if (updateError) {\n        console.error(\"Error updating question SRS:\", updateError);\n        return c.json(\n          {\n            error: \"Failed to update question SRS\",\n            details: updateError.message,\n          },\n          500\n        );\n      }\n\n      return c.json({ success: true, question: updatedQuestion }, 200);\n    } catch (error: any) {\n      console.error(\"Error in SRS update:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to create a new quiz manually (without AI generation)\nquizRoutes.post(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n\n  try {\n    const body = await c.req.json();\n    const { name, description, study_document_id } = body;\n\n    if (!name || typeof name !== \"string\" || name.trim().length === 0) {\n      return c.json(\n        { error: \"Name is required and must be a non-empty string\" },\n        400\n      );\n    }\n\n    const quizData = {\n      name: name.trim(),\n      description: description || null,\n      study_document_id: study_document_id || null,\n      user_id: user.id,\n    };\n\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .insert(quizData)\n      .select(\"id, name\")\n      .single();\n\n    if (quizError) {\n      console.error(\"Error creating quiz:\", quizError);\n      return c.json(\n        { error: \"Failed to create quiz\", details: quizError.message },\n        500\n      );\n    }\n\n    return c.json({ id: quiz.id, name: quiz.name }, 201);\n  } catch (error: any) {\n    console.error(\"Error in manual quiz creation:\", error);\n    return c.json(\n      { error: \"An unexpected error occurred\", details: error.message },\n      500\n    );\n  }\n});\n\n// Route to fetch all quizzes for the authenticated user\nquizRoutes.get(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const user = c.get(\"user\");\n  console.log(`quizRoutes: GET / main handler reached. User ID: ${user?.id}`); // Log 5\n  console.log(\"quizRoutes: GET / handler triggered\"); // Added for debugging\n  try {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    // Defensive checks, though middleware should handle auth and set these\n    if (!supabase) {\n      console.error(\n        \"GET /api/quizzes/ handler: Supabase client not found in context.\"\n      );\n      return c.json(\n        {\n          error:\n            \"Internal Server Configuration Error: Supabase client not available.\",\n        },\n        500\n      );\n    }\n    if (!user || !user.id) {\n      console.error(\n        \"GET /api/quizzes/ handler: User not found in context or user ID missing.\"\n      );\n      return c.json(\n        {\n          error: \"Authentication Error: User information not available.\",\n          details: \"User context is invalid or missing.\",\n        },\n        500\n      );\n    }\n\n    const { data: quizzesData, error: quizzesError } = await supabase\n      .from(\"quizzes\")\n      .select(\"id, name, created_at, study_document_id\")\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: false });\n\n    if (quizzesError) {\n      console.error(\"Error fetching user quizzes from Supabase:\", quizzesError);\n      return c.json(\n        {\n          error: \"Failed to fetch user quizzes\",\n          details:\n            quizzesError.message || \"Database query encountered an error.\",\n        },\n        500\n      );\n    }\n\n    return c.json({ quizzes: quizzesData || [] }, 200);\n  } catch (error: any) {\n    console.error(\"Unexpected error in GET /api/quizzes/ handler:\", error);\n    return c.json(\n      {\n        error: \"An unexpected server error occurred while fetching quizzes.\",\n        details: error.message || \"Unknown internal server error.\",\n      },\n      500\n    );\n  }\n});\n\n// Route to delete a quiz and its questions\nquizRoutes.delete(\n  \"/:quizId\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Invalid quiz ID\" }, 400);\n    }\n\n    try {\n      // First, delete all questions associated with this quiz and user\n      const { error: questionsDeleteError } = await supabase\n        .from(\"quiz_questions\")\n        .delete()\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id); // Ensure user owns the questions\n\n      if (questionsDeleteError) {\n        console.error(\"Error deleting quiz questions:\", questionsDeleteError);\n        return c.json(\n          {\n            error: \"Failed to delete quiz questions\",\n            details: questionsDeleteError.message,\n          },\n          500\n        );\n      }\n\n      // Then, delete the quiz itself\n      const { error: quizDeleteError } = await supabase\n        .from(\"quizzes\")\n        .delete()\n        .eq(\"id\", quizId)\n        .eq(\"user_id\", user.id); // Ensure user owns the quiz\n\n      if (quizDeleteError) {\n        console.error(\"Error deleting quiz:\", quizDeleteError);\n        return c.json(\n          { error: \"Failed to delete quiz\", details: quizDeleteError.message },\n          500\n        );\n      }\n\n      return c.json(\n        {\n          success: true,\n          message: \"Quiz and associated questions deleted successfully.\",\n        },\n        200\n      );\n    } catch (error: any) {\n      console.error(\"Error deleting quiz:\", error);\n      return c.json({ error: error.message || \"Failed to delete quiz\" }, 500);\n    }\n  }\n);\n\n// Custom Not Found handler for quizRoutes\nquizRoutes.notFound((c) => {\n  console.error(\n    `[quizRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`\n  );\n  return c.json(\n    { error: \"Quiz route not found\", path: c.req.path, method: c.req.method },\n    404\n  );\n});\n\n// Route to add a batch of questions to an existing quiz\nquizRoutes.post('/:quizId/questions/batch', async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get('supabase');\n  const user = c.get('user');\n  const quizId = c.req.param('quizId');\n\n  try {\n    const { questions: questionsPayload } = await c.req.json<{ questions: QuizQuestionBatchInsertPayload[] }>();\n\n    if (!quizId) {\n      return c.json({ error: 'Quiz ID is required' }, 400);\n    }\n\n    if (!Array.isArray(questionsPayload) || questionsPayload.length === 0) {\n      return c.json({ error: 'Questions payload must be a non-empty array' }, 400);\n    }\n\n    // Verify user owns the quiz\n    const { data: quiz, error: quizError } = await supabase\n      .from('quizzes')\n      .select('id, user_id')\n      .eq('id', quizId)\n      .single();\n\n    if (quizError || !quiz) {\n      return c.json({ error: 'Quiz not found' }, 404);\n    }\n    if (quiz.user_id !== user.id) {\n      return c.json({ error: 'Forbidden: You do not own this quiz' }, 403);\n    }\n\n    // Prepare questions for insertion\n    const questionsToInsert = questionsPayload.map(q => ({\n      question_text: q.question_text,\n      type: q.type, // Assuming q.type is already a valid enum string\n      options: q.options || null,\n      correct_answer: q.correct_answer || null,\n      explanation: q.explanation || null,\n      quiz_id: quizId,\n      user_id: user.id,\n    }));\n\n    const { data: insertedQuestions, error: insertError } = await supabase\n      .from('quiz_questions')\n      .insert(questionsToInsert)\n      .select();\n\n    if (insertError) {\n      console.error('Error inserting batch questions:', insertError);\n      return c.json({ error: 'Failed to add questions to quiz', details: insertError.message }, 500);\n    }\n\n    return c.json(insertedQuestions, 201);\n  } catch (error: any) {\n    console.error('Error in /:quizId/questions/batch route:', error);\n    return c.json({ error: 'An unexpected error occurred', details: error.message }, 500);\n  }\n});\n\n// Route to fetch a specific quiz and its questions\nquizRoutes.get(\"/:quizId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const quizId = c.req.param(\"quizId\");\n\n  if (!quizId) {\n    return c.json({ error: \"Invalid quiz ID\" }, 400);\n  }\n\n  try {\n    // Fetch the quiz\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .select(\"*\")\n      .eq(\"id\", quizId)\n      .single();\n\n    if (quizError) {\n      console.error(\"Error fetching quiz:\", quizError);\n      return c.json(\n        { error: \"Failed to fetch quiz\", details: quizError.message },\n        quizError.code === \"PGRST116\" ? 404 : 500\n      );\n    }\n\n    // Check if user owns the quiz\n    if (quiz.user_id !== user.id) {\n      return c.json(\n        { error: \"You do not have permission to access this quiz\" },\n        403\n      );\n    }\n\n    // Fetch questions for this quiz\n    const { data: questions, error: questionsError } = await supabase\n      .from(\"quiz_questions\")\n      .select(\"*\")\n      .eq(\"quiz_id\", quizId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\");\n\n    if (questionsError) {\n      console.error(\"Error fetching quiz questions:\", questionsError);\n      return c.json(\n        {\n          error: \"Failed to fetch quiz questions\",\n          details: questionsError.message,\n        },\n        500\n      );\n    }\n\n    // Return the quiz with its questions\n    return c.json({\n      ...quiz,\n      questions: questions || [],\n    });\n  } catch (error: any) {\n    console.error(\"Error fetching quiz by ID:\", error);\n    return c.json({ error: error.message || \"Failed to fetch quiz\" }, 500);\n  }\n});\n\n// Route to track quiz completion\nquizRoutes.post(\n  \"/:quizId/complete\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Quiz ID is required\" }, 400);\n    }\n\n    try {\n      const body = await c.req.json();\n      const completionData: QuizCompletionData = body;\n\n      // Validate required fields\n      if (!completionData.score || !completionData.questions_answered) {\n        return c.json({ error: \"Score and questions answered are required\" }, 400);\n      }\n\n      // Verify the quiz belongs to the user\n      const { data: quiz, error: quizError } = await supabase\n        .from(\"quizzes\")\n        .select(\"user_id\")\n        .eq(\"id\", quizId)\n        .single();\n\n      if (quizError || !quiz) {\n        return c.json({ error: \"Quiz not found\" }, 404);\n      }\n\n      if (quiz.user_id !== user.id) {\n        return c.json(\n          { error: \"Unauthorized: Quiz does not belong to this user\" },\n          403\n        );\n      }\n\n      // Create completion record\n      const completionInsert: UserCompletionInsert = {\n        user_id: user.id,\n        quiz_id: quizId,\n        completion_type: \"quiz\",\n        completed_at: new Date().toISOString(),\n        score: completionData.score,\n        time_spent_minutes: completionData.time_spent_minutes,\n        questions_answered: completionData.questions_answered,\n        correct_answers: completionData.correct_answers,\n        metadata: completionData.metadata || null,\n      };\n\n      const { data: completion, error: insertError } = await supabase\n        .from(\"user_completions\")\n        .insert(completionInsert)\n        .select()\n        .single();\n\n      if (insertError) {\n        console.error(\"Error recording quiz completion:\", insertError);\n        return c.json(\n          { error: \"Failed to record completion\", details: insertError.message },\n          500\n        );\n      }\n\n      return c.json(completion, 201);\n    } catch (error: any) {\n      console.error(\"Error in quiz completion endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get completion statistics for the user\nquizRoutes.get(\n  \"/stats/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      // Get all user completions\n      const { data: completions, error: completionsError } = await supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"completed_at\", { ascending: false });\n\n      if (completionsError) {\n        console.error(\"Error fetching completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      const allCompletions = completions || [];\n      const quizCompletions = allCompletions.filter(c => c.completion_type === \"quiz\");\n      const flashcardCompletions = allCompletions.filter(c => c.completion_type === \"flashcard_set\");\n\n      // Calculate statistics\n      const stats: CompletionStats = {\n        total_completions: allCompletions.length,\n        quiz_completions: quizCompletions.length,\n        flashcard_completions: flashcardCompletions.length,\n        average_quiz_score: quizCompletions.length > 0\n          ? Math.round(quizCompletions.reduce((sum, c) => sum + (c.score || 0), 0) / quizCompletions.length)\n          : 0,\n        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),\n        best_quiz_score: quizCompletions.length > 0\n          ? Math.max(...quizCompletions.map(c => c.score || 0))\n          : 0,\n        recent_completions: allCompletions.slice(0, 10),\n        completion_streak: calculateCompletionStreak(allCompletions),\n        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),\n        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),\n      };\n\n      return c.json(stats);\n    } catch (error: any) {\n      console.error(\"Error fetching completion stats:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get user's quiz completion data\nquizRoutes.get(\n  \"/:quizId/user-completion\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Quiz ID is required\" }, 400);\n    }\n\n    try {\n      // Fetch user's completion data for this quiz\n      const { data, error } = await supabase\n        .from(\"quiz_completions\")\n        .select(\"*\")\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id)\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      return c.json(\n        { success: true, completionData: data },\n        200\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching user completion data:\", error);\n      return c.json(\n        { error: \"Failed to fetch user completion data\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get user's completion history with filtering\nquizRoutes.get(\n  \"/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      const queryParams = c.req.query();\n      const filters: CompletionFilters = {\n        completion_type: queryParams.type as 'quiz' | 'flashcard_set' | undefined,\n        quiz_id: queryParams.quiz_id,\n        flashcard_set_id: queryParams.flashcard_set_id,\n        min_score: queryParams.min_score ? parseInt(queryParams.min_score) : undefined,\n        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,\n        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,\n      };\n\n      if (queryParams.start_date && queryParams.end_date) {\n        filters.date_range = {\n          start: queryParams.start_date,\n          end: queryParams.end_date,\n        };\n      }\n\n      // Build query\n      let query = supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id);\n\n      if (filters.completion_type) {\n        query = query.eq(\"completion_type\", filters.completion_type);\n      }\n\n      if (filters.quiz_id) {\n        query = query.eq(\"quiz_id\", filters.quiz_id);\n      }\n\n      if (filters.flashcard_set_id) {\n        query = query.eq(\"flashcard_set_id\", filters.flashcard_set_id);\n      }\n\n      if (filters.min_score) {\n        query = query.gte(\"score\", filters.min_score);\n      }\n\n      if (filters.date_range) {\n        query = query\n          .gte(\"completed_at\", filters.date_range.start)\n          .lte(\"completed_at\", filters.date_range.end);\n      }\n\n      if (filters.limit) {\n        query = query.limit(filters.limit);\n      }\n\n      if (filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n      }\n\n      query = query.order(\"completed_at\", { ascending: false });\n\n      const { data: completions, error: completionsError } = await query;\n\n      if (completionsError) {\n        console.error(\"Error fetching filtered completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      return c.json(completions || []);\n    } catch (error: any) {\n      console.error(\"Error in completions endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Helper functions for completion statistics\nfunction calculateCompletionStreak(completions: any[]): number {\n  if (completions.length === 0) return 0;\n\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  \n  let streak = 0;\n  let currentDate = new Date(today);\n  \n  for (let i = 0; i < 30; i++) { // Check last 30 days\n    const dayCompletions = completions.filter(c => {\n      const completionDate = new Date(c.completed_at);\n      completionDate.setHours(0, 0, 0, 0);\n      return completionDate.getTime() === currentDate.getTime();\n    });\n    \n    if (dayCompletions.length > 0) {\n      streak++;\n    } else if (streak > 0) {\n      break; // Streak is broken\n    }\n    \n    currentDate.setDate(currentDate.getDate() - 1);\n  }\n  \n  return streak;\n}\n\nfunction getCompletionsInTimeRange(completions: any[], days: number): number {\n  const cutoffDate = new Date();\n  cutoffDate.setDate(cutoffDate.getDate() - days);\n  \n  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;\n}\n\nexport default quizRoutes;\n", "modifiedCode": "import { Hono, Context, Next } from \"hono\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\nimport express, { Request, Response } from \"express\";\nimport {\n  GenerateQuizParams,\n  GenerateQuizResponse,\n  QuizQuestion as SharedQuizQuestion,\n  QuizQuestionOption,\n  AIProviderConfig,\n  QuizQuestionBatchInsertPayload,\n  Quiz,\n} from \"../../shared/types/quiz\";\nimport {\n  UserCompletionInsert,\n  QuizCompletionData,\n  CompletionStats,\n  CompletionFilters,\n} from \"../../shared/types/completion\";\nimport { v4 as uuidv4 } from \"uuid\";\n\n// Extend RequestInit to include Node.js specific duplex option\ninterface NodeRequestInit extends RequestInit {\n  duplex?: \"half\" | \"full\";\n}\n\n// Fix for Node.js 18+ fetch duplex issue\nif (typeof globalThis.fetch === \"undefined\") {\n  globalThis.fetch = require(\"node-fetch\");\n}\n\ntype AppVariables = {\n  supabase: SupabaseClient;\n  user: { id: string };\n};\n\nconsole.log(\"quizRoutes.ts: Module loaded\"); // Added for debugging\n\nconst quizRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nquizRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for quizRoutes\n// This ensures that any unhandled error within quizRoutes still returns a JSON response.\nquizRoutes.onError((err, c) => {\n  console.error(\"Error in quizRoutes:\", err);\n  // Optionally include stack in development\n  // const stack = process.env.NODE_ENV === 'development' ? err.stack : undefined;\n  return c.json(\n    {\n      error: \"An unexpected error occurred in quiz routes.\",\n      message: err.message,\n      // stack\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`quizRoutes: Auth middleware triggered for path: ${c.req.path}`); // Log 1\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\n      \"quizRoutes: Auth Error - Authorization header missing or malformed.\"\n    ); // Log E1\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"quizRoutes: Auth Error - Token missing after Bearer split.\"); // Log E2\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  const supabase = c.get(\"supabase\");\n  if (!supabase) {\n    console.error(\n      \"quizRoutes: Auth Error - Supabase client not found in request context.\"\n    ); // Log E3\n    return c.json(\n      { error: \"Server configuration error: Supabase client missing\" },\n      500\n    );\n  }\n\n  console.log(\n    \"quizRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\"\n  ); // Log 2\n  try {\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\n        `quizRoutes: Auth Error - supabase.auth.getUser returned an error: ${getUserError.message}`,\n        getUserError\n      ); // Log E4\n      if (\n        getUserError.message.toLowerCase().includes(\"invalid token\") ||\n        getUserError.message.includes(\"jwt\")\n      ) {\n        return c.json(\n          {\n            error: \"Unauthorized: Invalid token\",\n            details: getUserError.message,\n          },\n          401\n        );\n      }\n      return c.json(\n        {\n          error: \"Server error validating token\",\n          details: getUserError.message,\n        },\n        500\n      );\n    }\n\n    const user = data?.user; // Check data.user specifically\n\n    if (!user) {\n      console.error(\n        \"quizRoutes: Auth Error - No user object found in supabase.auth.getUser response (data.user is null/undefined).\"\n      ); // Log E5\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    console.log(\n      `quizRoutes: Auth Success - User ${user.id} authenticated. Calling next().`\n    ); // Log 4\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\n      \"quizRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\",\n      err.message,\n      err.stack\n    ); // Log E6\n    // In Hono, if an error occurs after next() has been called and potentially a response sent by a later handler,\n    // this return might not execute or might cause issues if a response was already committed.\n    // However, for errors *before* or *during* c.json(), this is fine.\n    // If next() itself throws and doesn't handle its own response, this catch block will execute.\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nquizRoutes.use(\"*\", authMiddleware);\n\ninterface AIPromptPayload {\n  model: string;\n  messages: {\n    role: \"user\" | \"assistant\" | \"system\";\n    content: string;\n  }[];\n  temperature?: number;\n  max_tokens?: number;\n  // Add other parameters as supported by OpenRouter and the model\n}\n\ninterface AIResponseFormat {\n  questions: AIGeneratedQuestion[];\n}\ninterface AIGeneratedQuestion {\n  questionText: string;\n  type:\n    | \"multiple_choice\"\n    | \"select_all_that_apply\"\n    | \"true_false\"\n    | \"short_answer\";\n  options?: Array<{ text: string; is_correct?: boolean }>;\n  correctAnswer?: string | boolean | string[];\n  explanation?: string;\n}\n\n// Helper function to call AI (OpenRouter)\nasync function generateQuestionsFromAI(\n  textContent: string,\n  numberOfQuestions: number,\n  aiConfig: AIProviderConfig,\n  quizTitle: string,\n  customPromptText?: string,\n  requestedQuestionTypes?: string[]\n): Promise<AIResponseFormat | null> {\n  const { apiKey, baseUrl, model } = aiConfig;\n\n  // Validate AI configuration\n  if (!apiKey || !apiKey.trim()) {\n    console.error(\"AI API key is missing or empty\");\n    throw new Error(\n      \"AI API key is required but not provided. Please configure your AI provider settings.\"\n    );\n  }\n\n  if (!baseUrl || !baseUrl.trim()) {\n    console.error(\"AI base URL is missing or empty\");\n    throw new Error(\n      \"AI base URL is required but not provided. Please configure your AI provider settings.\"\n    );\n  }\n\n  console.log(\n    `Generating ${numberOfQuestions} questions using model: ${\n      model || \"google/gemini-2.5-pro-preview\"\n    }`\n  );\n\n  const questionTypesString =\n    requestedQuestionTypes && requestedQuestionTypes.length > 0\n      ? requestedQuestionTypes.join(\", \")\n      : \"multiple_choice, true_false, short_answer, select_all_that_apply\"; // Default if not specified\n\n  console.log(`AI Generation: Requested question types: [${questionTypesString}]`);\n\n  const basePrompt = `Based on the following text, generate a quiz titled \"${quizTitle}\" with exactly ${numberOfQuestions} questions.\n\nIMPORTANT: You MUST generate questions ONLY of the following types: ${questionTypesString}\nDo NOT generate questions of any other types. Every single question must be one of these exact types: ${questionTypesString}.\n\nFor each question, provide:\n1. \"questionText\": The text of the question (string).\n2. \"type\": The type of question (string, must be one of \"multiple_choice\", \"select_all_that_apply\", \"true_false\", \"short_answer\").\n3. \"options\": (ONLY for \"multiple_choice\" and \"select_all_that_apply\") An array of 3 to 5 option objects. Each option object must have a \"text\" (string) key. For \"multiple_choice\", one option should have \"is_correct\": true. For \"select_all_that_apply\", one or more options should have \"is_correct\": true. Other options should have \"is_correct\": false or omit it.\n4. \"correctAnswer\":\n   - For \"true_false\": The string \"true\" or \"false\".\n   - For \"short_answer\": The concise correct answer string.\n   - For \"multiple_choice\": The text of the single correct option. (This is redundant if \"is_correct\" is in options, but good for AI to provide).\n   - For \"select_all_that_apply\": An array of strings, where each string is the text of a correct option. (Also redundant if \"is_correct\" is in options).\n5. \"explanation\": (Optional) A brief explanation for why the answer is correct (string).\n\nReturn the output as a valid JSON object with a single key \"questions\". The value of \"questions\" should be an array of question objects adhering to the structure described above.`;\n\n  const customInstructions = customPromptText\n    ? `\\n\\nAdditional instructions for question generation: ${customPromptText}`\n    : \"\";\n  const prompt = `${basePrompt}${customInstructions}\\n\\nText:\\n\"\"\"\\n${textContent.substring(\n    0,\n    15000\n  )}\\n\"\"\"\\n\\nOutput only the JSON object.`;\n\n  console.log(\"AI Prompt being sent:\", prompt.substring(0, 500) + \"...\"); // Log part of the prompt\n\n  const payload: AIPromptPayload = {\n    model: model || \"google/gemini-2.5-pro-preview\", // Default to gemini-2.5-pro-preview as per OpenRouter\n    messages: [{ role: \"user\", content: prompt }],\n    // temperature: 0.7, // Adjust as needed\n    // max_tokens: 2000, // Adjust as needed, ensure it's enough for the number of questions\n  };\n\n  try {\n    console.log(`Making AI API call to: ${baseUrl}/chat/completions`);\n    const response = await fetch(`${baseUrl}/chat/completions`, {\n      method: \"POST\",\n      headers: {\n        Authorization: `Bearer ${apiKey}`,\n        \"Content-Type\": \"application/json\",\n      },\n      body: JSON.stringify(payload),\n      duplex: \"half\", // Required for Node.js 18+ when sending a body\n    } as NodeRequestInit);\n\n    if (!response.ok) {\n      console.error(`AI API error: ${response.status} ${response.statusText}`);\n      const errorText = await response.text();\n      console.error(\"AI API error response:\", errorText);\n\n      // Provide more specific error messages based on status code\n      if (response.status === 401) {\n        throw new Error(\n          \"Unauthorized: Invalid API key. Please check your AI provider API key.\"\n        );\n      } else if (response.status === 403) {\n        throw new Error(\n          \"Forbidden: API key does not have permission for this operation.\"\n        );\n      } else if (response.status === 429) {\n        throw new Error(\n          \"Rate limited: Too many requests. Please try again later.\"\n        );\n      } else {\n        throw new Error(`AI provider error (${response.status}): ${errorText}`);\n      }\n    }\n\n    const responseData = await response.json();\n\n    if (\n      responseData &&\n      responseData.choices &&\n      responseData.choices[0] &&\n      responseData.choices[0].message\n    ) {\n      const content = responseData.choices[0].message.content;\n      console.log(\"AI response received, parsing JSON...\");\n\n      // The AI might return the JSON as a string, sometimes wrapped in markdown ```json ... ```\n      const jsonMatch = content.match(/```json\\n([\\s\\S]*?)\\n```/);\n      const jsonString = jsonMatch ? jsonMatch[1] : content;\n      try {\n        const result = JSON.parse(jsonString) as AIResponseFormat;\n        console.log(\n          `Successfully parsed ${\n            result.questions?.length || 0\n          } questions from AI response`\n        );\n\n        // Filter questions to only include the requested types\n        if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {\n          const originalCount = result.questions.length;\n          result.questions = result.questions.filter(q =>\n            requestedQuestionTypes.includes(q.type)\n          );\n          const filteredCount = result.questions.length;\n\n          if (originalCount !== filteredCount) {\n            console.log(\n              `AI Generation: Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +\n              `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(\", \")}]`\n            );\n          }\n        }\n\n        return result;\n      } catch (parseError) {\n        console.error(\n          \"Error parsing AI response JSON:\",\n          parseError,\n          \"Raw content:\",\n          content\n        );\n        // Attempt to find JSON within a potentially messy string\n        const relaxedJsonMatch = content.match(/{[\\s\\S]*}/);\n        if (relaxedJsonMatch && relaxedJsonMatch[0]) {\n          try {\n            const result = JSON.parse(relaxedJsonMatch[0]) as AIResponseFormat;\n            console.log(\n              `Successfully parsed ${\n                result.questions?.length || 0\n              } questions from relaxed JSON match`\n            );\n\n            // Filter questions to only include the requested types\n            if (requestedQuestionTypes && requestedQuestionTypes.length > 0 && result.questions) {\n              const originalCount = result.questions.length;\n              result.questions = result.questions.filter(q =>\n                requestedQuestionTypes.includes(q.type)\n              );\n              const filteredCount = result.questions.length;\n\n              if (originalCount !== filteredCount) {\n                console.log(\n                  `AI Generation (relaxed): Filtered out ${originalCount - filteredCount} questions that didn't match requested types. ` +\n                  `Kept ${filteredCount} questions of types: [${requestedQuestionTypes.join(\", \")}]`\n                );\n              }\n            }\n\n            return result;\n          } catch (relaxedParseError) {\n            console.error(\n              \"Error parsing relaxed AI response JSON:\",\n              relaxedParseError,\n              \"Raw content:\",\n              content\n            );\n            throw new Error(\n              \"Failed to parse AI response as valid JSON. The AI response format was unexpected.\"\n            );\n          }\n        }\n        throw new Error(\n          \"Failed to parse AI response as valid JSON. The AI response format was unexpected.\"\n        );\n      }\n    } else {\n      console.error(\"Unexpected AI response structure:\", responseData);\n      throw new Error(\n        \"AI response did not contain expected message structure.\"\n      );\n    }\n  } catch (error) {\n    console.error(\"Error calling AI API:\", error);\n    // Re-throw the error instead of returning null so the calling function can handle it properly\n    throw error;\n  }\n}\n\n// POST /api/quizzes/generate\nquizRoutes.post(\n  \"/generate\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    try {\n      console.log(\n        `[quizRoutes] POST /generate handler reached. Path: ${c.req.path}, Method: ${c.req.method}`\n      );\n      const requestBody = await c.req.json();\n      console.log(\n        \"Quiz generation request body:\",\n        JSON.stringify(requestBody, null, 2)\n      );\n\n      // Handle both payload formats:\n      // Format 1 (UploadSection): { textContent, documentId, quizTitle, numberOfQuestions, aiConfig }\n      // Format 2 (CreateQuizForm): { documentIds, quizName, quizDescription, generationOptions }\n\n      let textContent: string;\n      let documentId: string | null;\n      let quizTitle: string;\n      let numberOfQuestions: number;\n      let aiConfig: AIProviderConfig;\n      let generationOptions: GenerateQuizParams[\"generationOptions\"];\n      let customPrompt: string | undefined;\n\n      if (requestBody.textContent && requestBody.quizTitle) {\n        // Format 1 (UploadSection) - Legacy format\n        textContent = requestBody.textContent;\n        documentId = requestBody.documentId || null;\n        quizTitle = requestBody.quizTitle;\n        numberOfQuestions = requestBody.numberOfQuestions;\n        aiConfig = requestBody.aiConfig;\n        customPrompt = requestBody.customPrompt;\n        // Infer generationOptions if not explicitly provided in this format, or use what's passed\n        generationOptions = requestBody.generationOptions || {\n          numberOfQuestions: numberOfQuestions,\n          questionTypes: requestBody.questionTypes || [\"multiple_choice\"], // Use provided questionTypes or default\n        };\n      } else if (requestBody.textContent && requestBody.quizName && requestBody.generationOptions) {\n        // Format 1.5 (UploadSection) - New format with textContent and generationOptions\n        textContent = requestBody.textContent;\n        documentId = requestBody.documentId || null;\n        quizTitle = requestBody.quizName; // This is correct\n        // Ensure generationOptions is correctly picked up from requestBody for Format 1.5\n        generationOptions = requestBody.generationOptions; // This should contain numberOfQuestions and questionTypes\n        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;\n        customPrompt = requestBody.customPrompt;\n        aiConfig = requestBody.aiConfig;\n      } else if (requestBody.quizName && requestBody.generationOptions) {\n        // Format 2 (CreateQuizForm) - need to get document content\n        const documentIds = requestBody.documentIds || [];\n        if (documentIds.length === 0) {\n          return c.json(\n            { error: \"No document IDs provided for AI generation\" },\n            400\n          );\n        }\n\n        // Handle multiple documents\n        let combinedTextContent = \"\";\n        for (const docId of documentIds) {\n          const { data: document, error: docError } = await supabase\n            .from(\"study_documents\") // Fetch file_name for context\n            .select(\"extracted_text_path, user_id, file_name\")\n            .eq(\"id\", docId)\n            .single();\n\n          if (docError || !document) {\n            return c.json(\n              { error: `Document not found for ID: ${docId}` },\n              404\n            );\n          }\n          if (document.user_id !== user.id) {\n            return c.json(\n              {\n                error: `Forbidden: Document ${docId} does not belong to this user`,\n              },\n              403\n            );\n          }\n          if (!document.extracted_text_path) {\n            return c.json(\n              {\n                error: `Document ${docId} (${document.file_name}) has no extracted text path.`,\n              },\n              400\n            );\n          }\n\n          const { data: fileData, error: downloadError } =\n            await supabase.storage\n              .from(\"study_materials\") // Assuming 'study_materials' is the correct bucket\n              .download(document.extracted_text_path);\n\n          if (downloadError || !fileData) {\n            console.error(\n              `Error downloading content for document ${docId}:`,\n              downloadError\n            );\n            return c.json(\n              {\n                error: `Failed to download content for document ${docId} (${document.file_name})`,\n                details: downloadError?.message,\n              },\n              500\n            );\n          }\n\n          const docTextContent = await fileData.text();\n          combinedTextContent += `Content from ${document.file_name}:\\n${docTextContent}\\n\\n`;\n        }\n        textContent = combinedTextContent.trim();\n        documentId = documentIds.join(\",\"); // Store comma-separated IDs or handle differently\n\n        quizTitle = requestBody.quizName;\n        generationOptions = requestBody.generationOptions; // FIX: Assign the generationOptions object\n        numberOfQuestions = requestBody.generationOptions.numberOfQuestions;\n        customPrompt = requestBody.generationOptions.customPrompt;\n\n        // Get AI config from stored user credentials\n        const { getEphemeralUserCredentials } = await import('../middleware/apiKeyStorage');\n        const credentialsResult = await getEphemeralUserCredentials(userId, 'OpenRouter');\n\n        if (!credentialsResult.success || !credentialsResult.credentials) {\n          return c.json(\n            {\n              error: \"AI Provider not configured. Please configure your AI settings first.\",\n              details: \"No stored credentials found for OpenRouter provider\"\n            },\n            400\n          );\n        }\n\n        aiConfig = {\n          apiKey: credentialsResult.credentials.apiKey,\n          baseUrl: credentialsResult.credentials.baseUrl,\n          model: credentialsResult.credentials.generationModel,\n        };\n      } else {\n        return c.json(\n          {\n            error:\n              \"Invalid request format. Expected either { textContent, quizTitle, numberOfQuestions, aiConfig } or { textContent, quizName, generationOptions, aiConfig } or { quizName, documentIds, generationOptions }\",\n          },\n          400\n        );\n      }\n\n      if (!textContent || !quizTitle || !numberOfQuestions) {\n        return c.json( // This check might be redundant if generationOptions is always present for Format 2\n          {\n            error: \"Missing required fields after processing request\",\n          },\n          400\n        );\n      }\n\n      // Debug logging for generationOptions\n      console.log(\"Backend: Received generationOptions:\", JSON.stringify(generationOptions, null, 2));\n      console.log(\"Backend: Question types received:\", generationOptions?.questionTypes);\n      console.log(\"Backend: Question types length:\", generationOptions?.questionTypes?.length);\n\n      // Validate that question types are provided and not empty\n      if (!generationOptions?.questionTypes || generationOptions.questionTypes.length === 0) {\n        console.error(\"Backend: Validation failed - no question types provided\");\n        return c.json(\n          {\n            error: \"At least one question type must be selected for AI generation\",\n          },\n          400\n        );\n      }\n\n      // Document ownership already verified above for Format 2\n      // For Format 1, verify document ownership only if we need to fetch content from the database\n      // If textContent is already provided, we don't need to verify the document exists\n      if (documentId && !requestBody.textContent) {\n        const { data: documentOwner, error: docError } = await supabase\n          .from(\"study_documents\")\n          .select(\"user_id\")\n          .eq(\"id\", documentId)\n          .single();\n\n        if (docError || !documentOwner) {\n          return c.json(\n            {\n              error: `Failed to verify document ownership or document not found for ID: ${documentId}`,\n            },\n            404\n          );\n        }\n\n        if (documentOwner.user_id !== user.id) {\n          return c.json(\n            {\n              error: `Forbidden: Document ${documentId} does not belong to this user.`,\n            },\n            403\n          );\n        }\n      }\n\n      console.log(\"🤖 Generating questions from AI...\");\n      console.log(`Quiz Generation: Requested ${numberOfQuestions} questions of types: [${generationOptions?.questionTypes?.join(\", \") || \"default\"}]`);\n\n      const aiGeneratedData = await generateQuestionsFromAI(\n        textContent,\n        numberOfQuestions,\n        aiConfig,\n        quizTitle,\n        customPrompt,\n        generationOptions?.questionTypes\n      );\n\n      if (\n        !aiGeneratedData ||\n        !aiGeneratedData.questions ||\n        aiGeneratedData.questions.length === 0\n      ) {\n        return c.json(\n          {\n            error:\n              \"Failed to generate questions from AI or AI returned no questions.\",\n          },\n          500\n        );\n      }\n\n      // Log what types were actually generated\n      const generatedTypes = aiGeneratedData.questions.map(q => q.type);\n      const typeCounts = generatedTypes.reduce((acc, type) => {\n        acc[type] = (acc[type] || 0) + 1;\n        return acc;\n      }, {} as Record<string, number>);\n      console.log(`Quiz Generation: AI generated ${aiGeneratedData.questions.length} questions with types:`, typeCounts);\n\n      // Ensure the number of questions matches the request, or handle discrepancies\n      const generatedQs = aiGeneratedData.questions.slice(0, numberOfQuestions);\n      if (generatedQs.length < numberOfQuestions) {\n        console.warn(\n          `AI generated ${generatedQs.length} questions, but ${numberOfQuestions} were requested.`\n        );\n        // Decide if this is an error or if we proceed with fewer questions\n        if (generatedQs.length === 0) {\n          return c.json({ error: \"AI generated no usable questions.\" }, 500);\n        }\n      }\n\n      console.log(\"💾 Saving quiz to database...\");\n\n      let finalStudyDocumentId: string | null = null;\n      if (documentId) {\n        // If documentId might be a comma-separated list from multiple docs, take the first one for linking.\n        // Or, decide on a strategy for linking quizzes to multiple documents (e.g., a join table or array field).\n        // For simplicity, let's assume we link to the first document if multiple are provided.\n        const firstDocId = documentId.split(\",\")[0];\n        if (firstDocId) {\n          const uuidRegex =\n            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;\n          if (uuidRegex.test(firstDocId)) {\n            const { data: existingDoc, error: docCheckError } = await supabase\n              .from(\"study_documents\")\n              .select(\"id\")\n              .eq(\"id\", firstDocId)\n              .eq(\"user_id\", user.id)\n              .maybeSingle();\n\n            if (docCheckError) {\n              console.error(\n                `Error checking for document ${firstDocId}:`,\n                docCheckError.message\n              );\n            } else if (existingDoc) {\n              finalStudyDocumentId = existingDoc.id;\n            } else {\n              console.warn(\n                `Document with ID \"${firstDocId}\" not found for user \"${user.id}\". Proceeding with null study_document_id for quiz.`\n              );\n            }\n          } else {\n            console.warn(\n              `Invalid UUID format for documentId: \"${firstDocId}\". Proceeding with null for study_document_id.`\n            );\n          }\n        } else {\n          console.warn(\n            `No valid document ID found in \"${documentId}\". Proceeding with null for study_document_id.`\n          );\n        }\n      }\n\n      // Use Supabase instead of Drizzle ORM\n      let savedQuiz;\n      let quizError;\n\n      const { data: firstAttemptQuiz, error: firstAttemptError } =\n        await supabase\n          .from(\"quizzes\")\n          .insert({\n            name: quizTitle,\n            study_document_id: finalStudyDocumentId, // Use validated and existing or null ID\n            user_id: user.id,\n          })\n          .select()\n          .single();\n\n      savedQuiz = firstAttemptQuiz;\n      quizError = firstAttemptError;\n\n      if (quizError) {\n        console.error(\"Initial error saving quiz:\", quizError);\n        // Check for specific foreign key violation on document_id\n        if (\n          quizError.code === \"23503\" &&\n          quizError.message.includes(\"quizzes_document_id_fkey\") &&\n          finalStudyDocumentId !== null\n        ) {\n          console.warn(\n            `Foreign key violation for document_id: ${finalStudyDocumentId}. Retrying quiz insert with null document_id.`\n          );\n          const { data: retryQuiz, error: retryError } = await supabase\n            .from(\"quizzes\")\n            .insert({\n              name: quizTitle,\n              study_document_id: null, // Attempt with null\n              user_id: user.id,\n            })\n            .select()\n            .single();\n\n          if (retryError) {\n            console.error(\n              \"Error saving quiz on retry with null document_id:\",\n              retryError\n            );\n            // Keep the original error for the response if retry also fails\n            quizError = retryError; // Or could be firstAttemptError, depending on desired behavior\n            savedQuiz = null;\n          } else {\n            console.log(\n              \"Successfully saved quiz on retry with null document_id.\"\n            );\n            savedQuiz = retryQuiz;\n            quizError = null; // Clear error as retry was successful\n          }\n        }\n      }\n\n      if (quizError || !savedQuiz) {\n        console.error(\n          \"Final error saving quiz after potential retry:\",\n          quizError\n        );\n        return c.json({ error: \"Failed to save quiz to database.\" }, 500);\n      }\n\n      console.log(\"💾 Attempting to save quiz questions to database...\");\n\n      // Check if quiz_questions table exists by attempting to insert\n      const questionsToInsert = generatedQs.map((q) => {\n        let correctAnswerValue: string | null = null;\n        if (typeof q.correctAnswer === \"string\")\n          correctAnswerValue = q.correctAnswer;\n        else if (typeof q.correctAnswer === \"boolean\")\n          correctAnswerValue = q.correctAnswer.toString();\n        else if (Array.isArray(q.correctAnswer))\n          correctAnswerValue = JSON.stringify(q.correctAnswer);\n        return {\n          quiz_id: savedQuiz.id,\n          question_text: q.questionText,\n          type: q.type,\n          options: q.options ?? null,\n          correct_answer: correctAnswerValue,\n          explanation: q.explanation || null,\n          user_id: user.id,\n        };\n      });\n\n      let insertedQuestions = null;\n      let questionsError = null;\n\n      // Try to save questions if table exists\n      if (questionsToInsert.length > 0) {\n        try {\n          const result = await supabase\n            .from(\"quiz_questions\")\n            .insert(questionsToInsert)\n            .select();\n\n          insertedQuestions = result.data;\n          questionsError = result.error;\n        } catch (error: any) {\n          console.warn(\n            \"Quiz questions table may not exist yet:\",\n            error.message\n          );\n          questionsError = error;\n        }\n\n        if (questionsError) {\n          console.warn(\n            \"Could not save quiz questions (table may not exist):\",\n            questionsError\n          );\n          // Don't fail the entire operation - just save the quiz without questions for now\n        }\n      }\n\n      console.log(\"✅ Quiz saved successfully!\");\n\n      // Create a simplified quiz response that matches what the frontend expects\n      const fullQuiz = {\n        id: savedQuiz.id.toString(),\n        title: savedQuiz.name,\n        questions: insertedQuestions\n          ? insertedQuestions.map((q) => ({\n              id: q.id.toString(),\n              questionText: q.question_text,\n              type: q.type,\n              options: q.options,\n              correctAnswer: q.correct_answer,\n              explanation: q.explanation || undefined,\n            }))\n          : generatedQs.map((q, index) => ({\n              id: `temp_${index}`,\n              questionText: q.questionText,\n              type: q.type,\n              options: q.options,\n              correctAnswer: q.correctAnswer,\n              explanation: q.explanation || undefined,\n            })),\n      };\n\n      // Return consistent format for both request types\n      return c.json(\n        {\n          success: true,\n          quiz: fullQuiz,\n          quizId: savedQuiz.id.toString(),\n          id: savedQuiz.id.toString(),\n          name: savedQuiz.name,\n        },\n        201\n      );\n    } catch (error: any) {\n      console.error(\"❌ Error in /generate quiz route:\", error);\n      console.error(\"Error stack:\", error.stack);\n      return c.json(\n        { error: error.message || \"An unexpected error occurred\" },\n        500\n      );\n    }\n  }\n);\n\n// Route to fetch a specific quiz and its questions\nquizRoutes.get(\"/:quizId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const quizId = c.req.param(\"quizId\");\n\n  if (!quizId) {\n    return c.json({ error: \"Invalid quiz ID\" }, 400);\n  }\n\n  try {\n    // First get the quiz basic info\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .select(\"*\")\n      .eq(\"id\", quizId)\n      .eq(\"user_id\", user.id) // Ensure user owns the quiz\n      .single();\n\n    if (quizError || !quiz) {\n      console.error(\"Error fetching quiz:\", quizError);\n      return c.json({ error: \"Quiz not found or access denied\" }, 404);\n    }\n\n    // Try to get quiz questions if the table exists\n    try {\n      const { data: questions, error: questionsError } = await supabase\n        .from(\"quiz_questions\")\n        .select(\n          \"id, question_text, type, options, correct_answer, explanation, created_at\"\n        )\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: true });\n\n      if (!questionsError && questions) {\n        quiz.quiz_questions = questions.map((q: any) => ({\n          id: q.id,\n          question_text: q.question_text,\n          type: q.type,\n          options: q.options,\n          correct_answer: q.correct_answer,\n          explanation: q.explanation,\n          created_at: q.created_at,\n        }));\n      } else {\n        console.warn(\n          \"Could not fetch quiz questions (table may not exist):\",\n          questionsError\n        );\n        quiz.quiz_questions = [];\n      }\n    } catch (error: any) {\n      console.warn(\"Quiz questions table may not exist:\", error.message);\n      quiz.quiz_questions = [];\n    }\n\n    return c.json(quiz, 200);\n  } catch (error: any) {\n    console.error(\"Error fetching quiz by ID:\", error);\n    return c.json({ error: error.message || \"Failed to fetch quiz\" }, 500);\n  }\n});\n\n// Route to fetch all quizzes for a specific document (owned by the user)\nquizRoutes.get(\n  \"/document/:documentId\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const documentId = c.req.param(\"documentId\");\n\n    if (!documentId) {\n      return c.json({ error: \"Invalid document ID\" }, 400);\n    }\n\n    try {\n      const { data: docData, error: docError } = await supabase\n        .from(\"study_documents\")\n        .select(\"id\")\n        .eq(\"id\", documentId)\n        .eq(\"user_id\", user.id)\n        .single();\n\n      if (docError || !docData) {\n        return c.json({ error: \"Document not found or access denied.\" }, 404);\n      }\n\n      const { data: quizzesData, error: quizzesError } = await supabase\n        .from(\"quizzes\")\n        .select(\"id, name, created_at, study_document_id\")\n        .eq(\"study_document_id\", documentId)\n        .eq(\"user_id\", user.id)\n        .order(\"created_at\", { ascending: false });\n\n      if (quizzesError) {\n        console.error(\"Error fetching quizzes for document:\", quizzesError);\n        return c.json(\n          { error: \"Failed to fetch quizzes for the document\" },\n          500\n        );\n      }\n\n      return c.json(quizzesData || [], 200);\n    } catch (error: any) {\n      console.error(\"Error fetching quizzes by document ID:\", error);\n      return c.json({ error: error.message || \"Failed to fetch quizzes\" }, 500);\n    }\n  }\n);\n\n// Add a testing endpoint to verify the router is working properly\nquizRoutes.get(\"/test\", async (c: Context) => {\n  console.log(\"Test endpoint hit successfully!\");\n  return c.json({ status: \"success\", message: \"Quiz routes are working!\" });\n});\n\n// Route to update SRS data for a quiz question\nquizRoutes.patch(\n  \"/questions/:questionId/srs\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const questionId = c.req.param(\"questionId\");\n\n    try {\n      const body = await c.req.json();\n      const {\n        srs_level,\n        due_at,\n        last_reviewed_at,\n        srs_interval,\n        srs_ease_factor,\n        srs_repetitions,\n        srs_correct_streak,\n      } = body;\n\n      if (!questionId) {\n        return c.json({ error: \"Question ID is required\" }, 400);\n      }\n\n      // Verify the question belongs to the user\n      const { data: questionCheck, error: checkError } = await supabase\n        .from(\"quiz_questions\")\n        .select(\"user_id\")\n        .eq(\"id\", questionId)\n        .single();\n\n      if (checkError || !questionCheck) {\n        return c.json({ error: \"Question not found\" }, 404);\n      }\n\n      if (questionCheck.user_id !== user.id) {\n        return c.json(\n          { error: \"Unauthorized: Question does not belong to this user\" },\n          403\n        );\n      }\n\n      // Update the SRS fields\n      const updateData: any = {};\n      if (srs_level !== undefined) updateData.srs_level = srs_level;\n      if (due_at !== undefined) updateData.due_at = due_at;\n      if (last_reviewed_at !== undefined)\n        updateData.last_reviewed_at = last_reviewed_at;\n      if (srs_interval !== undefined) updateData.srs_interval = srs_interval;\n      if (srs_ease_factor !== undefined)\n        updateData.srs_ease_factor = srs_ease_factor;\n      if (srs_repetitions !== undefined)\n        updateData.srs_repetitions = srs_repetitions;\n      if (srs_correct_streak !== undefined)\n        updateData.srs_correct_streak = srs_correct_streak;\n\n      if (Object.keys(updateData).length === 0) {\n        return c.json({ error: \"No SRS fields provided for update\" }, 400);\n      }\n\n      const { data: updatedQuestion, error: updateError } = await supabase\n        .from(\"quiz_questions\")\n        .update(updateData)\n        .eq(\"id\", questionId)\n        .eq(\"user_id\", user.id)\n        .select()\n        .single();\n\n      if (updateError) {\n        console.error(\"Error updating question SRS:\", updateError);\n        return c.json(\n          {\n            error: \"Failed to update question SRS\",\n            details: updateError.message,\n          },\n          500\n        );\n      }\n\n      return c.json({ success: true, question: updatedQuestion }, 200);\n    } catch (error: any) {\n      console.error(\"Error in SRS update:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to create a new quiz manually (without AI generation)\nquizRoutes.post(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n\n  try {\n    const body = await c.req.json();\n    const { name, description, study_document_id } = body;\n\n    if (!name || typeof name !== \"string\" || name.trim().length === 0) {\n      return c.json(\n        { error: \"Name is required and must be a non-empty string\" },\n        400\n      );\n    }\n\n    const quizData = {\n      name: name.trim(),\n      description: description || null,\n      study_document_id: study_document_id || null,\n      user_id: user.id,\n    };\n\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .insert(quizData)\n      .select(\"id, name\")\n      .single();\n\n    if (quizError) {\n      console.error(\"Error creating quiz:\", quizError);\n      return c.json(\n        { error: \"Failed to create quiz\", details: quizError.message },\n        500\n      );\n    }\n\n    return c.json({ id: quiz.id, name: quiz.name }, 201);\n  } catch (error: any) {\n    console.error(\"Error in manual quiz creation:\", error);\n    return c.json(\n      { error: \"An unexpected error occurred\", details: error.message },\n      500\n    );\n  }\n});\n\n// Route to fetch all quizzes for the authenticated user\nquizRoutes.get(\"/\", async (c: Context<{ Variables: AppVariables }>) => {\n  const user = c.get(\"user\");\n  console.log(`quizRoutes: GET / main handler reached. User ID: ${user?.id}`); // Log 5\n  console.log(\"quizRoutes: GET / handler triggered\"); // Added for debugging\n  try {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    // Defensive checks, though middleware should handle auth and set these\n    if (!supabase) {\n      console.error(\n        \"GET /api/quizzes/ handler: Supabase client not found in context.\"\n      );\n      return c.json(\n        {\n          error:\n            \"Internal Server Configuration Error: Supabase client not available.\",\n        },\n        500\n      );\n    }\n    if (!user || !user.id) {\n      console.error(\n        \"GET /api/quizzes/ handler: User not found in context or user ID missing.\"\n      );\n      return c.json(\n        {\n          error: \"Authentication Error: User information not available.\",\n          details: \"User context is invalid or missing.\",\n        },\n        500\n      );\n    }\n\n    const { data: quizzesData, error: quizzesError } = await supabase\n      .from(\"quizzes\")\n      .select(\"id, name, created_at, study_document_id\")\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\", { ascending: false });\n\n    if (quizzesError) {\n      console.error(\"Error fetching user quizzes from Supabase:\", quizzesError);\n      return c.json(\n        {\n          error: \"Failed to fetch user quizzes\",\n          details:\n            quizzesError.message || \"Database query encountered an error.\",\n        },\n        500\n      );\n    }\n\n    return c.json({ quizzes: quizzesData || [] }, 200);\n  } catch (error: any) {\n    console.error(\"Unexpected error in GET /api/quizzes/ handler:\", error);\n    return c.json(\n      {\n        error: \"An unexpected server error occurred while fetching quizzes.\",\n        details: error.message || \"Unknown internal server error.\",\n      },\n      500\n    );\n  }\n});\n\n// Route to delete a quiz and its questions\nquizRoutes.delete(\n  \"/:quizId\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Invalid quiz ID\" }, 400);\n    }\n\n    try {\n      // First, delete all questions associated with this quiz and user\n      const { error: questionsDeleteError } = await supabase\n        .from(\"quiz_questions\")\n        .delete()\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id); // Ensure user owns the questions\n\n      if (questionsDeleteError) {\n        console.error(\"Error deleting quiz questions:\", questionsDeleteError);\n        return c.json(\n          {\n            error: \"Failed to delete quiz questions\",\n            details: questionsDeleteError.message,\n          },\n          500\n        );\n      }\n\n      // Then, delete the quiz itself\n      const { error: quizDeleteError } = await supabase\n        .from(\"quizzes\")\n        .delete()\n        .eq(\"id\", quizId)\n        .eq(\"user_id\", user.id); // Ensure user owns the quiz\n\n      if (quizDeleteError) {\n        console.error(\"Error deleting quiz:\", quizDeleteError);\n        return c.json(\n          { error: \"Failed to delete quiz\", details: quizDeleteError.message },\n          500\n        );\n      }\n\n      return c.json(\n        {\n          success: true,\n          message: \"Quiz and associated questions deleted successfully.\",\n        },\n        200\n      );\n    } catch (error: any) {\n      console.error(\"Error deleting quiz:\", error);\n      return c.json({ error: error.message || \"Failed to delete quiz\" }, 500);\n    }\n  }\n);\n\n// Custom Not Found handler for quizRoutes\nquizRoutes.notFound((c) => {\n  console.error(\n    `[quizRoutes] Not Found: Path ${c.req.path} with method ${c.req.method} was not matched.`\n  );\n  return c.json(\n    { error: \"Quiz route not found\", path: c.req.path, method: c.req.method },\n    404\n  );\n});\n\n// Route to add a batch of questions to an existing quiz\nquizRoutes.post('/:quizId/questions/batch', async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get('supabase');\n  const user = c.get('user');\n  const quizId = c.req.param('quizId');\n\n  try {\n    const { questions: questionsPayload } = await c.req.json<{ questions: QuizQuestionBatchInsertPayload[] }>();\n\n    if (!quizId) {\n      return c.json({ error: 'Quiz ID is required' }, 400);\n    }\n\n    if (!Array.isArray(questionsPayload) || questionsPayload.length === 0) {\n      return c.json({ error: 'Questions payload must be a non-empty array' }, 400);\n    }\n\n    // Verify user owns the quiz\n    const { data: quiz, error: quizError } = await supabase\n      .from('quizzes')\n      .select('id, user_id')\n      .eq('id', quizId)\n      .single();\n\n    if (quizError || !quiz) {\n      return c.json({ error: 'Quiz not found' }, 404);\n    }\n    if (quiz.user_id !== user.id) {\n      return c.json({ error: 'Forbidden: You do not own this quiz' }, 403);\n    }\n\n    // Prepare questions for insertion\n    const questionsToInsert = questionsPayload.map(q => ({\n      question_text: q.question_text,\n      type: q.type, // Assuming q.type is already a valid enum string\n      options: q.options || null,\n      correct_answer: q.correct_answer || null,\n      explanation: q.explanation || null,\n      quiz_id: quizId,\n      user_id: user.id,\n    }));\n\n    const { data: insertedQuestions, error: insertError } = await supabase\n      .from('quiz_questions')\n      .insert(questionsToInsert)\n      .select();\n\n    if (insertError) {\n      console.error('Error inserting batch questions:', insertError);\n      return c.json({ error: 'Failed to add questions to quiz', details: insertError.message }, 500);\n    }\n\n    return c.json(insertedQuestions, 201);\n  } catch (error: any) {\n    console.error('Error in /:quizId/questions/batch route:', error);\n    return c.json({ error: 'An unexpected error occurred', details: error.message }, 500);\n  }\n});\n\n// Route to fetch a specific quiz and its questions\nquizRoutes.get(\"/:quizId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const quizId = c.req.param(\"quizId\");\n\n  if (!quizId) {\n    return c.json({ error: \"Invalid quiz ID\" }, 400);\n  }\n\n  try {\n    // Fetch the quiz\n    const { data: quiz, error: quizError } = await supabase\n      .from(\"quizzes\")\n      .select(\"*\")\n      .eq(\"id\", quizId)\n      .single();\n\n    if (quizError) {\n      console.error(\"Error fetching quiz:\", quizError);\n      return c.json(\n        { error: \"Failed to fetch quiz\", details: quizError.message },\n        quizError.code === \"PGRST116\" ? 404 : 500\n      );\n    }\n\n    // Check if user owns the quiz\n    if (quiz.user_id !== user.id) {\n      return c.json(\n        { error: \"You do not have permission to access this quiz\" },\n        403\n      );\n    }\n\n    // Fetch questions for this quiz\n    const { data: questions, error: questionsError } = await supabase\n      .from(\"quiz_questions\")\n      .select(\"*\")\n      .eq(\"quiz_id\", quizId)\n      .eq(\"user_id\", user.id)\n      .order(\"created_at\");\n\n    if (questionsError) {\n      console.error(\"Error fetching quiz questions:\", questionsError);\n      return c.json(\n        {\n          error: \"Failed to fetch quiz questions\",\n          details: questionsError.message,\n        },\n        500\n      );\n    }\n\n    // Return the quiz with its questions\n    return c.json({\n      ...quiz,\n      questions: questions || [],\n    });\n  } catch (error: any) {\n    console.error(\"Error fetching quiz by ID:\", error);\n    return c.json({ error: error.message || \"Failed to fetch quiz\" }, 500);\n  }\n});\n\n// Route to track quiz completion\nquizRoutes.post(\n  \"/:quizId/complete\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Quiz ID is required\" }, 400);\n    }\n\n    try {\n      const body = await c.req.json();\n      const completionData: QuizCompletionData = body;\n\n      // Validate required fields\n      if (!completionData.score || !completionData.questions_answered) {\n        return c.json({ error: \"Score and questions answered are required\" }, 400);\n      }\n\n      // Verify the quiz belongs to the user\n      const { data: quiz, error: quizError } = await supabase\n        .from(\"quizzes\")\n        .select(\"user_id\")\n        .eq(\"id\", quizId)\n        .single();\n\n      if (quizError || !quiz) {\n        return c.json({ error: \"Quiz not found\" }, 404);\n      }\n\n      if (quiz.user_id !== user.id) {\n        return c.json(\n          { error: \"Unauthorized: Quiz does not belong to this user\" },\n          403\n        );\n      }\n\n      // Create completion record\n      const completionInsert: UserCompletionInsert = {\n        user_id: user.id,\n        quiz_id: quizId,\n        completion_type: \"quiz\",\n        completed_at: new Date().toISOString(),\n        score: completionData.score,\n        time_spent_minutes: completionData.time_spent_minutes,\n        questions_answered: completionData.questions_answered,\n        correct_answers: completionData.correct_answers,\n        metadata: completionData.metadata || null,\n      };\n\n      const { data: completion, error: insertError } = await supabase\n        .from(\"user_completions\")\n        .insert(completionInsert)\n        .select()\n        .single();\n\n      if (insertError) {\n        console.error(\"Error recording quiz completion:\", insertError);\n        return c.json(\n          { error: \"Failed to record completion\", details: insertError.message },\n          500\n        );\n      }\n\n      return c.json(completion, 201);\n    } catch (error: any) {\n      console.error(\"Error in quiz completion endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get completion statistics for the user\nquizRoutes.get(\n  \"/stats/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      // Get all user completions\n      const { data: completions, error: completionsError } = await supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id)\n        .order(\"completed_at\", { ascending: false });\n\n      if (completionsError) {\n        console.error(\"Error fetching completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      const allCompletions = completions || [];\n      const quizCompletions = allCompletions.filter(c => c.completion_type === \"quiz\");\n      const flashcardCompletions = allCompletions.filter(c => c.completion_type === \"flashcard_set\");\n\n      // Calculate statistics\n      const stats: CompletionStats = {\n        total_completions: allCompletions.length,\n        quiz_completions: quizCompletions.length,\n        flashcard_completions: flashcardCompletions.length,\n        average_quiz_score: quizCompletions.length > 0\n          ? Math.round(quizCompletions.reduce((sum, c) => sum + (c.score || 0), 0) / quizCompletions.length)\n          : 0,\n        total_study_time_minutes: allCompletions.reduce((sum, c) => sum + (c.time_spent_minutes || 0), 0),\n        best_quiz_score: quizCompletions.length > 0\n          ? Math.max(...quizCompletions.map(c => c.score || 0))\n          : 0,\n        recent_completions: allCompletions.slice(0, 10),\n        completion_streak: calculateCompletionStreak(allCompletions),\n        this_week_completions: getCompletionsInTimeRange(allCompletions, 7),\n        this_month_completions: getCompletionsInTimeRange(allCompletions, 30),\n      };\n\n      return c.json(stats);\n    } catch (error: any) {\n      console.error(\"Error fetching completion stats:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get user's quiz completion data\nquizRoutes.get(\n  \"/:quizId/user-completion\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n    const quizId = c.req.param(\"quizId\");\n\n    if (!quizId) {\n      return c.json({ error: \"Quiz ID is required\" }, 400);\n    }\n\n    try {\n      // Fetch user's completion data for this quiz\n      const { data, error } = await supabase\n        .from(\"quiz_completions\")\n        .select(\"*\")\n        .eq(\"quiz_id\", quizId)\n        .eq(\"user_id\", user.id)\n        .single();\n\n      if (error) {\n        throw error;\n      }\n\n      return c.json(\n        { success: true, completionData: data },\n        200\n      );\n    } catch (error: any) {\n      console.error(\"Error fetching user completion data:\", error);\n      return c.json(\n        { error: \"Failed to fetch user completion data\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Route to get user's completion history with filtering\nquizRoutes.get(\n  \"/completions\",\n  async (c: Context<{ Variables: AppVariables }>) => {\n    const supabase = c.get(\"supabase\");\n    const user = c.get(\"user\");\n\n    try {\n      const queryParams = c.req.query();\n      const filters: CompletionFilters = {\n        completion_type: queryParams.type as 'quiz' | 'flashcard_set' | undefined,\n        quiz_id: queryParams.quiz_id,\n        flashcard_set_id: queryParams.flashcard_set_id,\n        min_score: queryParams.min_score ? parseInt(queryParams.min_score) : undefined,\n        limit: queryParams.limit ? parseInt(queryParams.limit) : 50,\n        offset: queryParams.offset ? parseInt(queryParams.offset) : 0,\n      };\n\n      if (queryParams.start_date && queryParams.end_date) {\n        filters.date_range = {\n          start: queryParams.start_date,\n          end: queryParams.end_date,\n        };\n      }\n\n      // Build query\n      let query = supabase\n        .from(\"user_completions\")\n        .select(\"*\")\n        .eq(\"user_id\", user.id);\n\n      if (filters.completion_type) {\n        query = query.eq(\"completion_type\", filters.completion_type);\n      }\n\n      if (filters.quiz_id) {\n        query = query.eq(\"quiz_id\", filters.quiz_id);\n      }\n\n      if (filters.flashcard_set_id) {\n        query = query.eq(\"flashcard_set_id\", filters.flashcard_set_id);\n      }\n\n      if (filters.min_score) {\n        query = query.gte(\"score\", filters.min_score);\n      }\n\n      if (filters.date_range) {\n        query = query\n          .gte(\"completed_at\", filters.date_range.start)\n          .lte(\"completed_at\", filters.date_range.end);\n      }\n\n      if (filters.limit) {\n        query = query.limit(filters.limit);\n      }\n\n      if (filters.offset) {\n        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n      }\n\n      query = query.order(\"completed_at\", { ascending: false });\n\n      const { data: completions, error: completionsError } = await query;\n\n      if (completionsError) {\n        console.error(\"Error fetching filtered completions:\", completionsError);\n        return c.json(\n          { error: \"Failed to fetch completions\", details: completionsError.message },\n          500\n        );\n      }\n\n      return c.json(completions || []);\n    } catch (error: any) {\n      console.error(\"Error in completions endpoint:\", error);\n      return c.json(\n        { error: \"An unexpected error occurred\", details: error.message },\n        500\n      );\n    }\n  }\n);\n\n// Helper functions for completion statistics\nfunction calculateCompletionStreak(completions: any[]): number {\n  if (completions.length === 0) return 0;\n\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  \n  let streak = 0;\n  let currentDate = new Date(today);\n  \n  for (let i = 0; i < 30; i++) { // Check last 30 days\n    const dayCompletions = completions.filter(c => {\n      const completionDate = new Date(c.completed_at);\n      completionDate.setHours(0, 0, 0, 0);\n      return completionDate.getTime() === currentDate.getTime();\n    });\n    \n    if (dayCompletions.length > 0) {\n      streak++;\n    } else if (streak > 0) {\n      break; // Streak is broken\n    }\n    \n    currentDate.setDate(currentDate.getDate() - 1);\n  }\n  \n  return streak;\n}\n\nfunction getCompletionsInTimeRange(completions: any[], days: number): number {\n  const cutoffDate = new Date();\n  cutoffDate.setDate(cutoffDate.getDate() - days);\n  \n  return completions.filter(c => new Date(c.completed_at) >= cutoffDate).length;\n}\n\nexport default quizRoutes;\n"}