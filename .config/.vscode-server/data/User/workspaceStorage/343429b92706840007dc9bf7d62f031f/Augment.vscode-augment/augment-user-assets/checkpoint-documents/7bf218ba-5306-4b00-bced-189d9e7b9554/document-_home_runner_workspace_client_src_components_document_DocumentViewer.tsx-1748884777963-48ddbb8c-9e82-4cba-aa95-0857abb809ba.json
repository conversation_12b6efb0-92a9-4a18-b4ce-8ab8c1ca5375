{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/document/DocumentViewer.tsx"}, "originalCode": "import React, { useEffect, useState } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { Tables } from \"../../types/supabase\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { X, FileText, Download } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport MarkdownRenderer from \"./MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface DocumentViewerProps {\n  document: StudyDocument;\n  onClose: () => void;\n}\n\nconst API_BASE_URL = import.meta.env.VITE_API_BASE_URL ||\n  (import.meta.env.DEV ? \"http://localhost:5000/api\" : \"/api\");\n\n// Helper to get auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n    return session?.access_token || null;\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n}\n\nexport const DocumentViewer: React.FC<DocumentViewerProps> = ({\n  document,\n  onClose,\n}) => {\n  const [content, setContent] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleEsc = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") {\n        onClose();\n      }\n    };\n    window.addEventListener(\"keydown\", handleEsc);\n    return () => {\n      window.removeEventListener(\"keydown\", handleEsc);\n    };\n  }, [onClose]);\n\n  useEffect(() => {\n    const fetchDocumentContent = async () => {\n      if (!document?.id) {\n        setContent(null);\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setContent(null);\n\n      try {\n        const token = await getAuthToken();\n        if (!token) {\n          throw new Error(\"Authentication required\");\n        }\n\n        // Fetch content from secure backend endpoint\n        const response = await fetch(\n          `${API_BASE_URL}/documents/${document.id}/content`,\n          {\n            method: \"GET\",\n            headers: {\n              Authorization: `Bearer ${token}`,\n            },\n          }\n        );\n\n        if (!response.ok) {\n          const errorData = await response.json().catch(() => ({}));\n          throw new Error(\n            errorData.error || `HTTP ${response.status}: ${response.statusText}`\n          );\n        }\n\n        const textContent = await response.text();\n        setContent(textContent);\n      } catch (err: any) {\n        console.error(\"Error fetching document content:\", err);\n        setError(err.message || \"Failed to load document content.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDocumentContent();\n  }, [document]);\n\n  const handleDownload = async () => {\n    if (!content || !document) return;\n\n    try {\n      const blob = new Blob([content], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `${document.file_name}_extracted.txt`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error(\"Error downloading content:\", err);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <Card className=\"w-full max-w-4xl h-[80vh] bg-slate-800 border-slate-700 flex flex-col\">\n        <CardHeader className=\"flex flex-row items-center justify-between py-4\">\n          <div className=\"flex items-center space-x-2\">\n            <FileText className=\"h-5 w-5 text-purple-400\" />\n            <CardTitle className=\"text-lg text-purple-400\">\n              {document?.file_name || \"Document Viewer\"}\n            </CardTitle>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {content && (\n              <Button\n                onClick={handleDownload}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Download\n              </Button>\n            )}\n            <Button\n              onClick={onClose}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"flex-1 p-6 overflow-hidden\">\n          {loading && (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <Spinner size=\"lg\" />\n                <p className=\"text-slate-300 mt-4\">\n                  Loading document content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <p className=\"text-red-400 mb-4\">{error}</p>\n                <Button\n                  onClick={() => window.location.reload()}\n                  variant=\"outline\"\n                  className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n                >\n                  Retry\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {content && !loading && !error && (\n            <ScrollArea className=\"h-full w-full\">\n              <div className=\"bg-slate-900 p-6 rounded-lg\">\n                <div className=\"mb-4 text-sm text-slate-400\">\n                  <p>\n                    <strong>File:</strong> {document.file_name}\n                  </p>\n                  <p>\n                    <strong>Type:</strong> {document.content_type}\n                  </p>\n                  <p>\n                    <strong>Size:</strong>{\" \"}\n                    {document.size_bytes\n                      ? `${(document.size_bytes / 1024).toFixed(1)} KB`\n                      : \"Unknown\"}\n                  </p>\n                  <p>\n                    <strong>Status:</strong>{\" \"}\n                    <span className=\"text-green-400\">{document.status}</span>\n                  </p>\n                </div>\n                <div className=\"border-t border-slate-700 pt-4\">\n                  <h3 className=\"text-slate-300 font-medium mb-3\">\n                    Extracted Content:\n                  </h3>\n                  <MarkdownRenderer\n                    content={content}\n                    className=\"text-sm leading-relaxed\"\n                  />\n                </div>\n              </div>\n            </ScrollArea>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n", "modifiedCode": "import React, { useEffect, useState } from \"react\";\nimport { supabase } from \"../../lib/supabaseClient\";\nimport { Tables } from \"../../types/supabase\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { X, FileText, Download } from \"lucide-react\";\nimport Spinner from \"@/components/ui/Spinner\";\nimport MarkdownRenderer from \"./MarkdownRenderer\";\n\ntype StudyDocument = Tables<\"study_documents\">;\n\ninterface DocumentViewerProps {\n  document: StudyDocument;\n  onClose: () => void;\n}\n\nconst API_BASE_URL = import.meta.env.VITE_API_BASE_URL ||\n  (import.meta.env.DEV ? \"http://localhost:5000/api\" : \"/api\");\n\n// Helper to get auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n    return session?.access_token || null;\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n}\n\nexport const DocumentViewer: React.FC<DocumentViewerProps> = ({\n  document,\n  onClose,\n}) => {\n  const [content, setContent] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleEsc = (event: KeyboardEvent) => {\n      if (event.key === \"Escape\") {\n        onClose();\n      }\n    };\n    window.addEventListener(\"keydown\", handleEsc);\n    return () => {\n      window.removeEventListener(\"keydown\", handleEsc);\n    };\n  }, [onClose]);\n\n  useEffect(() => {\n    const fetchDocumentContent = async () => {\n      if (!document?.id) {\n        setContent(null);\n        setLoading(false);\n        return;\n      }\n\n      setLoading(true);\n      setError(null);\n      setContent(null);\n\n      try {\n        const token = await getAuthToken();\n        if (!token) {\n          throw new Error(\"Authentication required\");\n        }\n\n        const url = `${API_BASE_URL}/documents/${document.id}/content`;\n        console.log(\"Fetching document content from:\", url);\n        console.log(\"Using token:\", token ? \"✓ Present\" : \"✗ Missing\");\n\n        // Fetch content from secure backend endpoint\n        const response = await fetch(url, {\n          method: \"GET\",\n          headers: {\n            \"Authorization\": `Bearer ${token}`,\n            \"Content-Type\": \"application/json\",\n          },\n        });\n\n        console.log(\"Response status:\", response.status);\n        console.log(\"Response headers:\", Object.fromEntries(response.headers.entries()));\n\n        if (!response.ok) {\n          let errorData;\n          try {\n            errorData = await response.json();\n          } catch {\n            errorData = { error: `HTTP ${response.status}: ${response.statusText}` };\n          }\n          console.error(\"API Error:\", errorData);\n          throw new Error(\n            errorData.error || `HTTP ${response.status}: ${response.statusText}`\n          );\n        }\n\n        const textContent = await response.text();\n        console.log(\"Content fetched successfully, length:\", textContent.length);\n        setContent(textContent);\n      } catch (err: any) {\n        console.error(\"Error fetching document content:\", err);\n        console.error(\"Error details:\", {\n          name: err.name,\n          message: err.message,\n          stack: err.stack\n        });\n        setError(err.message || \"Failed to load document content.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDocumentContent();\n  }, [document]);\n\n  const handleDownload = async () => {\n    if (!content || !document) return;\n\n    try {\n      const blob = new Blob([content], { type: \"text/plain\" });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement(\"a\");\n      a.href = url;\n      a.download = `${document.file_name}_extracted.txt`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (err) {\n      console.error(\"Error downloading content:\", err);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n      <Card className=\"w-full max-w-4xl h-[80vh] bg-slate-800 border-slate-700 flex flex-col\">\n        <CardHeader className=\"flex flex-row items-center justify-between py-4\">\n          <div className=\"flex items-center space-x-2\">\n            <FileText className=\"h-5 w-5 text-purple-400\" />\n            <CardTitle className=\"text-lg text-purple-400\">\n              {document?.file_name || \"Document Viewer\"}\n            </CardTitle>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            {content && (\n              <Button\n                onClick={handleDownload}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Download\n              </Button>\n            )}\n            <Button\n              onClick={onClose}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </CardHeader>\n\n        <CardContent className=\"flex-1 p-6 overflow-hidden\">\n          {loading && (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <Spinner size=\"lg\" />\n                <p className=\"text-slate-300 mt-4\">\n                  Loading document content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {error && (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center\">\n                <p className=\"text-red-400 mb-4\">{error}</p>\n                <Button\n                  onClick={() => window.location.reload()}\n                  variant=\"outline\"\n                  className=\"text-slate-300 border-slate-600 hover:bg-slate-700\"\n                >\n                  Retry\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {content && !loading && !error && (\n            <ScrollArea className=\"h-full w-full\">\n              <div className=\"bg-slate-900 p-6 rounded-lg\">\n                <div className=\"mb-4 text-sm text-slate-400\">\n                  <p>\n                    <strong>File:</strong> {document.file_name}\n                  </p>\n                  <p>\n                    <strong>Type:</strong> {document.content_type}\n                  </p>\n                  <p>\n                    <strong>Size:</strong>{\" \"}\n                    {document.size_bytes\n                      ? `${(document.size_bytes / 1024).toFixed(1)} KB`\n                      : \"Unknown\"}\n                  </p>\n                  <p>\n                    <strong>Status:</strong>{\" \"}\n                    <span className=\"text-green-400\">{document.status}</span>\n                  </p>\n                </div>\n                <div className=\"border-t border-slate-700 pt-4\">\n                  <h3 className=\"text-slate-300 font-medium mb-3\">\n                    Extracted Content:\n                  </h3>\n                  <MarkdownRenderer\n                    content={content}\n                    className=\"text-sm leading-relaxed\"\n                  />\n                </div>\n              </div>\n            </ScrollArea>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"}