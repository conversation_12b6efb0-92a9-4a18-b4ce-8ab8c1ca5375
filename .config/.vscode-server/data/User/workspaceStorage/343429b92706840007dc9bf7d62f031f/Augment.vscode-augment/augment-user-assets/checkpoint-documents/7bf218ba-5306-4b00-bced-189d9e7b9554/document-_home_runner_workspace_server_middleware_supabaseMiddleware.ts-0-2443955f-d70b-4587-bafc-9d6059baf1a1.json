{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/supabaseMiddleware.ts"}, "originalCode": "import { Context, Next } from 'hono';\nimport { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\n\n// Create Supabase client outside the middleware to avoid creating a new client on each request\nconst supabaseUrl = supabaseConfig.url;\nconst supabaseServiceKey = supabaseConfig.serviceRoleKey;\n\nconsole.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');\n\n// Create a single Supabase client instance\nexport const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);\n\n// Middleware to attach the Supabase client to the Hono context\nexport const supabaseMiddleware = async (c: Context, next: Next) => {\n  if (!supabaseClient) {\n    console.error('Supabase client initialization failed');\n    return c.json({ \n      error: 'Internal Server Configuration Error: Supabase client initialization failed.' \n    }, 500);\n  }\n  \n  // Attach the Supabase client to the context\n  c.set('supabase', supabaseClient);\n  \n  // Log that the middleware is working with request details\n  console.log(`Supabase client attached to request context for ${c.req.method} ${c.req.url}`);\n  console.log(`Request headers: ${JSON.stringify(Object.fromEntries(c.req.raw.headers.entries()))}`);\n  \n  await next();\n};\n", "modifiedCode": "import { Context, Next } from 'hono';\nimport { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\n\n// Create Supabase client outside the middleware to avoid creating a new client on each request\nconst supabaseUrl = supabaseConfig.url;\nconst supabaseServiceKey = supabaseConfig.serviceRoleKey;\n\nconsole.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');\n\n// Create a single Supabase client instance\nexport const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);\n\n// Middleware to attach the Supabase client to the Hono context\nexport const supabaseMiddleware = async (c: Context, next: Next) => {\n  if (!supabaseClient) {\n    console.error('Supabase client initialization failed');\n    return c.json({ \n      error: 'Internal Server Configuration Error: Supabase client initialization failed.' \n    }, 500);\n  }\n  \n  // Attach the Supabase client to the context\n  c.set('supabase', supabaseClient);\n  \n  // Log that the middleware is working with request details\n  console.log(`Supabase client attached to request context for ${c.req.method} ${c.req.url}`);\n  console.log(`Request headers: ${JSON.stringify(Object.fromEntries(c.req.raw.headers.entries()))}`);\n  \n  await next();\n};\n"}