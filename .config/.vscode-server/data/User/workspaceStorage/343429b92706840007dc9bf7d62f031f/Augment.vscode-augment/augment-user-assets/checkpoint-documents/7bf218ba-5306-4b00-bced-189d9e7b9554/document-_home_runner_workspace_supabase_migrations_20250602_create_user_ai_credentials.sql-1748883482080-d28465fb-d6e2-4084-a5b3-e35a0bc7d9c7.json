{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "supabase/migrations/20250602_create_user_ai_credentials.sql"}, "modifiedCode": "-- Create table for securely storing user AI provider credentials\nCREATE TABLE IF NOT EXISTS user_ai_credentials (\n    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\n    provider VARCHAR(50) NOT NULL,\n    encrypted_api_key TEXT NOT NULL,\n    encryption_iv VARCHAR(32) NOT NULL,\n    encryption_tag VARCHAR(32) NOT NULL,\n    base_url TEXT NOT NULL,\n    extraction_model VARCHAR(100),\n    generation_model VARCHAR(100),\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    \n    -- Ensure one credential set per user per provider\n    UNIQUE(user_id, provider)\n);\n\n-- Enable Row Level Security\nALTER TABLE user_ai_credentials ENABLE ROW LEVEL SECURITY;\n\n-- Create RLS policies\n-- Users can only access their own credentials\nCREATE POLICY \"Users can view own AI credentials\" ON user_ai_credentials\n    FOR SELECT USING (auth.uid() = user_id);\n\nCREATE POLICY \"Users can insert own AI credentials\" ON user_ai_credentials\n    FOR INSERT WITH CHECK (auth.uid() = user_id);\n\nCREATE POLICY \"Users can update own AI credentials\" ON user_ai_credentials\n    FOR UPDATE USING (auth.uid() = user_id);\n\nCREATE POLICY \"Users can delete own AI credentials\" ON user_ai_credentials\n    FOR DELETE USING (auth.uid() = user_id);\n\n-- Create indexes for performance\nCREATE INDEX idx_user_ai_credentials_user_id ON user_ai_credentials(user_id);\nCREATE INDEX idx_user_ai_credentials_provider ON user_ai_credentials(provider);\nCREATE INDEX idx_user_ai_credentials_user_provider ON user_ai_credentials(user_id, provider);\n\n-- Add comments for documentation\nCOMMENT ON TABLE user_ai_credentials IS 'Securely stores encrypted user AI provider credentials';\nCOMMENT ON COLUMN user_ai_credentials.encrypted_api_key IS 'AES-256-GCM encrypted API key';\nCOMMENT ON COLUMN user_ai_credentials.encryption_iv IS 'Initialization vector for encryption';\nCOMMENT ON COLUMN user_ai_credentials.encryption_tag IS 'Authentication tag for encryption verification';\nCOMMENT ON COLUMN user_ai_credentials.provider IS 'AI provider name (e.g., OpenRouter, OpenAI)';\nCOMMENT ON COLUMN user_ai_credentials.base_url IS 'API base URL for the provider';\nCOMMENT ON COLUMN user_ai_credentials.extraction_model IS 'Model used for text extraction tasks';\nCOMMENT ON COLUMN user_ai_credentials.generation_model IS 'Model used for content generation tasks';\n"}