{"path": {"rootPath": "/home/<USER>/workspace", "relPath": ".env.example"}, "originalCode": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n# Copy this file to .env and fill in your actual values\n# NEVER commit .env files to version control\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\n\n# Database Configuration (if using direct database access)\nVITE_DATABASE_PASSWORD=Aloha808!\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\n# Default AI provider settings - users will configure their own in the app\n\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\n# Generate secure random strings for production\n\nJWT_SECRET=michelle\nSESSION_SECRET=2019\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\n# Frontend URL for CORS configuration\n\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\n# For local SQLite storage if needed\n\nDATABASE_URL=./data/chewyai.sqlite\n\n# =============================================================================\n# PRODUCTION DEPLOYMENT NOTES\n# =============================================================================\n# For Replit deployment, set these in Replit Secrets:\n# - SUPABASE_URL\n# - SUPABASE_SERVICE_ROLE_KEY\n# - VITE_SUPABASE_URL\n# - VITE_SUPABASE_ANON_KEY\n# - NODE_ENV=production\n# - PORT=80\n#\n# The VITE_ variables will be automatically included in the client build\n# The server variables will only be available to the backend", "modifiedCode": "# =============================================================================\n# ChewyAI Environment Configuration\n# =============================================================================\n# Copy this file to .env and fill in your actual values\n# NEVER commit .env files to version control\n\n# =============================================================================\n# CLIENT ENVIRONMENT VARIABLES (VITE_ prefix for Vite)\n# =============================================================================\n# These are exposed to the browser - only include non-sensitive values\n\n# Supabase Configuration (Client)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ\n\n# API Configuration\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# =============================================================================\n# SERVER ENVIRONMENT VARIABLES\n# =============================================================================\n# These are only available on the server - include sensitive values here\n\n# Application Configuration\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=your-supabase-url-here\nSUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here\n\n# Database Configuration (if using direct database access)\nVITE_DATABASE_PASSWORD=your-database-password-here\n\n# =============================================================================\n# AI PROVIDER CONFIGURATION (Optional)\n# =============================================================================\n# Default AI provider settings - users will configure their own in the app\n\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash-preview-05-20\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro-preview\n\n# =============================================================================\n# SECURITY CONFIGURATION\n# =============================================================================\n# Generate secure random strings for production\n\nJWT_SECRET=michelle\nSESSION_SECRET=2019\n\n# =============================================================================\n# CORS CONFIGURATION\n# =============================================================================\n# Frontend URL for CORS configuration\n\nFRONTEND_URL=http://localhost:3000\n\n# =============================================================================\n# ADDITIONAL DATABASE CONFIGURATION (Optional)\n# =============================================================================\n# For local SQLite storage if needed\n\nDATABASE_URL=./data/chewyai.sqlite\n\n# =============================================================================\n# PRODUCTION DEPLOYMENT NOTES\n# =============================================================================\n# For Replit deployment, set these in Replit Secrets:\n# - SUPABASE_URL\n# - SUPABASE_SERVICE_ROLE_KEY\n# - VITE_SUPABASE_URL\n# - VITE_SUPABASE_ANON_KEY\n# - NODE_ENV=production\n# - PORT=80\n#\n# The VITE_ variables will be automatically included in the client build\n# The server variables will only be available to the backend"}