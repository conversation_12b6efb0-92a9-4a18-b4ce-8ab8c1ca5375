{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/QuizGenerationPopup.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogHeader,\n  <PERSON>alogTitle,\n  DialogTrigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n  customPrompt: string;\n}\n\ninterface QuizGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: QuizGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxQuestions?: number;\n}\n\nconst QUESTION_TYPE_OPTIONS = [\n  { id: \"multiple_choice\", label: \"Multiple Choice\" },\n  { id: \"select_all_that_apply\", label: \"Select All That Apply\" },\n  { id: \"true_false\", label: \"True/False\" },\n  { id: \"short_answer\", label: \"Short Answer\" },\n];\n\nexport const QuizGenerationPopup: React.FC<QuizGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxQuestions = 20,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<QuizGenerationOptions>({\n    numberOfQuestions: 10,\n    questionTypes: [\"multiple_choice\"],\n    customPrompt: \"\",\n  });\n\n  const handleQuestionTypeChange = (typeId: string) => {\n    setOptions((prev) => ({\n      ...prev,\n      questionTypes: prev.questionTypes.includes(typeId)\n        ? prev.questionTypes.filter((qt) => qt !== typeId)\n        : [...prev.questionTypes, typeId],\n    }));\n  };\n\n  const handleGenerate = async () => {\n    if (options.questionTypes.length === 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Quiz generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.questionTypes.length > 0 && options.numberOfQuestions > 0;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-slate-800 border-slate-700\">\n        <DialogHeader>\n          <DialogTitle className=\"text-slate-100\">Generate Quiz Options</DialogTitle>\n        </DialogHeader>\n\n        <div className=\"space-y-6 py-4\">\n          {/* Number of Questions */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfQuestions\" className=\"text-slate-200\">\n              Number of Questions\n            </Label>\n            <Input\n              id=\"numberOfQuestions\"\n              type=\"number\"\n              min=\"1\"\n              max={maxQuestions}\n              value={options.numberOfQuestions}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-slate-700 border-slate-600 text-slate-100\"\n            />\n          </div>\n\n          {/* Question Types */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-slate-200\">Question Types</Label>\n            <div className=\"space-y-2\">\n              {QUESTION_TYPE_OPTIONS.map((option) => (\n                <div key={option.id} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={option.id}\n                    checked={options.questionTypes.includes(option.id)}\n                    onCheckedChange={() => handleQuestionTypeChange(option.id)}\n                    className=\"border-slate-500 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600\"\n                  />\n                  <Label\n                    htmlFor={option.id}\n                    className=\"text-sm text-slate-300 cursor-pointer\"\n                  >\n                    {option.label}\n                  </Label>\n                </div>\n              ))}\n            </div>\n            {options.questionTypes.length === 0 && (\n              <p className=\"text-sm text-red-400\">\n                Please select at least one question type.\n              </p>\n            )}\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-slate-200\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"Add any specific instructions for the AI to follow when generating the quiz...\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-slate-700 border-slate-600 text-slate-100 min-h-[80px]\"\n            />\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-slate-700\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n            className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={!isValidOptions || isGenerating}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >            {isGenerating ? (\n              <div className=\"flex items-center\">\n                <Spinner size=\"sm\" />\n                <span className=\"ml-2\">Generating...</span>\n              </div>\n            ) : (\n              \"Generate Quiz\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n", "modifiedCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogDescription,\n  <PERSON>alogHeader,\n  <PERSON>alogTitle,\n  DialogTrigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n  customPrompt: string;\n}\n\ninterface QuizGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: QuizGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxQuestions?: number;\n}\n\nconst QUESTION_TYPE_OPTIONS = [\n  { id: \"multiple_choice\", label: \"Multiple Choice\" },\n  { id: \"select_all_that_apply\", label: \"Select All That Apply\" },\n  { id: \"true_false\", label: \"True/False\" },\n  { id: \"short_answer\", label: \"Short Answer\" },\n];\n\nexport const QuizGenerationPopup: React.FC<QuizGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxQuestions = 20,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<QuizGenerationOptions>({\n    numberOfQuestions: 10,\n    questionTypes: [\"multiple_choice\"],\n    customPrompt: \"\",\n  });\n\n  const handleQuestionTypeChange = (typeId: string) => {\n    setOptions((prev) => ({\n      ...prev,\n      questionTypes: prev.questionTypes.includes(typeId)\n        ? prev.questionTypes.filter((qt) => qt !== typeId)\n        : [...prev.questionTypes, typeId],\n    }));\n  };\n\n  const handleGenerate = async () => {\n    if (options.questionTypes.length === 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Quiz generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.questionTypes.length > 0 && options.numberOfQuestions > 0;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-slate-800 border-slate-700\">\n        <DialogHeader>\n          <DialogTitle className=\"text-slate-100\">Generate Quiz Options</DialogTitle>\n          <DialogDescription className=\"text-slate-400\">\n            Configure quiz generation settings including question types, number of questions, and custom instructions.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6 py-4\">\n          {/* Number of Questions */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfQuestions\" className=\"text-slate-200\">\n              Number of Questions\n            </Label>\n            <Input\n              id=\"numberOfQuestions\"\n              type=\"number\"\n              min=\"1\"\n              max={maxQuestions}\n              value={options.numberOfQuestions}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-slate-700 border-slate-600 text-slate-100\"\n            />\n          </div>\n\n          {/* Question Types */}\n          <div className=\"space-y-3\">\n            <Label className=\"text-slate-200\">Question Types</Label>\n            <div className=\"space-y-2\">\n              {QUESTION_TYPE_OPTIONS.map((option) => (\n                <div key={option.id} className=\"flex items-center space-x-2\">\n                  <Checkbox\n                    id={option.id}\n                    checked={options.questionTypes.includes(option.id)}\n                    onCheckedChange={() => handleQuestionTypeChange(option.id)}\n                    className=\"border-slate-500 data-[state=checked]:bg-purple-600 data-[state=checked]:border-purple-600\"\n                  />\n                  <Label\n                    htmlFor={option.id}\n                    className=\"text-sm text-slate-300 cursor-pointer\"\n                  >\n                    {option.label}\n                  </Label>\n                </div>\n              ))}\n            </div>\n            {options.questionTypes.length === 0 && (\n              <p className=\"text-sm text-red-400\">\n                Please select at least one question type.\n              </p>\n            )}\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-slate-200\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"Add any specific instructions for the AI to follow when generating the quiz...\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-slate-700 border-slate-600 text-slate-100 min-h-[80px]\"\n            />\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex justify-end space-x-3 pt-4 border-t border-slate-700\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n            className=\"border-slate-600 text-slate-300 hover:bg-slate-700\"\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={!isValidOptions || isGenerating}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >            {isGenerating ? (\n              <div className=\"flex items-center\">\n                <Spinner size=\"sm\" />\n                <span className=\"ml-2\">Generating...</span>\n              </div>\n            ) : (\n              \"Generate Quiz\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n"}