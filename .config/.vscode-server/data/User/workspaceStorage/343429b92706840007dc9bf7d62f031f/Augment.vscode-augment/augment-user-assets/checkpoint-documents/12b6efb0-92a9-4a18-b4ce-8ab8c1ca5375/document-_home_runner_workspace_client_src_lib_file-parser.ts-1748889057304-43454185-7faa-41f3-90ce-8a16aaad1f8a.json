{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/file-parser.ts"}, "originalCode": "import { extractTextFromPdf } from './pdf-parser';\nimport { extractTextFromDocx, extractTextFromTxt, extractTextFromMd } from './docx-parser';\nimport { Document } from '@/types';\nimport { getAIProviderSettings, isAIProviderConfigured } from './ai-provider';\n\n/**\n * AI-powered text extraction and formatting using Gemini 2.5 Flash\n */\nasync function enhanceTextWithAI(rawText: string, fileName: string): Promise<string> {\n  // Skip AI enhancement if provider not configured\n  if (!isAIProviderConfigured()) {\n    console.log('🔧 AI provider not configured, using basic text extraction');\n    return rawText;\n  }\n\n  // Skip AI enhancement for very short texts (less than 100 characters)\n  if (rawText.length < 100) {\n    console.log('📄 Text too short for AI enhancement, using raw text');\n    return rawText;\n  }\n\n  try {\n    console.log(`🤖 Enhancing text extraction for \"${fileName}\" using Gemini 2.5 Flash...`);\n    const aiSettings = getAIProviderSettings();\n\n    const response = await fetch('/api/extract-and-format', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        rawText,\n        fileName,\n        aiSettings,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.warn(`⚠️ AI extraction failed (${response.status}): ${errorText}`);\n      console.log('📄 Falling back to basic text extraction');\n      return rawText;\n    }\n\n    const data = await response.json();\n    if (data.success && data.formattedContent) {\n      const improvement = ((data.formattedLength / data.originalLength) * 100).toFixed(1);\n      console.log(`✅ AI extraction successful: ${data.originalLength} → ${data.formattedLength} chars (${improvement}% of original)`);\n      return data.formattedContent;\n    } else {\n      console.warn('⚠️ AI extraction returned invalid response structure');\n      console.log('📄 Falling back to basic text extraction');\n      return rawText;\n    }\n  } catch (error) {\n    console.warn('❌ AI extraction error:', error);\n    console.log('📄 Falling back to basic text extraction');\n    return rawText;\n  }\n}\n\n/**\n * Extract text from a file based on its type\n * Unified interface for all file parsers with AI enhancement\n */\nexport async function extractTextFromFile(file: File): Promise<Document> {\n  const fileType = file.name.toLowerCase();\n  let rawDocument: Document;\n\n  // First, extract raw text using appropriate parser\n  if (fileType.endsWith('.pdf')) {\n    rawDocument = await extractTextFromPdf(file);\n  } else if (fileType.endsWith('.docx')) {\n    rawDocument = await extractTextFromDocx(file);\n  } else if (fileType.endsWith('.txt')) {\n    rawDocument = await extractTextFromTxt(file);\n  } else if (fileType.endsWith('.md')) {\n    rawDocument = await extractTextFromMd(file);\n  } else {\n    throw new Error('Unsupported file format. Please upload a PDF, DOCX, TXT, or MD file.');\n  }\n\n  // Enhance text with AI if available (uses Gemini 2.5 Flash for extraction)\n  const enhancedContent = await enhanceTextWithAI(rawDocument.content, file.name);\n\n  return {\n    ...rawDocument,\n    content: enhancedContent,\n  };\n}", "modifiedCode": "import { extractTextFromPdf } from './pdf-parser';\nimport { extractTextFromDocx, extractTextFromTxt, extractTextFromMd } from './docx-parser';\nimport { Document } from '@/types';\nimport { getAIProviderSettings, isAIProviderConfigured } from './ai-provider';\nimport { supabase } from './supabase';\n\n/**\n * AI-powered text extraction and formatting using Gemini 2.5 Flash\n */\nasync function enhanceTextWithAI(rawText: string, fileName: string): Promise<string> {\n  // Skip AI enhancement if provider not configured\n  if (!isAIProviderConfigured()) {\n    console.log('🔧 AI provider not configured, using basic text extraction');\n    return rawText;\n  }\n\n  // Skip AI enhancement for very short texts (less than 100 characters)\n  if (rawText.length < 100) {\n    console.log('📄 Text too short for AI enhancement, using raw text');\n    return rawText;\n  }\n\n  try {\n    console.log(`🤖 Enhancing text extraction for \"${fileName}\" using Gemini 2.5 Flash...`);\n    const aiSettings = getAIProviderSettings();\n\n    const response = await fetch('/api/extract-and-format', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        rawText,\n        fileName,\n        aiSettings,\n      }),\n    });\n\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.warn(`⚠️ AI extraction failed (${response.status}): ${errorText}`);\n      console.log('📄 Falling back to basic text extraction');\n      return rawText;\n    }\n\n    const data = await response.json();\n    if (data.success && data.formattedContent) {\n      const improvement = ((data.formattedLength / data.originalLength) * 100).toFixed(1);\n      console.log(`✅ AI extraction successful: ${data.originalLength} → ${data.formattedLength} chars (${improvement}% of original)`);\n      return data.formattedContent;\n    } else {\n      console.warn('⚠️ AI extraction returned invalid response structure');\n      console.log('📄 Falling back to basic text extraction');\n      return rawText;\n    }\n  } catch (error) {\n    console.warn('❌ AI extraction error:', error);\n    console.log('📄 Falling back to basic text extraction');\n    return rawText;\n  }\n}\n\n/**\n * Extract text from a file based on its type\n * Unified interface for all file parsers with AI enhancement\n */\nexport async function extractTextFromFile(file: File): Promise<Document> {\n  const fileType = file.name.toLowerCase();\n  let rawDocument: Document;\n\n  // First, extract raw text using appropriate parser\n  if (fileType.endsWith('.pdf')) {\n    rawDocument = await extractTextFromPdf(file);\n  } else if (fileType.endsWith('.docx')) {\n    rawDocument = await extractTextFromDocx(file);\n  } else if (fileType.endsWith('.txt')) {\n    rawDocument = await extractTextFromTxt(file);\n  } else if (fileType.endsWith('.md')) {\n    rawDocument = await extractTextFromMd(file);\n  } else {\n    throw new Error('Unsupported file format. Please upload a PDF, DOCX, TXT, or MD file.');\n  }\n\n  // Enhance text with AI if available (uses Gemini 2.5 Flash for extraction)\n  const enhancedContent = await enhanceTextWithAI(rawDocument.content, file.name);\n\n  return {\n    ...rawDocument,\n    content: enhancedContent,\n  };\n}"}