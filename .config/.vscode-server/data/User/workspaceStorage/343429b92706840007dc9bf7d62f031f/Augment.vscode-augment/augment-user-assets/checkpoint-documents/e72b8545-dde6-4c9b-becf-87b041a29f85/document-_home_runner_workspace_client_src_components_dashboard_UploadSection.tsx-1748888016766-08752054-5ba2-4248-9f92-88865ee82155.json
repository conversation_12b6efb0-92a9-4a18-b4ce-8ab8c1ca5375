{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/dashboard/UploadSection.tsx"}, "originalCode": "import React, { useState, useCallback } from \"react\";\nimport { useMutation } from \"@tanstack/react-query\";\nimport { useLocation } from \"wouter\";\nimport { Input } from \"@/components/ui/input\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n  UploadCloud,\n  FileText,\n  AlertCircle,\n  Sparkles,\n  HelpCircleIcon,\n  ListChecks,\n  HelpCircle,\n} from \"lucide-react\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { extractTextFromFile } from \"@/lib/file-parser\";\nimport {\n  getAIProviderSettings,\n  isAIProviderConfigured,\n  isAIProviderConfiguredSync,\n} from \"@/lib/ai-provider\";\nimport { Document as DocumentType } from \"@/types\"; \nimport { Flashcard } from \"@shared/types/flashcards\";\nimport { Quiz, GenerateQuizResponse } from \"@shared/types/quiz\";\nimport { GenerateAiQuizApiPayload } from \"@/lib/api\";\nimport FlashcardViewer from \"../flashcards/FlashcardViewer\";\nimport { supabase } from \"@/lib/supabaseClient\";\n\ninterface UploadSectionProps {\n  onDocumentProcessed: (document: DocumentType) => void;\n  onFlashcardsGenerated: (flashcards: Flashcard[]) => void;\n  onQuizGenerated: (quiz: Quiz) => void; \n};\n\nconst QUESTION_TYPE_OPTIONS = [\n  { id: \"multiple_choice\", label: \"Multiple Choice\" },\n  { id: \"select_all_that_apply\", label: \"Select All That Apply\" },\n  { id: \"true_false\", label: \"True/False\" },\n  { id: \"short_answer\", label: \"Short Answer\" },\n];\n\nconst UploadSection: React.FC<UploadSectionProps> = ({\n  onDocumentProcessed,\n  onFlashcardsGenerated,\n  onQuizGenerated,\n}) => {\n  const [, setLocation] = useLocation();\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [extractedText, setExtractedText] = useState<string | null>(null);\n  const [progress, setProgress] = useState<number>(0);\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\n  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null); \n  const [document, setDocument] = useState<DocumentType | null>(null);\n  const [numberOfQuizQuestions, setNumberOfQuizQuestions] = useState<number>(5);\n  const [numberOfFlashcards, setNumberOfFlashcards] = useState<number>(10);\n  const [quizTitle, setQuizTitle] = useState<string>(\"\");\n  const [quizQuestionTypes, setQuizQuestionTypes] = useState<string[]>([\"multiple_choice\"]);\n  const [flashcardCustomPrompt, setFlashcardCustomPrompt] = useState<string>(\"\");\n  const [quizCustomPrompt, setQuizCustomPrompt] = useState<string>(\"\");\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-slate-100 shadow-md\";\n  const textPrimaryClass = \"text-slate-50\";\n  const textSecondaryClass = \"text-slate-300\";\n  const textMutedClass = \"text-slate-400\";\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const accentBgClass = `bg-${accentColor}`;\n  const hoverAccentBgClass = `hover:bg-purple-500`;\n  const destructiveColor = \"red-500\"; \n  const warningColor = \"yellow-500\"; \n\n  const {\n    mutate: processFile,\n    isPending: isProcessingFile,\n    error: fileError,\n  } = useMutation<DocumentType, Error, File>({\n    mutationFn: async (file: File) => {      \n      setQuizTitle(`Quiz for ${file.name}`);\n      setProgress(30);\n      try {\n        const doc = await extractTextFromFile(file);\n        setProgress(70);\n        setExtractedText(doc.content);\n        onDocumentProcessed(doc);\n        setDocument(doc);\n        setProgress(100);\n        return doc;\n      } catch (err: any) {\n        console.error(\"Error in file processing mutation:\", err);\n        setProgress(0);\n        throw new Error(err.message || \"Failed to process file.\");\n      }\n    },\n    onSuccess: () => {\n      console.log(\"File processed successfully, ready for generation.\");\n      setFlashcards([]); \n      setGeneratedQuiz(null); \n    },\n    onError: (error) => {\n      setSelectedFile(null);\n      setExtractedText(null);\n      setFlashcards([]);\n      setGeneratedQuiz(null);\n      setQuizTitle(\"\");\n      console.error(\"File processing failed:\", error);\n    },\n  });\n\n  const {\n    mutate: generateFlashcards,\n    isPending: isGeneratingFlashcards,\n    error: flashcardError,\n  } = useMutation<Flashcard[], Error, void>({\n    mutationFn: async () => {\n      const isConfigured = await isAIProviderConfigured();\n      if (!isConfigured) {\n        throw new Error(\"AI Provider not configured. Please configure your AI settings first.\");\n      }\n      if (!extractedText)\n        throw new Error(\"No text to generate flashcards from.\");\n      if (!document?.id)\n        throw new Error(\"No processed document to associate with flashcards.\");\n\n      const aiSettings = getAIProviderSettings();\n      const requestBody = {\n        textContent: extractedText,\n        documentId: document.id,\n        deckTitle: selectedFile?.name || `Flashcards for ${document.name}`,\n        count: numberOfFlashcards,\n        customPrompt: flashcardCustomPrompt || undefined,\n        aiSettings,\n      };\n\n      try {\n        const response = await fetch(\"/api/flashcards/generate\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify(requestBody),\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorData;\n          try {\n            errorData = JSON.parse(errorText);\n          } catch (e) {\n            throw new Error(\n              `Failed to generate flashcards: ${response.status} ${response.statusText}. ${errorText}`\n            );\n          }\n          throw new Error(\n            errorData.error ||\n              errorData.message ||\n              `Failed to generate flashcards: ${response.status}`\n          );\n        }\n\n        const data = await response.json();\n        if (data && data.deck && data.deck.flashcards) {\n          return data.deck.flashcards;\n        } else {\n          throw new Error(\n            \"Unexpected response structure from flashcard generation API.\"\n          );\n        }\n      } catch (err: any) {\n        console.error(\"Error calling backend to generate flashcards:\", err);\n        const errorDetails =\n          err.response?.data?.message ||\n          err.response?.data?.details ||\n          err.message ||\n          \"Unknown error occurred\";\n        throw new Error(`Failed to generate flashcards: ${errorDetails}`);\n      }\n    },\n    onSuccess: (generatedFlashcards) => {\n      console.log(\"Flashcards generated:\", generatedFlashcards);\n      setFlashcards(generatedFlashcards);\n      onFlashcardsGenerated(generatedFlashcards);\n    },\n    onError: (error) => {\n      console.error(\"Flashcard generation failed:\", error);\n    },\n  });\n\n  const {\n    mutate: generateQuizMutation,\n    isPending: isGeneratingQuiz,\n    error: quizError,\n  } = useMutation<GenerateQuizResponse, Error, void>({\n    mutationFn: async () => {\n      const isConfigured = await isAIProviderConfigured();\n      if (!isConfigured) {\n        throw new Error(\"AI Provider not configured. Please configure your AI settings first.\");\n      }\n      if (!extractedText) throw new Error(\"No text to generate quiz from.\");\n      if (!document?.id)\n        throw new Error(\"No processed document to associate with quiz.\");\n      if (!quizTitle.trim()) throw new Error(\"Quiz title cannot be empty.\");\n      if (numberOfQuizQuestions <= 0) {\n        throw new Error(\"Number of questions must be greater than 0.\");\n      }\n      if (quizQuestionTypes.length === 0) {\n        throw new Error(\"At least one question type must be selected.\");\n      }\n\n      const aiSettings = getAIProviderSettings();\n      const requestBody: GenerateAiQuizApiPayload = {\n        textContent: extractedText,\n        documentId: document.id,\n        quizName: quizTitle.trim(),\n        generationOptions: {\n          numberOfQuestions: numberOfQuizQuestions,\n          questionTypes: quizQuestionTypes,\n        },\n        customPrompt: quizCustomPrompt || undefined,\n        // AI config is now handled by the backend using stored credentials\n      };\n\n      try {\n        const response = await fetch(\"/api/quizzes/generate\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify(requestBody),\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorData;\n          try {\n            errorData = JSON.parse(errorText);\n          } catch (e) {\n            throw new Error(\n              `Failed to generate quiz: ${response.status} ${response.statusText}. ${errorText}`\n            );\n          }\n          throw new Error(\n            errorData.error ||\n              errorData.message ||\n              `Failed to generate quiz: ${response.status}`\n          );\n        }\n\n        const data = await response.json();\n        if (data && data.success && data.quiz) {\n          return data;\n        } else {\n          throw new Error(\n            data.message ||\n              \"Failed to generate quiz or unexpected response structure.\"\n          );\n        }\n      } catch (err: any) {\n        console.error(\"Error calling backend to generate quiz:\", err);\n        const errorDetails = err.message || \"Unknown error occurred\";\n        throw new Error(`Failed to generate quiz: ${errorDetails}`);\n      }\n    },\n    onSuccess: (data) => {\n      if (data.quiz && data.quizId) {\n        console.log(\"Quiz generated:\", data.quiz);\n        setGeneratedQuiz(data.quiz);\n        onQuizGenerated(data.quiz);\n        setLocation(`/quiz/${data.quizId}`);\n      } else {\n        console.error(\n          \"Quiz generation reported success but quiz data or ID is missing.\"\n        );\n      }\n    },\n    onError: (error) => {\n      console.error(\"Quiz generation failed:\", error);\n    },\n  });\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      setExtractedText(null);\n      setFlashcards([]);\n      setGeneratedQuiz(null);\n      setProgress(10);\n      processFile(file);\n    }\n  };\n\n  const handleGenerateFlashcardsClick = useCallback(() => {\n    generateFlashcards();\n  }, [generateFlashcards]);\n\n  const handleGenerateQuizClick = useCallback(() => {\n    generateQuizMutation();\n  }, [generateQuizMutation]);\n\n  const handleQuizQuestionTypeChange = (typeId: string) => {\n    setQuizQuestionTypes((prevTypes) =>\n      prevTypes.includes(typeId)\n        ? prevTypes.filter((qt) => qt !== typeId)\n        : [...prevTypes, typeId]\n    );\n  };\n\n  const displayError = fileError || flashcardError || quizError;\n  const isGeneratingAnything =\n    isProcessingFile || isGeneratingFlashcards || isGeneratingQuiz;\n\n  return (\n    <Card className={`${commonCardClasses} w-full`}>\n      <CardHeader>\n        <CardTitle\n          className={`flex items-center gap-2 text-2xl ${accentTextClass}`}\n        >\n          <UploadCloud className=\"h-6 w-6\" /> Upload Document\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n            <div\n              className={`flex flex-col items-center justify-center p-6 border-2 border-dashed border-slate-600 rounded-lg hover:bg-slate-700/70 transition-colors`}\n            >\n              <UploadCloud className={`h-10 w-10 mb-2 ${textMutedClass}`} />\n              <span className={`text-sm font-semibold ${textSecondaryClass} truncate max-w-full`}>\n                {selectedFile ? selectedFile.name : \"Click or drag file to upload\"}\n              </span>\n              <p className={`text-xs ${textMutedClass} mt-1`}>\n                PDF, TXT, MD, DOCX\n              </p>\n            </div>\n          </label>\n          <Input\n            id=\"file-upload\"\n            type=\"file\"\n            className=\"hidden\"\n            accept=\".pdf,.txt,.md,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n            onChange={handleFileChange}\n            disabled={isGeneratingAnything}\n          />\n        </div>\n\n        {(isProcessingFile ||\n          (progress > 0 && progress < 100 && !extractedText)) && (\n          <div className=\"space-y-2\">\n            <Progress\n              value={progress}\n              className={`w-full h-2 [&>div]:${accentBgClass}`}\n            />\n            <p className={`text-sm text-center ${textSecondaryClass}`}>\n              Processing file...\n            </p>\n          </div>\n        )}\n\n        {displayError && (\n          <Alert\n            variant=\"destructive\"\n            className={`bg-red-900/30 border-${destructiveColor} text-${destructiveColor}`}\n          >\n            <AlertCircle className={`h-4 w-4 text-${destructiveColor}`} />\n            <AlertTitle className={`text-${destructiveColor}`}>\n              Error\n            </AlertTitle>\n            <AlertDescription className={`text-red-300`}>\n              {displayError.message}\n            </AlertDescription>\n          </Alert>\n        )}\n\n        {extractedText && !isGeneratingAnything && (\n          <div className=\"space-y-6\">\n            {/* Section Header */}\n            <div className=\"text-center space-y-2 py-4 border-t border-slate-700\">\n              <h3 className={`text-lg font-semibold ${textPrimaryClass}`}>\n                What would you like to generate? (Optional)\n              </h3>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Choose one or both options below. You can always generate content later from your documents.\n              </p>\n            </div>\n\n            {/* Flashcard Generation Section */}\n            <div className=\"bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Sparkles className=\"h-5 w-5 text-purple-400\" />\n                <h4 className={`text-md font-medium ${textPrimaryClass}`}>\n                  Generate Flashcards\n                </h4>\n              </div>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Create study flashcards from your document content for spaced repetition learning.\n              </p>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center space-x-2\">\n                  <Label htmlFor=\"num-flashcards\" className={textSecondaryClass}>\n                    Number of Flashcards\n                </Label>\n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <HelpCircle className=\"h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help\" />\n                    </TooltipTrigger>\n                    <TooltipContent className=\"bg-slate-800 border-slate-600 text-slate-200\">\n                      <p>Recommended: 5-15 flashcards per document</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              </div>\n              <Input\n                type=\"number\"\n                id=\"num-flashcards\"\n                value={numberOfFlashcards}\n                onChange={(e) =>\n                  setNumberOfFlashcards(\n                    Math.max(1, parseInt(e.target.value, 10) || 1)\n                  )\n                }\n                min=\"1\"\n                max=\"50\"\n                className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              />\n            </div>\n\n            {/* Custom Prompt for Flashcards */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"flashcard-custom-prompt\" className={textSecondaryClass}>\n                Custom Prompt (Optional)\n              </Label>\n              <Textarea\n                id=\"flashcard-custom-prompt\"\n                value={flashcardCustomPrompt}\n                onChange={(e) => setFlashcardCustomPrompt(e.target.value)}\n                placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n                rows={3}\n                className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              />\n              <p className=\"text-xs text-purple-400\">\n                Add specific instructions for the AI on what kind of flashcards you want.\n              </p>\n            </div>\n\n            <Button\n              onClick={handleGenerateFlashcardsClick}\n              disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center\"\n            >\n              {isGeneratingFlashcards ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle><path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>\n                  Generating Flashcards...\n                </>\n              ) : (\n                <>\n                  <Sparkles className=\"mr-2 h-4 w-4\" /> Generate Flashcards\n                </>\n              )}\n            </Button>\n            </div>\n\n            {/* Quiz Generation Section */}\n            <div className=\"bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <ListChecks className=\"h-5 w-5 text-purple-400\" />\n                <h4 className={`text-md font-medium ${textPrimaryClass}`}>\n                  Generate Quiz\n                </h4>\n              </div>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Create an interactive quiz from your document content to test your knowledge.\n              </p>\n\n              <div className=\"space-y-1.5\">\n                <Label htmlFor=\"quiz-title\" className={textSecondaryClass}>\n                  Quiz Title\n                </Label>\n                <Input\n                  type=\"text\"\n                  id=\"quiz-title\"\n                  value={quizTitle}\n                  onChange={(e) => setQuizTitle(e.target.value)}\n                  placeholder=\"Enter title for your quiz\"\n                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center space-x-2\">\n                  <Label htmlFor=\"num-questions\" className={textSecondaryClass}>\n                    Number of Questions\n                  </Label>\n                  <TooltipProvider>\n                    <Tooltip>\n                      <TooltipTrigger asChild>\n                        <HelpCircle className=\"h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help\" />\n                      </TooltipTrigger>\n                      <TooltipContent className=\"bg-slate-800 border-slate-600 text-slate-200\">\n                        <p>Optimal quiz length: 5-20 questions</p>\n                      </TooltipContent>\n                    </Tooltip>\n                  </TooltipProvider>\n                </div>\n                <Input\n                  type=\"number\"\n                  id=\"num-questions\"\n                  value={numberOfQuizQuestions}\n                  onChange={(e) =>\n                    setNumberOfQuizQuestions(\n                      Math.max(1, parseInt(e.target.value, 10) || 1)\n                    )\n                  }\n                  min=\"1\"\n                  max=\"50\"\n                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n              </div>\n\n              <div>\n                <Label className={textSecondaryClass}>Question Types</Label>\n                <div className=\"mt-2 space-y-2\">\n                  {QUESTION_TYPE_OPTIONS.map((type) => (\n                    <div key={type.id} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`upload-type-${type.id}`}\n                        checked={quizQuestionTypes.includes(type.id)}\n                        onCheckedChange={() => handleQuizQuestionTypeChange(type.id)}\n                        disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                        className=\"border-slate-500 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500\"\n                      />\n                      <Label\n                        htmlFor={`upload-type-${type.id}`}\n                        className={`font-normal ${textSecondaryClass} cursor-pointer`}\n                      >\n                        {type.label}\n                      </Label>\n                    </div>\n                  ))}\n                </div>\n                {quizQuestionTypes.length === 0 && (\n                  <p className=\"text-xs text-red-400 mt-1\">Please select at least one question type.</p>\n                )}\n              </div>\n\n              {/* Custom Prompt for Quiz */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"quiz-custom-prompt\" className={textSecondaryClass}>\n                  Custom Prompt (Optional)\n                </Label>\n                <Textarea\n                  id=\"quiz-custom-prompt\"\n                  value={quizCustomPrompt}\n                  onChange={(e) => setQuizCustomPrompt(e.target.value)}\n                  placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n                  rows={3}\n                  className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n                <p className=\"text-xs text-purple-400\">\n                  Add specific instructions for the AI on what kind of questions you want.\n                </p>\n              </div>\n\n              <Button\n                onClick={handleGenerateQuizClick}\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center\"\n              >\n              {isGeneratingQuiz ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle><path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>\n                  Generating Quiz...\n                </>\n              ) : (\n                <>\n                  <ListChecks className=\"mr-2 h-4 w-4\" />\n                  Generate Quiz\n                </>\n              )}\n            </Button>\n            </div>\n          </div>\n        )}\n        {!isAIProviderConfiguredSync() &&\n          extractedText &&\n          !isGeneratingAnything && (\n            <Alert\n              variant=\"default\"\n              className={`bg-yellow-900/30 border-${warningColor} text-${warningColor}`}\n            >\n              <AlertCircle className={`h-4 w-4 text-${warningColor}`} />\n              <AlertTitle className={`text-${warningColor}`}>\n                Configuration Needed\n              </AlertTitle>\n              <AlertDescription className={`text-yellow-300`}>\n                Please configure your AI Provider in settings before generating\n                content.\n              </AlertDescription>\n            </Alert>\n          )}\n\n        {Array.isArray(flashcards) &&\n          flashcards.length > 0 &&\n          !isGeneratingFlashcards && (\n            <FlashcardViewer flashcards={flashcards} />\n          )}\n\n        {generatedQuiz && !isGeneratingQuiz && (\n          <Alert\n            variant=\"default\"\n            className={`bg-green-900/30 border-green-500 text-green-300 mt-4`}\n          >\n            <HelpCircleIcon className={`h-4 w-4 text-green-400`} />\n            <AlertTitle className={`text-green-400`}>\n              Quiz Generated!\n            </AlertTitle>\n            <AlertDescription className={`text-green-200`}>\n              {generatedQuiz.name} is ready.\n            </AlertDescription>\n          </Alert>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default UploadSection;\n", "modifiedCode": "import React, { useState, useCallback } from \"react\";\nimport { useMutation } from \"@tanstack/react-query\";\nimport { useLocation } from \"wouter\";\nimport { Input } from \"@/components/ui/input\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport {\n  UploadCloud,\n  FileText,\n  AlertCircle,\n  Sparkles,\n  HelpCircleIcon,\n  ListChecks,\n  HelpCircle,\n} from \"lucide-react\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport {\n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\";\nimport { extractTextFromFile } from \"@/lib/file-parser\";\nimport {\n  getAIProviderSettings,\n  isAIProviderConfigured,\n  isAIProviderConfiguredSync,\n} from \"@/lib/ai-provider\";\nimport { Document as DocumentType } from \"@/types\"; \nimport { Flashcard } from \"@shared/types/flashcards\";\nimport { Quiz, GenerateQuizResponse } from \"@shared/types/quiz\";\nimport { GenerateAiQuizApiPayload } from \"@/lib/api\";\nimport FlashcardViewer from \"../flashcards/FlashcardViewer\";\nimport { supabase } from \"@/lib/supabaseClient\";\n\ninterface UploadSectionProps {\n  onDocumentProcessed: (document: DocumentType) => void;\n  onFlashcardsGenerated: (flashcards: Flashcard[]) => void;\n  onQuizGenerated: (quiz: Quiz) => void; \n};\n\nconst QUESTION_TYPE_OPTIONS = [\n  { id: \"multiple_choice\", label: \"Multiple Choice\" },\n  { id: \"select_all_that_apply\", label: \"Select All That Apply\" },\n  { id: \"true_false\", label: \"True/False\" },\n  { id: \"short_answer\", label: \"Short Answer\" },\n];\n\nconst UploadSection: React.FC<UploadSectionProps> = ({\n  onDocumentProcessed,\n  onFlashcardsGenerated,\n  onQuizGenerated,\n}) => {\n  const [, setLocation] = useLocation();\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [extractedText, setExtractedText] = useState<string | null>(null);\n  const [progress, setProgress] = useState<number>(0);\n  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);\n  const [generatedQuiz, setGeneratedQuiz] = useState<Quiz | null>(null); \n  const [document, setDocument] = useState<DocumentType | null>(null);\n  const [numberOfQuizQuestions, setNumberOfQuizQuestions] = useState<number>(5);\n  const [numberOfFlashcards, setNumberOfFlashcards] = useState<number>(10);\n  const [quizTitle, setQuizTitle] = useState<string>(\"\");\n  const [quizQuestionTypes, setQuizQuestionTypes] = useState<string[]>([\"multiple_choice\"]);\n  const [flashcardCustomPrompt, setFlashcardCustomPrompt] = useState<string>(\"\");\n  const [quizCustomPrompt, setQuizCustomPrompt] = useState<string>(\"\");\n  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);\n\n  // Check AI configuration on mount\n  useEffect(() => {\n    const checkAIConfig = async () => {\n      const configured = await isAIProviderConfigured();\n      setIsAIConfigured(configured);\n    };\n    checkAIConfig();\n  }, []);\n\n  const commonCardClasses =\n    \"bg-slate-800 border-slate-700 text-slate-100 shadow-md\";\n  const textPrimaryClass = \"text-slate-50\";\n  const textSecondaryClass = \"text-slate-300\";\n  const textMutedClass = \"text-slate-400\";\n  const accentColor = \"purple-400\";\n  const accentTextClass = `text-${accentColor}`;\n  const accentBorderClass = `border-${accentColor}`;\n  const accentBgClass = `bg-${accentColor}`;\n  const hoverAccentBgClass = `hover:bg-purple-500`;\n  const destructiveColor = \"red-500\"; \n  const warningColor = \"yellow-500\"; \n\n  const {\n    mutate: processFile,\n    isPending: isProcessingFile,\n    error: fileError,\n  } = useMutation<DocumentType, Error, File>({\n    mutationFn: async (file: File) => {      \n      setQuizTitle(`Quiz for ${file.name}`);\n      setProgress(30);\n      try {\n        const doc = await extractTextFromFile(file);\n        setProgress(70);\n        setExtractedText(doc.content);\n        onDocumentProcessed(doc);\n        setDocument(doc);\n        setProgress(100);\n        return doc;\n      } catch (err: any) {\n        console.error(\"Error in file processing mutation:\", err);\n        setProgress(0);\n        throw new Error(err.message || \"Failed to process file.\");\n      }\n    },\n    onSuccess: () => {\n      console.log(\"File processed successfully, ready for generation.\");\n      setFlashcards([]); \n      setGeneratedQuiz(null); \n    },\n    onError: (error) => {\n      setSelectedFile(null);\n      setExtractedText(null);\n      setFlashcards([]);\n      setGeneratedQuiz(null);\n      setQuizTitle(\"\");\n      console.error(\"File processing failed:\", error);\n    },\n  });\n\n  const {\n    mutate: generateFlashcards,\n    isPending: isGeneratingFlashcards,\n    error: flashcardError,\n  } = useMutation<Flashcard[], Error, void>({\n    mutationFn: async () => {\n      const isConfigured = await isAIProviderConfigured();\n      if (!isConfigured) {\n        throw new Error(\"AI Provider not configured. Please configure your AI settings first.\");\n      }\n      if (!extractedText)\n        throw new Error(\"No text to generate flashcards from.\");\n      if (!document?.id)\n        throw new Error(\"No processed document to associate with flashcards.\");\n\n      const aiSettings = getAIProviderSettings();\n      const requestBody = {\n        textContent: extractedText,\n        documentId: document.id,\n        deckTitle: selectedFile?.name || `Flashcards for ${document.name}`,\n        count: numberOfFlashcards,\n        customPrompt: flashcardCustomPrompt || undefined,\n        aiSettings,\n      };\n\n      try {\n        const response = await fetch(\"/api/flashcards/generate\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify(requestBody),\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorData;\n          try {\n            errorData = JSON.parse(errorText);\n          } catch (e) {\n            throw new Error(\n              `Failed to generate flashcards: ${response.status} ${response.statusText}. ${errorText}`\n            );\n          }\n          throw new Error(\n            errorData.error ||\n              errorData.message ||\n              `Failed to generate flashcards: ${response.status}`\n          );\n        }\n\n        const data = await response.json();\n        if (data && data.deck && data.deck.flashcards) {\n          return data.deck.flashcards;\n        } else {\n          throw new Error(\n            \"Unexpected response structure from flashcard generation API.\"\n          );\n        }\n      } catch (err: any) {\n        console.error(\"Error calling backend to generate flashcards:\", err);\n        const errorDetails =\n          err.response?.data?.message ||\n          err.response?.data?.details ||\n          err.message ||\n          \"Unknown error occurred\";\n        throw new Error(`Failed to generate flashcards: ${errorDetails}`);\n      }\n    },\n    onSuccess: (generatedFlashcards) => {\n      console.log(\"Flashcards generated:\", generatedFlashcards);\n      setFlashcards(generatedFlashcards);\n      onFlashcardsGenerated(generatedFlashcards);\n    },\n    onError: (error) => {\n      console.error(\"Flashcard generation failed:\", error);\n    },\n  });\n\n  const {\n    mutate: generateQuizMutation,\n    isPending: isGeneratingQuiz,\n    error: quizError,\n  } = useMutation<GenerateQuizResponse, Error, void>({\n    mutationFn: async () => {\n      const isConfigured = await isAIProviderConfigured();\n      if (!isConfigured) {\n        throw new Error(\"AI Provider not configured. Please configure your AI settings first.\");\n      }\n      if (!extractedText) throw new Error(\"No text to generate quiz from.\");\n      if (!document?.id)\n        throw new Error(\"No processed document to associate with quiz.\");\n      if (!quizTitle.trim()) throw new Error(\"Quiz title cannot be empty.\");\n      if (numberOfQuizQuestions <= 0) {\n        throw new Error(\"Number of questions must be greater than 0.\");\n      }\n      if (quizQuestionTypes.length === 0) {\n        throw new Error(\"At least one question type must be selected.\");\n      }\n\n      const aiSettings = getAIProviderSettings();\n      const requestBody: GenerateAiQuizApiPayload = {\n        textContent: extractedText,\n        documentId: document.id,\n        quizName: quizTitle.trim(),\n        generationOptions: {\n          numberOfQuestions: numberOfQuizQuestions,\n          questionTypes: quizQuestionTypes,\n        },\n        customPrompt: quizCustomPrompt || undefined,\n        // AI config is now handled by the backend using stored credentials\n      };\n\n      try {\n        const response = await fetch(\"/api/quizzes/generate\", {\n          method: \"POST\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n          },\n          body: JSON.stringify(requestBody),\n        });\n\n        if (!response.ok) {\n          const errorText = await response.text();\n          let errorData;\n          try {\n            errorData = JSON.parse(errorText);\n          } catch (e) {\n            throw new Error(\n              `Failed to generate quiz: ${response.status} ${response.statusText}. ${errorText}`\n            );\n          }\n          throw new Error(\n            errorData.error ||\n              errorData.message ||\n              `Failed to generate quiz: ${response.status}`\n          );\n        }\n\n        const data = await response.json();\n        if (data && data.success && data.quiz) {\n          return data;\n        } else {\n          throw new Error(\n            data.message ||\n              \"Failed to generate quiz or unexpected response structure.\"\n          );\n        }\n      } catch (err: any) {\n        console.error(\"Error calling backend to generate quiz:\", err);\n        const errorDetails = err.message || \"Unknown error occurred\";\n        throw new Error(`Failed to generate quiz: ${errorDetails}`);\n      }\n    },\n    onSuccess: (data) => {\n      if (data.quiz && data.quizId) {\n        console.log(\"Quiz generated:\", data.quiz);\n        setGeneratedQuiz(data.quiz);\n        onQuizGenerated(data.quiz);\n        setLocation(`/quiz/${data.quizId}`);\n      } else {\n        console.error(\n          \"Quiz generation reported success but quiz data or ID is missing.\"\n        );\n      }\n    },\n    onError: (error) => {\n      console.error(\"Quiz generation failed:\", error);\n    },\n  });\n\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      setSelectedFile(file);\n      setExtractedText(null);\n      setFlashcards([]);\n      setGeneratedQuiz(null);\n      setProgress(10);\n      processFile(file);\n    }\n  };\n\n  const handleGenerateFlashcardsClick = useCallback(() => {\n    generateFlashcards();\n  }, [generateFlashcards]);\n\n  const handleGenerateQuizClick = useCallback(() => {\n    generateQuizMutation();\n  }, [generateQuizMutation]);\n\n  const handleQuizQuestionTypeChange = (typeId: string) => {\n    setQuizQuestionTypes((prevTypes) =>\n      prevTypes.includes(typeId)\n        ? prevTypes.filter((qt) => qt !== typeId)\n        : [...prevTypes, typeId]\n    );\n  };\n\n  const displayError = fileError || flashcardError || quizError;\n  const isGeneratingAnything =\n    isProcessingFile || isGeneratingFlashcards || isGeneratingQuiz;\n\n  return (\n    <Card className={`${commonCardClasses} w-full`}>\n      <CardHeader>\n        <CardTitle\n          className={`flex items-center gap-2 text-2xl ${accentTextClass}`}\n        >\n          <UploadCloud className=\"h-6 w-6\" /> Upload Document\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"file-upload\" className=\"cursor-pointer\">\n            <div\n              className={`flex flex-col items-center justify-center p-6 border-2 border-dashed border-slate-600 rounded-lg hover:bg-slate-700/70 transition-colors`}\n            >\n              <UploadCloud className={`h-10 w-10 mb-2 ${textMutedClass}`} />\n              <span className={`text-sm font-semibold ${textSecondaryClass} truncate max-w-full`}>\n                {selectedFile ? selectedFile.name : \"Click or drag file to upload\"}\n              </span>\n              <p className={`text-xs ${textMutedClass} mt-1`}>\n                PDF, TXT, MD, DOCX\n              </p>\n            </div>\n          </label>\n          <Input\n            id=\"file-upload\"\n            type=\"file\"\n            className=\"hidden\"\n            accept=\".pdf,.txt,.md,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n            onChange={handleFileChange}\n            disabled={isGeneratingAnything}\n          />\n        </div>\n\n        {(isProcessingFile ||\n          (progress > 0 && progress < 100 && !extractedText)) && (\n          <div className=\"space-y-2\">\n            <Progress\n              value={progress}\n              className={`w-full h-2 [&>div]:${accentBgClass}`}\n            />\n            <p className={`text-sm text-center ${textSecondaryClass}`}>\n              Processing file...\n            </p>\n          </div>\n        )}\n\n        {displayError && (\n          <Alert\n            variant=\"destructive\"\n            className={`bg-red-900/30 border-${destructiveColor} text-${destructiveColor}`}\n          >\n            <AlertCircle className={`h-4 w-4 text-${destructiveColor}`} />\n            <AlertTitle className={`text-${destructiveColor}`}>\n              Error\n            </AlertTitle>\n            <AlertDescription className={`text-red-300`}>\n              {displayError.message}\n            </AlertDescription>\n          </Alert>\n        )}\n\n        {extractedText && !isGeneratingAnything && (\n          <div className=\"space-y-6\">\n            {/* Section Header */}\n            <div className=\"text-center space-y-2 py-4 border-t border-slate-700\">\n              <h3 className={`text-lg font-semibold ${textPrimaryClass}`}>\n                What would you like to generate? (Optional)\n              </h3>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Choose one or both options below. You can always generate content later from your documents.\n              </p>\n            </div>\n\n            {/* Flashcard Generation Section */}\n            <div className=\"bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <Sparkles className=\"h-5 w-5 text-purple-400\" />\n                <h4 className={`text-md font-medium ${textPrimaryClass}`}>\n                  Generate Flashcards\n                </h4>\n              </div>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Create study flashcards from your document content for spaced repetition learning.\n              </p>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center space-x-2\">\n                  <Label htmlFor=\"num-flashcards\" className={textSecondaryClass}>\n                    Number of Flashcards\n                </Label>\n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <HelpCircle className=\"h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help\" />\n                    </TooltipTrigger>\n                    <TooltipContent className=\"bg-slate-800 border-slate-600 text-slate-200\">\n                      <p>Recommended: 5-15 flashcards per document</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n              </div>\n              <Input\n                type=\"number\"\n                id=\"num-flashcards\"\n                value={numberOfFlashcards}\n                onChange={(e) =>\n                  setNumberOfFlashcards(\n                    Math.max(1, parseInt(e.target.value, 10) || 1)\n                  )\n                }\n                min=\"1\"\n                max=\"50\"\n                className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              />\n            </div>\n\n            {/* Custom Prompt for Flashcards */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"flashcard-custom-prompt\" className={textSecondaryClass}>\n                Custom Prompt (Optional)\n              </Label>\n              <Textarea\n                id=\"flashcard-custom-prompt\"\n                value={flashcardCustomPrompt}\n                onChange={(e) => setFlashcardCustomPrompt(e.target.value)}\n                placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n                rows={3}\n                className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              />\n              <p className=\"text-xs text-purple-400\">\n                Add specific instructions for the AI on what kind of flashcards you want.\n              </p>\n            </div>\n\n            <Button\n              onClick={handleGenerateFlashcardsClick}\n              disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n              className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center\"\n            >\n              {isGeneratingFlashcards ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle><path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>\n                  Generating Flashcards...\n                </>\n              ) : (\n                <>\n                  <Sparkles className=\"mr-2 h-4 w-4\" /> Generate Flashcards\n                </>\n              )}\n            </Button>\n            </div>\n\n            {/* Quiz Generation Section */}\n            <div className=\"bg-slate-700/30 border border-slate-600 rounded-lg p-4 space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <ListChecks className=\"h-5 w-5 text-purple-400\" />\n                <h4 className={`text-md font-medium ${textPrimaryClass}`}>\n                  Generate Quiz\n                </h4>\n              </div>\n              <p className={`text-sm ${textSecondaryClass}`}>\n                Create an interactive quiz from your document content to test your knowledge.\n              </p>\n\n              <div className=\"space-y-1.5\">\n                <Label htmlFor=\"quiz-title\" className={textSecondaryClass}>\n                  Quiz Title\n                </Label>\n                <Input\n                  type=\"text\"\n                  id=\"quiz-title\"\n                  value={quizTitle}\n                  onChange={(e) => setQuizTitle(e.target.value)}\n                  placeholder=\"Enter title for your quiz\"\n                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n              </div>\n\n              <div className=\"space-y-2\">\n                <div className=\"flex items-center space-x-2\">\n                  <Label htmlFor=\"num-questions\" className={textSecondaryClass}>\n                    Number of Questions\n                  </Label>\n                  <TooltipProvider>\n                    <Tooltip>\n                      <TooltipTrigger asChild>\n                        <HelpCircle className=\"h-4 w-4 text-slate-500 hover:text-slate-300 cursor-help\" />\n                      </TooltipTrigger>\n                      <TooltipContent className=\"bg-slate-800 border-slate-600 text-slate-200\">\n                        <p>Optimal quiz length: 5-20 questions</p>\n                      </TooltipContent>\n                    </Tooltip>\n                  </TooltipProvider>\n                </div>\n                <Input\n                  type=\"number\"\n                  id=\"num-questions\"\n                  value={numberOfQuizQuestions}\n                  onChange={(e) =>\n                    setNumberOfQuizQuestions(\n                      Math.max(1, parseInt(e.target.value, 10) || 1)\n                    )\n                  }\n                  min=\"1\"\n                  max=\"50\"\n                  className={`!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500`}\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n              </div>\n\n              <div>\n                <Label className={textSecondaryClass}>Question Types</Label>\n                <div className=\"mt-2 space-y-2\">\n                  {QUESTION_TYPE_OPTIONS.map((type) => (\n                    <div key={type.id} className=\"flex items-center space-x-2\">\n                      <Checkbox\n                        id={`upload-type-${type.id}`}\n                        checked={quizQuestionTypes.includes(type.id)}\n                        onCheckedChange={() => handleQuizQuestionTypeChange(type.id)}\n                        disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                        className=\"border-slate-500 data-[state=checked]:bg-purple-500 data-[state=checked]:border-purple-500\"\n                      />\n                      <Label\n                        htmlFor={`upload-type-${type.id}`}\n                        className={`font-normal ${textSecondaryClass} cursor-pointer`}\n                      >\n                        {type.label}\n                      </Label>\n                    </div>\n                  ))}\n                </div>\n                {quizQuestionTypes.length === 0 && (\n                  <p className=\"text-xs text-red-400 mt-1\">Please select at least one question type.</p>\n                )}\n              </div>\n\n              {/* Custom Prompt for Quiz */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"quiz-custom-prompt\" className={textSecondaryClass}>\n                  Custom Prompt (Optional)\n                </Label>\n                <Textarea\n                  id=\"quiz-custom-prompt\"\n                  value={quizCustomPrompt}\n                  onChange={(e) => setQuizCustomPrompt(e.target.value)}\n                  placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n                  rows={3}\n                  className=\"!bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500\"\n                  disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                />\n                <p className=\"text-xs text-purple-400\">\n                  Add specific instructions for the AI on what kind of questions you want.\n                </p>\n              </div>\n\n              <Button\n                onClick={handleGenerateQuizClick}\n                disabled={isGeneratingAnything || !isAIProviderConfiguredSync()}\n                className=\"w-full bg-purple-600 hover:bg-purple-700 text-white py-2.5 text-sm font-medium flex items-center justify-center\"\n              >\n              {isGeneratingQuiz ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\"><circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle><path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path></svg>\n                  Generating Quiz...\n                </>\n              ) : (\n                <>\n                  <ListChecks className=\"mr-2 h-4 w-4\" />\n                  Generate Quiz\n                </>\n              )}\n            </Button>\n            </div>\n          </div>\n        )}\n        {!isAIProviderConfiguredSync() &&\n          extractedText &&\n          !isGeneratingAnything && (\n            <Alert\n              variant=\"default\"\n              className={`bg-yellow-900/30 border-${warningColor} text-${warningColor}`}\n            >\n              <AlertCircle className={`h-4 w-4 text-${warningColor}`} />\n              <AlertTitle className={`text-${warningColor}`}>\n                Configuration Needed\n              </AlertTitle>\n              <AlertDescription className={`text-yellow-300`}>\n                Please configure your AI Provider in settings before generating\n                content.\n              </AlertDescription>\n            </Alert>\n          )}\n\n        {Array.isArray(flashcards) &&\n          flashcards.length > 0 &&\n          !isGeneratingFlashcards && (\n            <FlashcardViewer flashcards={flashcards} />\n          )}\n\n        {generatedQuiz && !isGeneratingQuiz && (\n          <Alert\n            variant=\"default\"\n            className={`bg-green-900/30 border-green-500 text-green-300 mt-4`}\n          >\n            <HelpCircleIcon className={`h-4 w-4 text-green-400`} />\n            <AlertTitle className={`text-green-400`}>\n              Quiz Generated!\n            </AlertTitle>\n            <AlertDescription className={`text-green-200`}>\n              {generatedQuiz.name} is ready.\n            </AlertDescription>\n          </Alert>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default UploadSection;\n"}