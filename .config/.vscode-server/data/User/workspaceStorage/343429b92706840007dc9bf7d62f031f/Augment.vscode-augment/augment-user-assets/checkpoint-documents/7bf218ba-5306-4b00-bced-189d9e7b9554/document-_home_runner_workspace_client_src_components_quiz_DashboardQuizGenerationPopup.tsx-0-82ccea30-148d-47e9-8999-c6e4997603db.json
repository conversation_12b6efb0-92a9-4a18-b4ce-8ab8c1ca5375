{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/quiz/DashboardQuizGenerationPopup.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>Trigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  customPrompt: string;\n}\n\ninterface DashboardQuizGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: QuizGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxQuestions?: number;\n}\n\nexport const DashboardQuizGenerationPopup: React.FC<DashboardQuizGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxQuestions = 20,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<QuizGenerationOptions>({\n    numberOfQuestions: 5,\n    customPrompt: \"\",\n  });\n\n  const handleGenerate = async () => {\n    if (options.numberOfQuestions <= 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Quiz generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.numberOfQuestions > 0 && options.numberOfQuestions <= maxQuestions;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-card border-border\">\n        <DialogHeader>\n          <DialogTitle className=\"text-card-foreground\">Generate Quiz Options</DialogTitle>\n        </DialogHeader>\n        \n        <div className=\"space-y-6 py-4\">\n          {/* Number of Questions */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfQuestions\" className=\"text-card-foreground\">\n              Number of Questions\n            </Label>\n            <Input\n              id=\"numberOfQuestions\"\n              type=\"number\"\n              min=\"1\"\n              max={maxQuestions}\n              value={options.numberOfQuestions}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-background border-border text-foreground\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Choose between 1 and {maxQuestions} questions\n            </p>\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-card-foreground\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-background border-border text-foreground min-h-[80px]\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Add specific instructions for the AI on what kind of questions you want.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isGenerating || !isValidOptions}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {isGenerating ? (\n              <>\n                <Spinner size=\"sm\" className=\"mr-2\" />\n                Generating...\n              </>\n            ) : (\n              \"Generate Quiz\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n", "modifiedCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>Trigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface QuizGenerationOptions {\n  numberOfQuestions: number;\n  customPrompt: string;\n}\n\ninterface DashboardQuizGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: QuizGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxQuestions?: number;\n}\n\nexport const DashboardQuizGenerationPopup: React.FC<DashboardQuizGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxQuestions = 20,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<QuizGenerationOptions>({\n    numberOfQuestions: 5,\n    customPrompt: \"\",\n  });\n\n  const handleGenerate = async () => {\n    if (options.numberOfQuestions <= 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Quiz generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.numberOfQuestions > 0 && options.numberOfQuestions <= maxQuestions;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-card border-border\">\n        <DialogHeader>\n          <DialogTitle className=\"text-card-foreground\">Generate Quiz Options</DialogTitle>\n        </DialogHeader>\n        \n        <div className=\"space-y-6 py-4\">\n          {/* Number of Questions */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfQuestions\" className=\"text-card-foreground\">\n              Number of Questions\n            </Label>\n            <Input\n              id=\"numberOfQuestions\"\n              type=\"number\"\n              min=\"1\"\n              max={maxQuestions}\n              value={options.numberOfQuestions}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfQuestions: Math.max(1, Math.min(maxQuestions, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-background border-border text-foreground\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Choose between 1 and {maxQuestions} questions\n            </p>\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-card-foreground\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"e.g., 'Focus on definitions', 'Create scenario-based questions', 'Make questions suitable for beginners'\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-background border-border text-foreground min-h-[80px]\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Add specific instructions for the AI on what kind of questions you want.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isGenerating || !isValidOptions}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {isGenerating ? (\n              <>\n                <Spinner size=\"sm\" className=\"mr-2\" />\n                Generating...\n              </>\n            ) : (\n              \"Generate Quiz\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n"}