{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/api.ts"}, "originalCode": "import { AIProviderSettings } from \"@/types\"; // Removed Document import\nimport {\n  GenerateFlashcardsResponse,\n  FlashcardSet,\n} from \"@shared/types/flashcards\";\n\nimport { Quiz, AIProviderConfig, GetAllQuizzesApiResponse, QuestionType as SharedQuestionType } from \"@shared/types/quiz\";\nimport { supabase } from \"./supabaseClient\"; // Added Tables import\nimport { Tables } from \"../types/supabase\";\n\ninterface GenerateFlashcardsApiPayload {\n  textContent: string;\n  documentId: string;\n  deckTitle?: string;\n  count?: number; // Optional: based on server/routes.ts schema\n  customPrompt?: string; // New: custom prompt for AI generation\n  // aiSettings removed - credentials are retrieved from secure backend storage\n}\n\n/**\n * Calls the backend API to generate flashcards.\n */\nexport async function generateFlashcardsAPI(\n  payload: GenerateFlashcardsApiPayload\n): Promise<any> {\n  // AI credentials are now handled by the backend\n  const apiPayload = {\n    ...payload,\n  };\n\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    console.log(\"📤 generateFlashcardsAPI sending request:\", {\n      url: \"/api/flashcards/generate\",\n      payload: apiPayload,\n      hasAuth: !!session?.access_token\n    });\n\n    const response = await fetch(\"/api/flashcards/generate\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(apiPayload),\n    });\n\n    console.log(\"📥 generateFlashcardsAPI response status:\", response.status, response.statusText);\n\n    if (!response.ok) {\n      // Try to get the response text first for debugging\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        // Try to parse the text as JSON if possible\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        // If it's not JSON, create a more descriptive error message\n        throw new Error(\n          `Failed to generate flashcards: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to generate flashcards: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    // Get response text first for debugging\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText);\n      console.log(\"📥 generateFlashcardsAPI parsed response:\", data);\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response: ${errorMessage}`);\n    }\n\n    // Return the full response - the mapping function will handle the structure\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\nexport interface GenerateAiQuizApiPayload {\n  textContent?: string; // Optional: Direct text content for immediate generation (UploadSection)\n  documentId?: string; // Optional: ID of the study document for single document generation\n  documentIds?: string[]; // Array of document IDs for multi-document generation\n  quizName: string;\n  quizDescription?: string;\n  customPrompt?: string; // This is used by CreateQuizForm, not directly by UploadSection's simplified form\n  generationOptions: {\n    numberOfQuestions: number;\n    questionTypes: string[];\n  };\n  aiConfig?: AIProviderConfig;\n}\n\nexport interface GenerateAiQuizApiResponse {\n  success: boolean;\n  quiz?: GeneratedQuizStructure; // Use the more specific type\n  quizId: string;\n  id: string; // This seems redundant with quizId, but matches existing server response\n  name: string;\n}\n\n// Define a more specific type for the quiz structure returned by /generate endpoint\ninterface GeneratedQuizStructure {\n  id: string;\n  name: string;\n  // other quiz metadata fields from Tables<\"quizzes\"> can be added if needed\n  questions: Array<{\n    id: string;\n    questionText: string; // Matches server response structure\n    type: SharedQuestionType; \n    options?: any; \n    correctAnswer?: string | boolean | string[] | null; // Matches server response structure\n    explanation?: string | null;\n  }>;\n}\n\nexport interface CreateQuizApiPayload {\n  name: string;\n  description?: string;\n  study_document_id?: string;\n}\n\nexport interface CreateQuizApiResponse {\n  id: string;\n  name: string;\n}\n\n/**\n * Calls the backend API to create a quiz manually.\n */\nexport async function createQuizAPI(\n  payload: CreateQuizApiPayload\n): Promise<CreateQuizApiResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/quizzes\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to create quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to create quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as CreateQuizApiResponse;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Create Quiz API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to generate a quiz using AI.\n */\nexport async function generateAiQuizAPI(\n  payload: GenerateAiQuizApiPayload\n): Promise<GenerateAiQuizApiResponse> {\n  // Ensure backward compatibility\n  let apiPayload: any = {\n    ...payload,\n    // If documentIds is provided, use it; otherwise use documentId wrapped in an array\n    documentIds:\n      payload.documentIds && payload.documentIds.length > 0 ? payload.documentIds : (payload.documentId ? [payload.documentId] : []),\n    customPrompt: payload.customPrompt,\n  };\n  // Remove the old single documentId to avoid confusion\n  if (\"documentId\" in apiPayload) {\n    delete apiPayload.documentId;\n  }\n\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/quizzes/generate\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(apiPayload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to generate AI quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to generate AI quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as GenerateAiQuizApiResponse; // Assuming the direct response is the quiz object\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from AI Quiz Generation API: ${errorMessage}`);\n    }\n\n    // Ensure the returned data matches GenerateAiQuizApiResponse, specifically id and name\n    if (!data || typeof data.id !== \"string\" || typeof data.name !== \"string\") {\n      throw new Error(\n        \"Unexpected response structure from AI Quiz Generation API. Expected id and name.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch all quizzes for the authenticated user.\n */\nexport async function getAllQuizzesAPI(): Promise<GetAllQuizzesApiResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    // Make sure the URL matches exactly what the server expects (removing trailing slash)\n    const response = await fetch(\"/api/quizzes/\", {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch quizzes: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch quizzes: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as GetAllQuizzesApiResponse;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get All Quizzes API: ${errorMessage}`);\n    }\n\n    // Basic validation for the response structure\n    if (!data || !Array.isArray(data.quizzes)) {\n      throw new Error(\n        \"Unexpected response structure from Get All Quizzes API. Expected an object with a 'quizzes' array.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch a single quiz by ID.\n */\nexport async function getQuizByIdAPI(quizId: string): Promise<any> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/quizzes/${quizId}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText);\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Quiz API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Document API Functions\n// export interface GetDocumentsApiResponse { // This type is not used by getDocumentsAPI\n//   documents: Document[];\n// }\n\nexport interface CreateDocumentApiPayload {\n  name: string;\n  content: string;\n  type: string;\n  createdAt: number;\n  size: number;\n}\n\nexport interface UpdateDocumentApiPayload {\n  name?: string;\n  content?: string;\n  type?: string;\n  size?: number;\n}\n\n/**\n * Calls the backend API to fetch all documents for the authenticated user.\n */\nexport async function getDocumentsAPI(): Promise<Tables<\"study_documents\">[]> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/documents\", {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch documents: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch documents: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">[];\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Documents API: ${errorMessage}`);\n    }\n\n    // Basic validation for the response structure\n    if (!Array.isArray(data)) {\n      throw new Error(\n        \"Unexpected response structure from Get Documents API. Expected an array of documents.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch a single document by ID.\n */\nexport async function getDocumentAPI(\n  id: string\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to create a new document.\n */\nexport async function createDocumentAPI(\n  payload: CreateDocumentApiPayload\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/documents\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to create document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to create document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Create Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to update a document.\n */\nexport async function updateDocumentAPI(\n  id: string,\n  payload: UpdateDocumentApiPayload\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to update document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to update document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Update Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to delete a document.\n */\nexport async function deleteDocumentAPI(id: string): Promise<void> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"DELETE\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to delete document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to delete document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get the backend API URL from environment variables\nconst API_BASE_URL =\n  import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\n// Helper to get the current user's auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n    return session?.access_token || null;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Helper to create authenticated request headers\nasync function getAuthHeaders(): Promise<HeadersInit> {\n  const token = await getAuthToken();\n  return {\n    \"Content-Type\": \"application/json\",\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n}\n\n// Document upload types\nexport interface SecureUploadRequest {\n  fileName: string;\n  content: string;\n  contentType: string;\n  sizeBytes: number;\n  documentId?: string;\n}\n\nexport interface SecureUploadResponse {\n  success: boolean;\n  document: {\n    id: string;\n    user_id: string;\n    file_name: string;\n    file_path: string;\n    content_type: string;\n    size_bytes: number;\n    status: string;\n    extracted_text_path: string;\n    extracted_text_summary: string;\n    created_at: string;\n    updated_at: string;\n  };\n  message: string;\n}\n\n// Secure document upload through backend\nexport async function uploadDocumentSecure(\n  uploadData: SecureUploadRequest\n): Promise<SecureUploadResponse> {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(`${API_BASE_URL}/documents/secure-upload`, {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify(uploadData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get all documents for the current user (using Supabase directly for now, but could be moved to backend)\nexport async function getUserDocuments() {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(`${API_BASE_URL}/documents`, {\n      method: \"GET\",\n      headers,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const documents = await response.json();\n    return documents;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get document content securely through backend\nexport async function getDocumentContentAPI(\n  documentId: string\n): Promise<string> {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(\n      `${API_BASE_URL}/documents/${documentId}/content`,\n      {\n        method: \"GET\",\n        headers,\n      }\n    );\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const content = await response.text();\n    return content;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to delete a quiz.\n */\nexport async function deleteQuizAPI(quizId: string): Promise<void> {\n  const {\n    data: { session },\n  } = await supabase.auth.getSession();\n\n  const response = await fetch(`/api/quizzes/${quizId}`, {\n    method: \"DELETE\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      Authorization: `Bearer ${session?.access_token || \"\"}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error || `Failed to delete quiz: ${response.statusText}`\n    );\n  }\n}\n\n// Types for AI Question Generation via Server\ninterface AIGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n}\n\n// Types for Credentials API\ninterface StoreCredentialsRequest {\n  provider: string;\n  apiKey: string;\n  baseUrl: string;\n  extractionModel: string;\n  generationModel: string;\n}\n\ninterface StoreCredentialsResponse {\n  success: boolean;\n  message: string;\n}\n\ninterface GetCredentialsResponse {\n  success: boolean;\n  hasCredentials: boolean;\n  configuration?: {\n    provider: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n}\n\n/**\n * Store user's AI provider credentials securely on the backend\n */\nexport async function storeCredentialsAPI(\n  credentials: StoreCredentialsRequest\n): Promise<StoreCredentialsResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to save credentials\");\n    }\n\n    console.log(\"📤 storeCredentialsAPI sending request:\", {\n      url: \"/api/credentials\",\n      provider: credentials.provider,\n      hasAuth: !!session?.access_token\n    });\n\n    const response = await fetch(\"/api/credentials\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n      body: JSON.stringify(credentials),\n    });\n\n    console.log(\"📥 storeCredentialsAPI response status:\", response.status, response.statusText);\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to store credentials: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    console.error(\"❌ storeCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\n/**\n * Get user's stored credentials configuration (without API key)\n */\nexport async function getCredentialsAPI(\n  provider: string\n): Promise<GetCredentialsResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to retrieve credentials\");\n    }\n\n    const response = await fetch(`/api/credentials/${provider}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n    });\n\n    if (!response.ok) {\n      if (response.status === 404) {\n        return {\n          success: false,\n          hasCredentials: false,\n        };\n      }\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to retrieve credentials: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    console.error(\"❌ getCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\n/**\n * Delete user's stored credentials\n */\nexport async function deleteCredentialsAPI(provider: string): Promise<void> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to delete credentials\");\n    }\n\n    const response = await fetch(`/api/credentials/${provider}`, {\n      method: \"DELETE\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to delete credentials: ${response.statusText}`\n      );\n    }\n  } catch (error) {\n    console.error(\"❌ deleteCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\nexport interface GenerateAiQuestionsFromServerPayload {\n  documentId: string;\n  quizName: string;\n  customPrompt?: string;\n  generationOptions: AIGenerationOptions;\n}\n\n// Matches the AIGeneratedQuestion interface in the backend/edge function\nexport interface AIGeneratedQuestion {\n  question_text: string;\n  type:\n    | \"multiple_choice\"\n    | \"true_false\"\n    | \"short_answer\"\n    | \"select_all_that_apply\";\n  options?: { text: string; is_correct: boolean }[];\n  correct_answer: string;\n  explanation?: string;\n}\n\nexport async function generateAiQuestionsFromDocumentAPI(\n  payload: GenerateAiQuestionsFromServerPayload\n): Promise<AIGeneratedQuestion[]> {\n  const headers = await getAuthHeaders();\n  const response = await fetch(\n    `${API_BASE_URL}/ai/generate-questions-from-document`,\n    {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify(payload),\n    }\n  );\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error ||\n        `Failed to generate AI questions: ${response.statusText}`\n    );\n  }\n  return response.json();\n}\n\n// Types for adding questions to quiz\n// This should align with your quiz_questions table structure, excluding quiz_id/user_id managed by backend\nexport interface QuizQuestionBatchInsertPayload {\n  question_text: string;\n  type: string; // Should match Enums<\"question_type\">\n  options?: any; // JSONB\n  correct_answer?: string;\n  explanation?: string;\n}\n\nexport async function addQuestionsToQuizAPI(\n  quizId: string,\n  questions: QuizQuestionBatchInsertPayload[]\n): Promise<Tables<\"quiz_questions\">[]> {\n  const headers = await getAuthHeaders();\n  const response = await fetch(\n    `${API_BASE_URL}/quizzes/${quizId}/questions/batch`,\n    {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify({ questions }),\n    }\n  );\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error ||\n        `Failed to add questions to quiz: ${response.statusText}`\n    );\n  }\n  return response.json();\n}\n\n// Define the structure for a multiple-choice option\nexport interface McqOption {\n  text: string;\n  is_correct: boolean;\n}\n\nexport interface CreateQuizApiPayload {\n  name: string;\n  description?: string;\n  study_document_id?: string;\n}\n\nexport interface CreateQuizApiResponse {\n  id: string;\n  name: string;\n}\n", "modifiedCode": "import { AIProviderSettings } from \"@/types\"; // Removed Document import\nimport {\n  GenerateFlashcardsResponse,\n  FlashcardSet,\n} from \"@shared/types/flashcards\";\n\nimport { Quiz, AIProviderConfig, GetAllQuizzesApiResponse, QuestionType as SharedQuestionType } from \"@shared/types/quiz\";\nimport { supabase } from \"./supabaseClient\"; // Added Tables import\nimport { Tables } from \"../types/supabase\";\n\ninterface GenerateFlashcardsApiPayload {\n  textContent: string;\n  documentId: string;\n  deckTitle?: string;\n  count?: number; // Optional: based on server/routes.ts schema\n  customPrompt?: string; // New: custom prompt for AI generation\n  // aiSettings removed - credentials are retrieved from secure backend storage\n}\n\n/**\n * Calls the backend API to generate flashcards.\n */\nexport async function generateFlashcardsAPI(\n  payload: GenerateFlashcardsApiPayload\n): Promise<any> {\n  // AI credentials are now handled by the backend\n  const apiPayload = {\n    ...payload,\n  };\n\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    console.log(\"📤 generateFlashcardsAPI sending request:\", {\n      url: \"/api/flashcards/generate\",\n      payload: apiPayload,\n      hasAuth: !!session?.access_token\n    });\n\n    const response = await fetch(\"/api/flashcards/generate\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(apiPayload),\n    });\n\n    console.log(\"📥 generateFlashcardsAPI response status:\", response.status, response.statusText);\n\n    if (!response.ok) {\n      // Try to get the response text first for debugging\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        // Try to parse the text as JSON if possible\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        // If it's not JSON, create a more descriptive error message\n        throw new Error(\n          `Failed to generate flashcards: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to generate flashcards: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    // Get response text first for debugging\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText);\n      console.log(\"📥 generateFlashcardsAPI parsed response:\", data);\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response: ${errorMessage}`);\n    }\n\n    // Return the full response - the mapping function will handle the structure\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\nexport interface GenerateAiQuizApiPayload {\n  textContent?: string; // Optional: Direct text content for immediate generation (UploadSection)\n  documentId?: string; // Optional: ID of the study document for single document generation\n  documentIds?: string[]; // Array of document IDs for multi-document generation\n  quizName: string;\n  quizDescription?: string;\n  customPrompt?: string; // This is used by CreateQuizForm, not directly by UploadSection's simplified form\n  generationOptions: {\n    numberOfQuestions: number;\n    questionTypes: string[];\n  };\n  // aiConfig removed - credentials are retrieved from secure backend storage\n}\n\nexport interface GenerateAiQuizApiResponse {\n  success: boolean;\n  quiz?: GeneratedQuizStructure; // Use the more specific type\n  quizId: string;\n  id: string; // This seems redundant with quizId, but matches existing server response\n  name: string;\n}\n\n// Define a more specific type for the quiz structure returned by /generate endpoint\ninterface GeneratedQuizStructure {\n  id: string;\n  name: string;\n  // other quiz metadata fields from Tables<\"quizzes\"> can be added if needed\n  questions: Array<{\n    id: string;\n    questionText: string; // Matches server response structure\n    type: SharedQuestionType; \n    options?: any; \n    correctAnswer?: string | boolean | string[] | null; // Matches server response structure\n    explanation?: string | null;\n  }>;\n}\n\nexport interface CreateQuizApiPayload {\n  name: string;\n  description?: string;\n  study_document_id?: string;\n}\n\nexport interface CreateQuizApiResponse {\n  id: string;\n  name: string;\n}\n\n/**\n * Calls the backend API to create a quiz manually.\n */\nexport async function createQuizAPI(\n  payload: CreateQuizApiPayload\n): Promise<CreateQuizApiResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/quizzes\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to create quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to create quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as CreateQuizApiResponse;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Create Quiz API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to generate a quiz using AI.\n */\nexport async function generateAiQuizAPI(\n  payload: GenerateAiQuizApiPayload\n): Promise<GenerateAiQuizApiResponse> {\n  // Ensure backward compatibility\n  let apiPayload: any = {\n    ...payload,\n    // If documentIds is provided, use it; otherwise use documentId wrapped in an array\n    documentIds:\n      payload.documentIds && payload.documentIds.length > 0 ? payload.documentIds : (payload.documentId ? [payload.documentId] : []),\n    customPrompt: payload.customPrompt,\n  };\n  // Remove the old single documentId to avoid confusion\n  if (\"documentId\" in apiPayload) {\n    delete apiPayload.documentId;\n  }\n\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/quizzes/generate\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(apiPayload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to generate AI quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to generate AI quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as GenerateAiQuizApiResponse; // Assuming the direct response is the quiz object\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from AI Quiz Generation API: ${errorMessage}`);\n    }\n\n    // Ensure the returned data matches GenerateAiQuizApiResponse, specifically id and name\n    if (!data || typeof data.id !== \"string\" || typeof data.name !== \"string\") {\n      throw new Error(\n        \"Unexpected response structure from AI Quiz Generation API. Expected id and name.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch all quizzes for the authenticated user.\n */\nexport async function getAllQuizzesAPI(): Promise<GetAllQuizzesApiResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    // Make sure the URL matches exactly what the server expects (removing trailing slash)\n    const response = await fetch(\"/api/quizzes/\", {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch quizzes: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch quizzes: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as GetAllQuizzesApiResponse;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get All Quizzes API: ${errorMessage}`);\n    }\n\n    // Basic validation for the response structure\n    if (!data || !Array.isArray(data.quizzes)) {\n      throw new Error(\n        \"Unexpected response structure from Get All Quizzes API. Expected an object with a 'quizzes' array.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch a single quiz by ID.\n */\nexport async function getQuizByIdAPI(quizId: string): Promise<any> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/quizzes/${quizId}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch quiz: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch quiz: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText);\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Quiz API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Document API Functions\n// export interface GetDocumentsApiResponse { // This type is not used by getDocumentsAPI\n//   documents: Document[];\n// }\n\nexport interface CreateDocumentApiPayload {\n  name: string;\n  content: string;\n  type: string;\n  createdAt: number;\n  size: number;\n}\n\nexport interface UpdateDocumentApiPayload {\n  name?: string;\n  content?: string;\n  type?: string;\n  size?: number;\n}\n\n/**\n * Calls the backend API to fetch all documents for the authenticated user.\n */\nexport async function getDocumentsAPI(): Promise<Tables<\"study_documents\">[]> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/documents\", {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch documents: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch documents: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">[];\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Documents API: ${errorMessage}`);\n    }\n\n    // Basic validation for the response structure\n    if (!Array.isArray(data)) {\n      throw new Error(\n        \"Unexpected response structure from Get Documents API. Expected an array of documents.\"\n      );\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to fetch a single document by ID.\n */\nexport async function getDocumentAPI(\n  id: string\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to fetch document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to fetch document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Get Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to create a new document.\n */\nexport async function createDocumentAPI(\n  payload: CreateDocumentApiPayload\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(\"/api/documents\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to create document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to create document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Create Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to update a document.\n */\nexport async function updateDocumentAPI(\n  id: string,\n  payload: UpdateDocumentApiPayload\n): Promise<Tables<\"study_documents\">> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"PUT\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n      body: JSON.stringify(payload),\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to update document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to update document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n\n    const responseText = await response.text();\n\n    let data;\n    try {\n      data = JSON.parse(responseText) as Tables<\"study_documents\">;\n    } catch (e) {\n      const errorMessage = e instanceof Error ? e.message : String(e);\n      throw new Error(`Invalid JSON response from Update Document API: ${errorMessage}`);\n    }\n\n    return data;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to delete a document.\n */\nexport async function deleteDocumentAPI(id: string): Promise<void> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    const response = await fetch(`/api/documents/${id}`, {\n      method: \"DELETE\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session?.access_token || \"\"}`,\n      },\n    });\n\n    if (!response.ok) {\n      const responseText = await response.text();\n\n      let errorData;\n      try {\n        errorData = JSON.parse(responseText);\n      } catch (e) {\n        throw new Error(\n          `Failed to delete document: ${response.status} ${response.statusText}.\\n` +\n            `Server returned non-JSON response: ${responseText.substring(\n              0,\n              200\n            )}...`\n        );\n      }\n\n      throw new Error(\n        `Failed to delete document: ${response.status} ${response.statusText}.\\n` +\n          (errorData.message ? `Message: ${errorData.message}` : \"\") +\n          (errorData.error ? ` Details: ${errorData.error}` : \"\") +\n          (errorData.errors\n            ? ` Validation: ${JSON.stringify(errorData.errors)}`\n            : \"\")\n      );\n    }\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get the backend API URL from environment variables\nconst API_BASE_URL =\n  import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";\n\n// Helper to get the current user's auth token\nasync function getAuthToken(): Promise<string | null> {\n  try {\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n    return session?.access_token || null;\n  } catch (error) {\n    return null;\n  }\n}\n\n// Helper to create authenticated request headers\nasync function getAuthHeaders(): Promise<HeadersInit> {\n  const token = await getAuthToken();\n  return {\n    \"Content-Type\": \"application/json\",\n    ...(token && { Authorization: `Bearer ${token}` }),\n  };\n}\n\n// Document upload types\nexport interface SecureUploadRequest {\n  fileName: string;\n  content: string;\n  contentType: string;\n  sizeBytes: number;\n  documentId?: string;\n}\n\nexport interface SecureUploadResponse {\n  success: boolean;\n  document: {\n    id: string;\n    user_id: string;\n    file_name: string;\n    file_path: string;\n    content_type: string;\n    size_bytes: number;\n    status: string;\n    extracted_text_path: string;\n    extracted_text_summary: string;\n    created_at: string;\n    updated_at: string;\n  };\n  message: string;\n}\n\n// Secure document upload through backend\nexport async function uploadDocumentSecure(\n  uploadData: SecureUploadRequest\n): Promise<SecureUploadResponse> {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(`${API_BASE_URL}/documents/secure-upload`, {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify(uploadData),\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get all documents for the current user (using Supabase directly for now, but could be moved to backend)\nexport async function getUserDocuments() {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(`${API_BASE_URL}/documents`, {\n      method: \"GET\",\n      headers,\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const documents = await response.json();\n    return documents;\n  } catch (error) {\n    throw error;\n  }\n}\n\n// Get document content securely through backend\nexport async function getDocumentContentAPI(\n  documentId: string\n): Promise<string> {\n  try {\n    const headers = await getAuthHeaders();\n\n    const response = await fetch(\n      `${API_BASE_URL}/documents/${documentId}/content`,\n      {\n        method: \"GET\",\n        headers,\n      }\n    );\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `HTTP ${response.status}: ${response.statusText}`\n      );\n    }\n\n    const content = await response.text();\n    return content;\n  } catch (error) {\n    throw error;\n  }\n}\n\n/**\n * Calls the backend API to delete a quiz.\n */\nexport async function deleteQuizAPI(quizId: string): Promise<void> {\n  const {\n    data: { session },\n  } = await supabase.auth.getSession();\n\n  const response = await fetch(`/api/quizzes/${quizId}`, {\n    method: \"DELETE\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n      Authorization: `Bearer ${session?.access_token || \"\"}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error || `Failed to delete quiz: ${response.statusText}`\n    );\n  }\n}\n\n// Types for AI Question Generation via Server\ninterface AIGenerationOptions {\n  numberOfQuestions: number;\n  questionTypes: string[];\n}\n\n// Types for Credentials API\ninterface StoreCredentialsRequest {\n  provider: string;\n  apiKey: string;\n  baseUrl: string;\n  extractionModel: string;\n  generationModel: string;\n}\n\ninterface StoreCredentialsResponse {\n  success: boolean;\n  message: string;\n}\n\ninterface GetCredentialsResponse {\n  success: boolean;\n  hasCredentials: boolean;\n  configuration?: {\n    provider: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n}\n\n/**\n * Store user's AI provider credentials securely on the backend\n */\nexport async function storeCredentialsAPI(\n  credentials: StoreCredentialsRequest\n): Promise<StoreCredentialsResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to save credentials\");\n    }\n\n    console.log(\"📤 storeCredentialsAPI sending request:\", {\n      url: \"/api/credentials\",\n      provider: credentials.provider,\n      hasAuth: !!session?.access_token\n    });\n\n    const response = await fetch(\"/api/credentials\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n      body: JSON.stringify(credentials),\n    });\n\n    console.log(\"📥 storeCredentialsAPI response status:\", response.status, response.statusText);\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to store credentials: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    console.error(\"❌ storeCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\n/**\n * Get user's stored credentials configuration (without API key)\n */\nexport async function getCredentialsAPI(\n  provider: string\n): Promise<GetCredentialsResponse> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to retrieve credentials\");\n    }\n\n    const response = await fetch(`/api/credentials/${provider}`, {\n      method: \"GET\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n    });\n\n    if (!response.ok) {\n      if (response.status === 404) {\n        return {\n          success: false,\n          hasCredentials: false,\n        };\n      }\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to retrieve credentials: ${response.statusText}`\n      );\n    }\n\n    const result = await response.json();\n    return result;\n  } catch (error) {\n    console.error(\"❌ getCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\n/**\n * Delete user's stored credentials\n */\nexport async function deleteCredentialsAPI(provider: string): Promise<void> {\n  try {\n    // Get the current session for auth token\n    const {\n      data: { session },\n    } = await supabase.auth.getSession();\n\n    if (!session?.access_token) {\n      throw new Error(\"Authentication required to delete credentials\");\n    }\n\n    const response = await fetch(`/api/credentials/${provider}`, {\n      method: \"DELETE\",\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${session.access_token}`,\n      },\n    });\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({}));\n      throw new Error(\n        errorData.error || `Failed to delete credentials: ${response.statusText}`\n      );\n    }\n  } catch (error) {\n    console.error(\"❌ deleteCredentialsAPI error:\", error);\n    throw error;\n  }\n}\n\nexport interface GenerateAiQuestionsFromServerPayload {\n  documentId: string;\n  quizName: string;\n  customPrompt?: string;\n  generationOptions: AIGenerationOptions;\n}\n\n// Matches the AIGeneratedQuestion interface in the backend/edge function\nexport interface AIGeneratedQuestion {\n  question_text: string;\n  type:\n    | \"multiple_choice\"\n    | \"true_false\"\n    | \"short_answer\"\n    | \"select_all_that_apply\";\n  options?: { text: string; is_correct: boolean }[];\n  correct_answer: string;\n  explanation?: string;\n}\n\nexport async function generateAiQuestionsFromDocumentAPI(\n  payload: GenerateAiQuestionsFromServerPayload\n): Promise<AIGeneratedQuestion[]> {\n  const headers = await getAuthHeaders();\n  const response = await fetch(\n    `${API_BASE_URL}/ai/generate-questions-from-document`,\n    {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify(payload),\n    }\n  );\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error ||\n        `Failed to generate AI questions: ${response.statusText}`\n    );\n  }\n  return response.json();\n}\n\n// Types for adding questions to quiz\n// This should align with your quiz_questions table structure, excluding quiz_id/user_id managed by backend\nexport interface QuizQuestionBatchInsertPayload {\n  question_text: string;\n  type: string; // Should match Enums<\"question_type\">\n  options?: any; // JSONB\n  correct_answer?: string;\n  explanation?: string;\n}\n\nexport async function addQuestionsToQuizAPI(\n  quizId: string,\n  questions: QuizQuestionBatchInsertPayload[]\n): Promise<Tables<\"quiz_questions\">[]> {\n  const headers = await getAuthHeaders();\n  const response = await fetch(\n    `${API_BASE_URL}/quizzes/${quizId}/questions/batch`,\n    {\n      method: \"POST\",\n      headers,\n      body: JSON.stringify({ questions }),\n    }\n  );\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(\n      errorData.error ||\n        `Failed to add questions to quiz: ${response.statusText}`\n    );\n  }\n  return response.json();\n}\n\n// Define the structure for a multiple-choice option\nexport interface McqOption {\n  text: string;\n  is_correct: boolean;\n}\n\nexport interface CreateQuizApiPayload {\n  name: string;\n  description?: string;\n  study_document_id?: string;\n}\n\nexport interface CreateQuizApiResponse {\n  id: string;\n  name: string;\n}\n"}