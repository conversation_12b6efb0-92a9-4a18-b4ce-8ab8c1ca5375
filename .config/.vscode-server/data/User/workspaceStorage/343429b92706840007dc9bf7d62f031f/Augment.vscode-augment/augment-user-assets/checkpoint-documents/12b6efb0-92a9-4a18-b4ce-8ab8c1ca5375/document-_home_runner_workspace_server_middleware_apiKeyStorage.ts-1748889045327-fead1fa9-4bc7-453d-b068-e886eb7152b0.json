{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/apiKeyStorage.ts"}, "originalCode": "import { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\nimport crypto from 'crypto';\n\n// Create Supabase client for API key operations\nconst supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);\n\n// Encryption key for API keys (should be from environment variable)\nconst ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'default-key-change-in-production-32-chars';\nconst ALGORITHM = 'aes-256-cbc';\n\n// Ensure the key is exactly 32 bytes for AES-256\nconst getEncryptionKey = (): Buffer => {\n  if (typeof ENCRYPTION_KEY === 'string') {\n    return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);\n  }\n  return ENCRYPTION_KEY;\n};\n\n/**\n * Encrypt an API key for secure storage\n */\nfunction encryptApiKey(apiKey: string): { encrypted: string; iv: string; tag: string } {\n  const iv = crypto.randomBytes(16);\n  const key = getEncryptionKey();\n  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);\n\n  let encrypted = cipher.update(apiKey, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n\n  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity\n  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');\n\n  return {\n    encrypted,\n    iv: iv.toString('hex'),\n    tag\n  };\n}\n\n/**\n * Decrypt an API key from storage\n */\nfunction decryptApiKey(encryptedData: { encrypted: string; iv: string; tag: string }): string {\n  const iv = Buffer.from(encryptedData.iv, 'hex');\n  const key = getEncryptionKey();\n\n  // Verify integrity using HMAC\n  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');\n  if (expectedTag !== encryptedData.tag) {\n    throw new Error('Data integrity check failed');\n  }\n\n  const decipher = crypto.createDecipher(ALGORITHM, key);\n\n  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n\n  return decrypted;\n}\n\n/**\n * Store user's AI provider credentials securely in database\n */\nexport async function storeUserApiKey(\n  userId: string, \n  provider: string, \n  apiKey: string, \n  baseUrl: string,\n  models: { extraction: string; generation: string }\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    // Encrypt the API key\n    const encryptedKey = encryptApiKey(apiKey);\n    \n    // Store in database with encryption metadata\n    const { error } = await supabase\n      .from('user_ai_credentials')\n      .upsert({\n        user_id: userId,\n        provider,\n        encrypted_api_key: encryptedKey.encrypted,\n        encryption_iv: encryptedKey.iv,\n        encryption_tag: encryptedKey.tag,\n        base_url: baseUrl,\n        extraction_model: models.extraction,\n        generation_model: models.generation,\n        updated_at: new Date().toISOString()\n      }, {\n        onConflict: 'user_id,provider'\n      });\n\n    if (error) {\n      console.error('Error storing user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    return { success: true };\n  } catch (error: any) {\n    console.error('Error in storeUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Retrieve and decrypt user's AI provider credentials\n */\nexport async function getUserApiKey(\n  userId: string, \n  provider: string\n): Promise<{ \n  success: boolean; \n  credentials?: {\n    apiKey: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n  error?: string;\n}> {\n  try {\n    const { data, error } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('user_id', userId)\n      .eq('provider', provider)\n      .single();\n\n    if (error) {\n      if (error.code === 'PGRST116') {\n        // No credentials found\n        return { success: false, error: 'No credentials found for this provider' };\n      }\n      console.error('Error retrieving user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    // Decrypt the API key\n    const decryptedApiKey = decryptApiKey({\n      encrypted: data.encrypted_api_key,\n      iv: data.encryption_iv,\n      tag: data.encryption_tag\n    });\n\n    return {\n      success: true,\n      credentials: {\n        apiKey: decryptedApiKey,\n        baseUrl: data.base_url,\n        extractionModel: data.extraction_model,\n        generationModel: data.generation_model\n      }\n    };\n  } catch (error: any) {\n    console.error('Error in getUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Delete user's stored API credentials\n */\nexport async function deleteUserApiKey(\n  userId: string, \n  provider: string\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const { error } = await supabase\n      .from('user_ai_credentials')\n      .delete()\n      .eq('user_id', userId)\n      .eq('provider', provider);\n\n    if (error) {\n      console.error('Error deleting user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    return { success: true };\n  } catch (error: any) {\n    console.error('Error in deleteUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Get user credentials for ephemeral use (for AI API calls)\n * This function should be used only during AI API requests\n */\nexport async function getEphemeralUserCredentials(\n  userId: string,\n  provider: string\n): Promise<{\n  success: boolean;\n  credentials?: {\n    apiKey: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n  error?: string;\n}> {\n  // This is the same as getUserApiKey but with explicit naming\n  // to emphasize ephemeral usage\n  return getUserApiKey(userId, provider);\n}\n", "modifiedCode": "import { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\nimport crypto from 'crypto';\n\n// Create Supabase client for API key operations\nconst supabase = createClient(supabaseConfig.url, supabaseConfig.serviceRoleKey);\n\n// Encryption key for API keys (should be from environment variable)\nconst ENCRYPTION_KEY = process.env.API_KEY_ENCRYPTION_KEY || 'default-key-change-in-production-32-chars';\nconst ALGORITHM = 'aes-256-cbc';\n\n// Ensure the key is exactly 32 bytes for AES-256\nconst getEncryptionKey = (): Buffer => {\n  if (typeof ENCRYPTION_KEY === 'string') {\n    return crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);\n  }\n  return ENCRYPTION_KEY;\n};\n\n/**\n * Encrypt an API key for secure storage\n */\nfunction encryptApiKey(apiKey: string): { encrypted: string; iv: string; tag: string } {\n  const iv = crypto.randomBytes(16);\n  const key = getEncryptionKey();\n  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);\n\n  let encrypted = cipher.update(apiKey, 'utf8', 'hex');\n  encrypted += cipher.final('hex');\n\n  // For CBC mode, we don't have auth tag, so we'll use a hash for integrity\n  const tag = crypto.createHmac('sha256', key).update(encrypted + iv.toString('hex')).digest('hex');\n\n  return {\n    encrypted,\n    iv: iv.toString('hex'),\n    tag\n  };\n}\n\n/**\n * Decrypt an API key from storage\n */\nfunction decryptApiKey(encryptedData: { encrypted: string; iv: string; tag: string }): string {\n  const iv = Buffer.from(encryptedData.iv, 'hex');\n  const key = getEncryptionKey();\n\n  // Verify integrity using HMAC\n  const expectedTag = crypto.createHmac('sha256', key).update(encryptedData.encrypted + encryptedData.iv).digest('hex');\n  if (expectedTag !== encryptedData.tag) {\n    throw new Error('Data integrity check failed');\n  }\n\n  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);\n\n  let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');\n  decrypted += decipher.final('utf8');\n\n  return decrypted;\n}\n\n/**\n * Store user's AI provider credentials securely in database\n */\nexport async function storeUserApiKey(\n  userId: string, \n  provider: string, \n  apiKey: string, \n  baseUrl: string,\n  models: { extraction: string; generation: string }\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    // Encrypt the API key\n    const encryptedKey = encryptApiKey(apiKey);\n    \n    // Store in database with encryption metadata\n    const { error } = await supabase\n      .from('user_ai_credentials')\n      .upsert({\n        user_id: userId,\n        provider,\n        encrypted_api_key: encryptedKey.encrypted,\n        encryption_iv: encryptedKey.iv,\n        encryption_tag: encryptedKey.tag,\n        base_url: baseUrl,\n        extraction_model: models.extraction,\n        generation_model: models.generation,\n        updated_at: new Date().toISOString()\n      }, {\n        onConflict: 'user_id,provider'\n      });\n\n    if (error) {\n      console.error('Error storing user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    return { success: true };\n  } catch (error: any) {\n    console.error('Error in storeUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Retrieve and decrypt user's AI provider credentials\n */\nexport async function getUserApiKey(\n  userId: string, \n  provider: string\n): Promise<{ \n  success: boolean; \n  credentials?: {\n    apiKey: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n  error?: string;\n}> {\n  try {\n    const { data, error } = await supabase\n      .from('user_ai_credentials')\n      .select('*')\n      .eq('user_id', userId)\n      .eq('provider', provider)\n      .single();\n\n    if (error) {\n      if (error.code === 'PGRST116') {\n        // No credentials found\n        return { success: false, error: 'No credentials found for this provider' };\n      }\n      console.error('Error retrieving user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    // Decrypt the API key\n    const decryptedApiKey = decryptApiKey({\n      encrypted: data.encrypted_api_key,\n      iv: data.encryption_iv,\n      tag: data.encryption_tag\n    });\n\n    return {\n      success: true,\n      credentials: {\n        apiKey: decryptedApiKey,\n        baseUrl: data.base_url,\n        extractionModel: data.extraction_model,\n        generationModel: data.generation_model\n      }\n    };\n  } catch (error: any) {\n    console.error('Error in getUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Delete user's stored API credentials\n */\nexport async function deleteUserApiKey(\n  userId: string, \n  provider: string\n): Promise<{ success: boolean; error?: string }> {\n  try {\n    const { error } = await supabase\n      .from('user_ai_credentials')\n      .delete()\n      .eq('user_id', userId)\n      .eq('provider', provider);\n\n    if (error) {\n      console.error('Error deleting user API key:', error);\n      return { success: false, error: error.message };\n    }\n\n    return { success: true };\n  } catch (error: any) {\n    console.error('Error in deleteUserApiKey:', error);\n    return { success: false, error: error.message };\n  }\n}\n\n/**\n * Get user credentials for ephemeral use (for AI API calls)\n * This function should be used only during AI API requests\n */\nexport async function getEphemeralUserCredentials(\n  userId: string,\n  provider: string\n): Promise<{\n  success: boolean;\n  credentials?: {\n    apiKey: string;\n    baseUrl: string;\n    extractionModel: string;\n    generationModel: string;\n  };\n  error?: string;\n}> {\n  // This is the same as getUserApiKey but with explicit naming\n  // to emphasize ephemeral usage\n  return getUserApiKey(userId, provider);\n}\n"}