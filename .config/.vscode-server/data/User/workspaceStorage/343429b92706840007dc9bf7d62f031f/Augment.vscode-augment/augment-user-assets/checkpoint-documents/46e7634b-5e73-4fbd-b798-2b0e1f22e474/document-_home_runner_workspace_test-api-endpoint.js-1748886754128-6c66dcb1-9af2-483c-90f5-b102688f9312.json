{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "test-api-endpoint.js"}, "modifiedCode": "#!/usr/bin/env node\n\n// Test script to verify the credentials API endpoint\nimport { createClient } from '@supabase/supabase-js';\n\n// Environment variables\nconst SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDI5NDksImV4cCI6MjA2MjgxODk0OX0.0qS0Nh1aV4GjZYniANoUZKDDcuCcQxzTm-DnTeZsNlQ';\n\n// Create Supabase client\nconst supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);\n\nasync function testCredentialsAPI() {\n  console.log('🧪 Testing credentials API endpoint...\\n');\n  \n  try {\n    // Sign in as the test user\n    console.log('🔐 Signing in...');\n    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({\n      email: '<EMAIL>',\n      password: 'test-password' // This might not work, but let's try\n    });\n    \n    if (authError) {\n      console.log('❌ Auth failed, trying to get session...');\n      // Try to get existing session\n      const { data: sessionData } = await supabase.auth.getSession();\n      if (!sessionData.session) {\n        console.log('❌ No valid session found. Testing with mock token...');\n        return testWithMockAuth();\n      }\n      console.log('✅ Using existing session');\n    } else {\n      console.log('✅ Signed in successfully');\n    }\n    \n    // Get the session token\n    const { data: { session } } = await supabase.auth.getSession();\n    if (!session) {\n      console.log('❌ No session available. Testing with mock auth...');\n      return testWithMockAuth();\n    }\n    \n    console.log('📤 Testing POST /api/credentials...');\n    \n    // Test the credentials API\n    const response = await fetch('http://localhost:5000/api/credentials', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${session.access_token}`,\n      },\n      body: JSON.stringify({\n        provider: 'openrouter',\n        apiKey: 'sk-or-v1-test-api-key-12345',\n        baseUrl: 'https://openrouter.ai/api/v1',\n        extractionModel: 'google/gemini-2.5-flash-preview-05-20',\n        generationModel: 'google/gemini-2.5-pro-preview'\n      })\n    });\n    \n    console.log('📥 Response status:', response.status, response.statusText);\n    \n    const responseData = await response.json();\n    console.log('📥 Response data:', responseData);\n    \n    if (response.ok) {\n      console.log('✅ Credentials API test passed!');\n      \n      // Test retrieval\n      console.log('📤 Testing GET /api/credentials/openrouter...');\n      const getResponse = await fetch('http://localhost:5000/api/credentials/openrouter', {\n        method: 'GET',\n        headers: {\n          'Authorization': `Bearer ${session.access_token}`,\n        }\n      });\n      \n      const getResponseData = await getResponse.json();\n      console.log('📥 GET Response:', getResponseData);\n      \n      if (getResponse.ok) {\n        console.log('✅ Credentials retrieval test passed!');\n      } else {\n        console.log('❌ Credentials retrieval test failed');\n      }\n      \n      return true;\n    } else {\n      console.log('❌ Credentials API test failed');\n      return false;\n    }\n    \n  } catch (error) {\n    console.error('❌ Test error:', error);\n    return false;\n  }\n}\n\nasync function testWithMockAuth() {\n  console.log('🔧 Testing with service role key (bypassing RLS)...');\n  \n  try {\n    const response = await fetch('http://localhost:5000/api/credentials', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': 'Bearer mock-token-for-testing',\n      },\n      body: JSON.stringify({\n        provider: 'openrouter',\n        apiKey: 'sk-or-v1-test-api-key-12345',\n        baseUrl: 'https://openrouter.ai/api/v1',\n        extractionModel: 'google/gemini-2.5-flash-preview-05-20',\n        generationModel: 'google/gemini-2.5-pro-preview'\n      })\n    });\n    \n    console.log('📥 Response status:', response.status, response.statusText);\n    \n    const responseData = await response.json();\n    console.log('📥 Response data:', responseData);\n    \n    return response.ok;\n  } catch (error) {\n    console.error('❌ Mock test error:', error);\n    return false;\n  }\n}\n\nasync function main() {\n  const success = await testCredentialsAPI();\n  \n  if (success) {\n    console.log('\\n🎉 API endpoint test completed successfully!');\n  } else {\n    console.log('\\n❌ API endpoint test failed.');\n  }\n}\n\nmain().catch(console.error);\n"}