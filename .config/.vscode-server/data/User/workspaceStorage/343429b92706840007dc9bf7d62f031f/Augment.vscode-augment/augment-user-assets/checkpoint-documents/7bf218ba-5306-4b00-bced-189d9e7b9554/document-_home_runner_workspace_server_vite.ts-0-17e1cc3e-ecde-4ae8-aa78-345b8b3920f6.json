{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "originalCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer } from \"vite\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nexport function log(message: string) {\n  const now = new Date();\n  const timeStr = now.toLocaleTimeString();\n  console.log(`${timeStr} [express] ${message}`);\n}\n\nexport async function setupVite(app: Express) {\n  if (process.env.NODE_ENV === \"production\") {\n    log(\"Production mode detected, skipping Vite setup\");\n    return;\n  }\n\n  log(\"Setting up Vite dev middleware...\");\n  \n  try {\n    const vite = await createViteServer({\n      server: { middlewareMode: true },\n      appType: \"spa\",\n      // Pass any additional Vite config options\n    });\n\n    app.use(vite.middlewares);\n    log(\"Vite dev middleware set up successfully\");\n  } catch (e: any) {\n    log(`Error setting up Vite: ${e.message}`);\n    if (e.stack) log(e.stack);\n    process.exit(1);\n  }\n}\n\nexport function serveStatic(app: Express) {\n  // In production, serve the built frontend from the dist/public directory\n  const distPath = path.resolve(process.cwd(), \"dist/public\");\n  \n  // Check if the dist/public directory exists\n  if (!fs.existsSync(distPath)) {\n    log(`Warning: Static files directory ${distPath} does not exist!`);\n    log('Make sure to build the client first with npm run build');\n    return;\n  }\n  \n  log(`Serving static files from ${distPath}`);\n  \n  // Serve static files with caching for production\n  app.use(express.static(distPath, {\n    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,\n    etag: true,\n    index: false // Don't automatically serve index.html, we'll handle that below\n  }));\n\n  // For SPA routing - all non-API routes should serve the index.html\n  app.get(\"*\", (req, res, next) => {\n    // Skip API routes and let them be handled by their respective handlers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n    \n    // Serve the index.html for client-side routing\n    const indexPath = path.join(distPath, \"index.html\");\n    if (fs.existsSync(indexPath)) {\n      res.sendFile(indexPath);\n    } else {\n      log(`Warning: index.html not found at ${indexPath}`);\n      res.status(404).send('Frontend build not found');\n    }\n  });\n}\n", "modifiedCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer } from \"vite\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nexport function log(message: string) {\n  const now = new Date();\n  const timeStr = now.toLocaleTimeString();\n  console.log(`${timeStr} [express] ${message}`);\n}\n\nexport async function setupVite(app: Express) {\n  if (process.env.NODE_ENV === \"production\") {\n    log(\"Production mode detected, skipping Vite setup\");\n    return;\n  }\n\n  log(\"Setting up Vite dev middleware...\");\n  \n  try {\n    const vite = await createViteServer({\n      server: { middlewareMode: true },\n      appType: \"spa\",\n      // Pass any additional Vite config options\n    });\n\n    app.use(vite.middlewares);\n    log(\"Vite dev middleware set up successfully\");\n  } catch (e: any) {\n    log(`Error setting up Vite: ${e.message}`);\n    if (e.stack) log(e.stack);\n    process.exit(1);\n  }\n}\n\nexport function serveStatic(app: Express) {\n  // In production, serve the built frontend from the dist/public directory\n  const distPath = path.resolve(process.cwd(), \"dist/public\");\n  \n  // Check if the dist/public directory exists\n  if (!fs.existsSync(distPath)) {\n    log(`Warning: Static files directory ${distPath} does not exist!`);\n    log('Make sure to build the client first with npm run build');\n    return;\n  }\n  \n  log(`Serving static files from ${distPath}`);\n  \n  // Serve static files with caching for production\n  app.use(express.static(distPath, {\n    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,\n    etag: true,\n    index: false // Don't automatically serve index.html, we'll handle that below\n  }));\n\n  // For SPA routing - all non-API routes should serve the index.html\n  app.get(\"*\", (req, res, next) => {\n    // Skip API routes and let them be handled by their respective handlers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n    \n    // Serve the index.html for client-side routing\n    const indexPath = path.join(distPath, \"index.html\");\n    if (fs.existsSync(indexPath)) {\n      res.sendFile(indexPath);\n    } else {\n      log(`Warning: index.html not found at ${indexPath}`);\n      res.status(404).send('Frontend build not found');\n    }\n  });\n}\n"}