{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/README.md"}, "modifiedCode": "# ChewyAI Documentation\n\nWelcome to the ChewyAI documentation. This folder contains comprehensive documentation for the ChewyAI full-stack application.\n\n## 📁 Documentation Structure\n\n- **[RULES.md](./RULES.md)** - Development rules, coding standards, and best practices\n- **[MEMORIES.md](./MEMORIES.md)** - Important system decisions and architectural choices\n- **[DEPLOYMENT.md](./DEPLOYMENT.md)** - Production deployment guide and configuration\n- **[SECURITY.md](./SECURITY.md)** - Security practices and API key management\n- **[API.md](./API.md)** - API documentation and endpoint reference\n\n## 🚀 Quick Start\n\n### Development\n```bash\nnpm run dev\n```\n\n### Production Build\n```bash\nnpm run build\nnpm run start\n```\n\n### Testing Build\n```bash\nnpm run test:build\n```\n\n## 🏗️ Architecture Overview\n\nChewyAI is a full-stack application with:\n\n- **Frontend**: React + Vite + TypeScript + Tailwindcss\n- **Backend**: Express.js + Node.js\n- **Database**: Supabase (PostgreSQL)\n- **AI Integration**: OpenRouter API (user-configured)\n- **Deployment**: Replit (production-ready)\n\n## 🔐 Security\n\n- All API keys are handled ephemerally by the backend\n- No sensitive credentials are exposed to the client\n- Proper CORS and security headers implemented\n- Environment variables used for all configuration\n\n## 📝 Contributing\n\nPlease read [RULES.md](./RULES.md) before contributing to understand our development standards and practices.\n"}