{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}, "originalCode": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { storage } from \"./storage\";\nimport { body, validationResult } from \"express-validator\";\nimport { z } from \"zod\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  FlashcardDeck,\n  GenerateFlashcardsResponse,\n  Flashcard as SharedFlashcard, // Renaming to avoid conflict with local Flashcard if any\n} from \"@shared/types/flashcards\";\n\n// Updated Zod schema for flashcard generation request\n// Aligns with GenerateFlashcardsRequest from shared types and PRD's ephemeral key handling\nconst generateFlashcardsApiRequestSchema = z.object({\n  textContent: z.string().min(1, \"textContent is required\"),\n  documentId: z.string().min(1, \"documentId is required\"),\n  deckTitle: z.string().optional(),\n  count: z.number().int().min(1).max(50).optional().default(10), // Kept count from existing logic\n  customPrompt: z.string().optional(), // New: custom prompt for AI generation\n  // aiSettings are provided by the client per PRD US002\n  // The backend uses these ephemerally\n  aiSettings: z.object({\n    provider: z.string().min(1, \"AI provider is required\"), // e.g., \"OpenRouter\"\n    baseUrl: z.string().url(\"Valid Base URL for AI provider is required\"),\n    apiKey: z.string().min(1, \"AI API Key is required\"), // User's API key\n    model: z.string().min(1, \"AI Model ID is required\"), // e.g., \"google/gemini-2.5-pro-preview\"\n  }),\n});\n\n// Type for flashcard generation request, inferred from Zod schema\ntype GenerateFlashcardsApiRequest = z.infer<\n  typeof generateFlashcardsApiRequestSchema\n>;\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  console.log(\"🔧 Registering main routes...\");\n\n  // Health check endpoint - needs /api prefix since it's not mounted under a router\n  app.get(\"/api/health\", (req, res) => {\n    console.log(\"💚 Health check endpoint hit\");\n    res.json({\n      status: \"ok\",\n      timestamp: new Date().toISOString(),\n      message: \"Server is running properly\",\n    });\n  });\n\n  // Debug endpoint to list all registered routes\n  app.get(\"/api/debug/routes\", (req, res) => {\n    const routes: any[] = [];\n\n    // Helper function to extract routes from Express app\n    function extractRoutes(stack: any[], basePath = \"\") {\n      stack.forEach((layer) => {\n        if (layer.route) {\n          // Regular route\n          const methods = Object.keys(layer.route.methods);\n          routes.push({\n            path: basePath + layer.route.path,\n            methods: methods,\n            type: \"route\",\n          });\n        } else if (layer.name === \"router\") {\n          // Router middleware\n          const routerBasePath = layer.regexp.source\n            .replace(\"^\\\\\", \"\")\n            .replace(\"\\\\/?(?=\\\\/|$)\", \"\")\n            .replace(/\\\\\\//g, \"/\");\n          extractRoutes(layer.handle.stack, routerBasePath);\n        }\n      });\n    }\n\n    try {\n      extractRoutes(app._router?.stack || []);\n      res.json({\n        message: \"Debug route listing\",\n        routes: routes,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error: any) {\n      res.status(500).json({\n        error: \"Failed to extract routes\",\n        message: error.message,\n        timestamp: new Date().toISOString(),\n      });\n    }\n  });\n\n  // Environment debug endpoint\n  app.get(\"/api/debug/env\", (req, res) => {\n    res.json({\n      message: \"Environment debug info\",\n      nodeEnv: process.env.NODE_ENV,\n      hasViteDbPassword: !!process.env.VITE_DATABASE_PASSWORD,\n      timestamp: new Date().toISOString(),\n    });\n  });\n\n  console.log(\"✅ Main routes registered successfully\");\n  const httpServer = createServer(app);\n  return httpServer;\n}\n", "modifiedCode": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { storage } from \"./storage\";\nimport { body, validationResult } from \"express-validator\";\nimport { z } from \"zod\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  FlashcardDeck,\n  GenerateFlashcardsResponse,\n  Flashcard as SharedFlashcard, // Renaming to avoid conflict with local Flashcard if any\n} from \"@shared/types/flashcards\";\n\n// Updated Zod schema for flashcard generation request\n// AI credentials are now retrieved from secure backend storage\nconst generateFlashcardsApiRequestSchema = z.object({\n  textContent: z.string().min(1, \"textContent is required\"),\n  documentId: z.string().min(1, \"documentId is required\"),\n  deckTitle: z.string().optional(),\n  count: z.number().int().min(1).max(50).optional().default(10), // Kept count from existing logic\n  customPrompt: z.string().optional(), // New: custom prompt for AI generation\n  // aiSettings removed - credentials are retrieved from secure backend storage\n});\n\n// Type for flashcard generation request, inferred from Zod schema\ntype GenerateFlashcardsApiRequest = z.infer<\n  typeof generateFlashcardsApiRequestSchema\n>;\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  console.log(\"🔧 Registering main routes...\");\n\n  // Health check endpoint - needs /api prefix since it's not mounted under a router\n  app.get(\"/api/health\", (req, res) => {\n    console.log(\"💚 Health check endpoint hit\");\n    res.json({\n      status: \"ok\",\n      timestamp: new Date().toISOString(),\n      message: \"Server is running properly\",\n    });\n  });\n\n  // Debug endpoint to list all registered routes\n  app.get(\"/api/debug/routes\", (req, res) => {\n    const routes: any[] = [];\n\n    // Helper function to extract routes from Express app\n    function extractRoutes(stack: any[], basePath = \"\") {\n      stack.forEach((layer) => {\n        if (layer.route) {\n          // Regular route\n          const methods = Object.keys(layer.route.methods);\n          routes.push({\n            path: basePath + layer.route.path,\n            methods: methods,\n            type: \"route\",\n          });\n        } else if (layer.name === \"router\") {\n          // Router middleware\n          const routerBasePath = layer.regexp.source\n            .replace(\"^\\\\\", \"\")\n            .replace(\"\\\\/?(?=\\\\/|$)\", \"\")\n            .replace(/\\\\\\//g, \"/\");\n          extractRoutes(layer.handle.stack, routerBasePath);\n        }\n      });\n    }\n\n    try {\n      extractRoutes(app._router?.stack || []);\n      res.json({\n        message: \"Debug route listing\",\n        routes: routes,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error: any) {\n      res.status(500).json({\n        error: \"Failed to extract routes\",\n        message: error.message,\n        timestamp: new Date().toISOString(),\n      });\n    }\n  });\n\n  // Environment debug endpoint\n  app.get(\"/api/debug/env\", (req, res) => {\n    res.json({\n      message: \"Environment debug info\",\n      nodeEnv: process.env.NODE_ENV,\n      hasViteDbPassword: !!process.env.VITE_DATABASE_PASSWORD,\n      timestamp: new Date().toISOString(),\n    });\n  });\n\n  console.log(\"✅ Main routes registered successfully\");\n  const httpServer = createServer(app);\n  return httpServer;\n}\n"}