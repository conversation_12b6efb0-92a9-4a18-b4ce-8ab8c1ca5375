{"path": {"rootPath": "/home/<USER>/workspace", "relPath": ".env.example"}, "originalCode": "# Client Environment Variables (VITE_ prefix for Vite)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# Server Environment Variables\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\nVITE_DATABASE_PASSWORD=\n\n# AI Provider Default Configuration (Optional - User will configure their own)\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro\n\n# Security\nJWT_SECRET=your-jwt-secret-change-this-in-production\nSESSION_SECRET=your-session-secret-change-this-in-production\n\n# CORS Configuration\nFRONTEND_URL=http://localhost:3000\n\n# Database Configuration (Optional - for SQLite local storage)\nDATABASE_URL=./data/chewyai.sqlite ", "modifiedCode": "# Client Environment Variables (VITE_ prefix for Vite)\nVITE_SUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nVITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.BKJfxPCJFHI-i_m7VE-JqVoUDVNXx2tE0qSDI4JJT2g\nVITE_API_BASE_URL=http://localhost:5000/api\n\n# Server Environment Variables\nNODE_ENV=development\nPORT=5000\n\n# Supabase Configuration (Server)\nSUPABASE_URL=https://hrdjfukhzbzksqaupqie.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0\nVITE_DATABASE_PASSWORD=\n\n# AI Provider Default Configuration (Optional - User will configure their own)\nDEFAULT_AI_PROVIDER=openrouter\nDEFAULT_AI_BASE_URL=https://openrouter.ai/api/v1\nDEFAULT_EXTRACTION_MODEL=google/gemini-2.5-flash\nDEFAULT_GENERATION_MODEL=google/gemini-2.5-pro\n\n# Security\nJWT_SECRET=your-jwt-secret-change-this-in-production\nSESSION_SECRET=your-session-secret-change-this-in-production\n\n# CORS Configuration\nFRONTEND_URL=http://localhost:3000\n\n# Database Configuration (Optional - for SQLite local storage)\nDATABASE_URL=./data/chewyai.sqlite "}