{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/DEPLOYMENT.md"}, "modifiedCode": "# ChewyAI Deployment Guide\n\n## 🚀 Production Deployment on Replit\n\n### Prerequisites\n- Replit account with deployment capabilities\n- Supabase project configured\n- Environment variables prepared\n\n### Deployment Steps\n\n#### 1. Environment Configuration\nSet the following environment variables in Replit Secrets:\n\n```bash\n# Required for Production\nSUPABASE_URL=https://your-project.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=your-service-role-key\nVITE_SUPABASE_URL=https://your-project.supabase.co\nVITE_SUPABASE_ANON_KEY=your-anon-key\nNODE_ENV=production\nPORT=80\n```\n\n#### 2. Build Process\nThe deployment uses the following build command:\n```bash\nnpm run build\n```\n\nThis will:\n1. Clean the `dist` directory\n2. Build the React frontend with production optimizations\n3. Bundle the Express.js server with minification\n\n#### 3. Production Server\nThe production server runs:\n```bash\nNODE_ENV=production node dist/index.js\n```\n\n### Build Configuration\n\n#### Frontend Build (Vite)\n- **Output**: `dist/public/`\n- **Optimizations**: \n  - Code splitting for better caching\n  - Asset hashing for cache busting\n  - Minification with esbuild\n  - Source maps disabled in production\n\n#### Backend Build (esbuild)\n- **Output**: `dist/index.js`\n- **Optimizations**:\n  - Bundle all dependencies\n  - Minification enabled\n  - External packages for Node.js modules\n\n## 🔧 Local Development\n\n### Setup\n```bash\n# Install dependencies\nnpm install\n\n# Copy environment template\ncp .env.example .env\n\n# Configure your environment variables in .env\n\n# Start development servers\nnpm run dev\n```\n\n### Development Scripts\n- `npm run dev` - Start both frontend and backend in development mode\n- `npm run dev:client` - Start only the Vite dev server (port 3000)\n- `npm run dev:server` - Start only the Express server (port 5000)\n\n## 🏗️ Build Process Details\n\n### Production Build Steps\n1. **Clean**: Remove existing `dist` directory\n2. **Client Build**: \n   - Vite builds React app to `dist/public/`\n   - Optimizes assets and creates manifest\n   - Generates hashed filenames for caching\n3. **Server Build**:\n   - esbuild bundles server code to `dist/index.js`\n   - Minifies and optimizes for production\n\n### Build Verification\n```bash\n# Test the build process\nnpm run test:build\n\n# Preview production build locally\nnpm run preview\n```\n\n## 🌐 Static File Serving\n\n### Production Configuration\n- **Static Files**: Served from `dist/public/` with optimized caching\n- **SPA Routing**: All non-API routes serve `index.html`\n- **Cache Headers**: \n  - HTML files: No cache\n  - Assets (JS/CSS): 1 year cache with immutable flag\n- **Security Headers**: CSP, XSS protection, frame options\n\n### API Routes\nAll API endpoints are prefixed with `/api` and handled by Express:\n- `/api/health` - Health check endpoint\n- `/api/flashcards/*` - Flashcard management\n- `/api/quizzes/*` - Quiz functionality\n- `/api/documents/*` - Document processing\n- `/api/ai/*` - AI integration endpoints\n\n## 🔍 Monitoring & Health Checks\n\n### Health Check Endpoint\n- **URL**: `/api/health`\n- **Method**: GET\n- **Response**: JSON with status and timestamp\n- **Use**: Replit deployment monitoring\n\n### Logging\n- Request/response logging for API endpoints\n- Error logging with appropriate detail levels\n- No sensitive data in logs (API keys, tokens)\n\n## 🚨 Troubleshooting\n\n### Common Issues\n\n#### Build Failures\n- **Missing Dependencies**: Run `npm install`\n- **TypeScript Errors**: Run `npm run check`\n- **Environment Variables**: Verify all required vars are set\n\n#### Runtime Issues\n- **Static Files Not Found**: Ensure build completed successfully\n- **API Errors**: Check server logs and environment configuration\n- **Authentication Issues**: Verify Supabase configuration\n\n#### Performance Issues\n- **Slow Loading**: Check bundle sizes and optimize imports\n- **Memory Issues**: Monitor server resource usage\n- **Cache Issues**: Verify cache headers and asset hashing\n\n### Debug Commands\n```bash\n# Check TypeScript\nnpm run check\n\n# Test build process\nnpm run test:build\n\n# View build output\nls -la dist/\nls -la dist/public/\n\n# Check environment\nnpm run dev:server\n# Then visit http://localhost:5000/api/debug/env\n```\n\n## 📊 Performance Optimization\n\n### Frontend Optimizations\n- Code splitting by route and vendor libraries\n- Asset optimization and compression\n- Lazy loading for non-critical components\n- Optimized bundle sizes with tree shaking\n\n### Backend Optimizations\n- Minified server bundle\n- Efficient static file serving\n- Proper caching headers\n- Gzip compression (handled by Replit)\n\n### Database Optimizations\n- Supabase connection pooling\n- Efficient queries with proper indexing\n- Row Level Security for data access control\n\n## 🔄 Deployment Updates\n\n### Rolling Updates\n1. Update code in Replit\n2. Replit automatically triggers build\n3. Health check validates deployment\n4. Traffic switches to new version\n\n### Rollback Procedure\n1. Revert code changes in Replit\n2. Redeploy previous version\n3. Verify health check passes\n4. Monitor application functionality\n"}