{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/AiFlashcardGenerator.tsx"}, "originalCode": "import React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { Terminal, Sparkles } from \"lucide-react\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { supabase } from \"@/lib/supabaseClient\";\nimport { Tables } from \"@/types/supabase\";\nimport { generateFlashcardsAPI } from \"@/lib/api\";\nimport { getAIProviderSettings, isAIProviderConfigured } from \"@/lib/ai-provider\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface AiFlashcardGeneratorProps {\n  selectedDeckId: string;\n  selectedDeckName: string;\n  onGenerationComplete: () => void;\n}\n\nconst AiFlashcardGenerator: React.FC<AiFlashcardGeneratorProps> = ({\n  selectedDeckId,\n  selectedDeckName,\n  onGenerationComplete,\n}) => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  \n  // Document selection states\n  const [availableDocuments, setAvailableDocuments] = useState<Tables<\"study_documents\">[]>([]);\n  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);\n  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);\n  \n  // Generation options\n  const [numberOfCards, setNumberOfCards] = useState<number>(10);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n  \n  // UI states\n  const [isGenerating, setIsGenerating] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);\n\n  // Check AI configuration on mount\n  useEffect(() => {\n    const checkAIConfig = async () => {\n      const configured = await isAIProviderConfigured();\n      setIsAIConfigured(configured);\n    };\n    checkAIConfig();\n  }, []);\n\n  // Load documents on mount\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      if (user) {\n        setLoadingDocuments(true);\n        try {\n          const { data, error: dbError } = await supabase\n            .from(\"study_documents\")\n            .select(\"*\")\n            .eq(\"user_id\", user.id)\n            .eq(\"status\", \"extracted\")\n            .order(\"created_at\", { ascending: false });\n\n          if (dbError) throw dbError;\n          setAvailableDocuments(data || []);\n        } catch (err: any) {\n          console.error(\"Error fetching documents:\", err);\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI generation.\",\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingDocuments(false);\n        }\n      }\n    };\n    fetchDocuments();\n  }, [user, toast]);\n\n  const handleDocumentSelection = (documentId: string) => {\n    setSelectedDocumentIds((prev) =>\n      prev.includes(documentId)\n        ? prev.filter((id) => id !== documentId)\n        : [...prev, documentId]\n    );\n  };\n\n  const handleGenerate = async () => {\n    const isConfigured = await isAIProviderConfigured();\n    if (!isConfigured) {\n      setError(\"AI Provider not configured. Please configure your AI settings first.\");\n      return;\n    }\n\n    if (selectedDocumentIds.length === 0) {\n      setError(\"Please select at least one document.\");\n      return;\n    }\n\n    if (numberOfCards < 1 || numberOfCards > 50) {\n      setError(\"Number of cards must be between 1 and 50.\");\n      return;\n    }\n\n    setIsGenerating(true);\n    setError(null);\n\n    try {\n      const aiSettings = getAIProviderSettings();\n      const { data: { session } } = await supabase.auth.getSession();\n\n      if (!session?.access_token) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      // Fetch document contents\n      const documentContents: string[] = [];\n      for (const docId of selectedDocumentIds) {\n        try {\n          const response = await fetch(`/api/documents/${docId}/content`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${session.access_token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(`Failed to fetch document ${docId}: HTTP ${response.status}`);\n          }\n\n          const textContent = await response.text();\n          if (textContent.trim()) {\n            documentContents.push(textContent);\n          }\n        } catch (err) {\n          console.error(`Error fetching document ${docId}:`, err);\n          throw new Error(`Failed to fetch document ${docId}: ${err instanceof Error ? err.message : 'Unknown error'}`);\n        }\n      }\n\n      if (documentContents.length === 0) {\n        throw new Error(\"No valid document content found.\");\n      }\n\n      // Combine all document contents\n      const combinedContent = documentContents.join(\"\\n\\n--- Document Separator ---\\n\\n\");\n\n      // API payload\n      const payload = {\n        textContent: combinedContent,\n        documentId: selectedDocumentIds.join(\",\"),\n        deckTitle: `Additional cards for ${selectedDeckName}`,\n        count: numberOfCards,\n        customPrompt: customPrompt || undefined,\n        // aiSettings removed - credentials are retrieved from secure backend storage\n      };\n\n      console.log(\"📤 Generating additional flashcards:\", payload);\n      const response = await generateFlashcardsAPI(payload);\n      console.log(\"📥 Received flashcard generation response:\", response);\n\n      if (!response) {\n        throw new Error(\"No response received from flashcard generation API\");\n      }\n\n      // Process the response and save flashcards\n      const deckData = response.deck || response;\n      const flashcardsData = deckData.flashcards || [];\n\n      if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {\n        // Prepare flashcards for batch insert\n        const flashcardsToInsert = flashcardsData.map((fc: any) => ({\n          set_id: selectedDeckId,\n          user_id: user!.id,\n          front_text: fc.question || fc.front_text || \"No question\",\n          back_text: fc.answer || fc.back_text || \"No answer\",\n        }));\n\n        // Insert flashcards into Supabase\n        const { error: insertError } = await supabase\n          .from(\"flashcards\")\n          .insert(flashcardsToInsert);\n\n        if (insertError) {\n          throw new Error(`Failed to save flashcards: ${insertError.message}`);\n        }\n\n        toast({\n          title: \"Success!\",\n          description: `Generated ${flashcardsData.length} new flashcards for \"${selectedDeckName}\".`,\n        });\n\n        // Clear selections and notify parent\n        setSelectedDocumentIds([]);\n        setCustomPrompt(\"\");\n        setNumberOfCards(10);\n        onGenerationComplete();\n      } else {\n        throw new Error(\"No flashcards were generated.\");\n      }\n    } catch (e) {\n      console.error(\"Error generating flashcards:\", e);\n      setError(\n        e instanceof Error ? e.message : \"Failed to generate flashcards.\"\n      );\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center space-x-2\">\n        <Sparkles className=\"h-5 w-5 text-purple-400\" />\n        <h3 className=\"text-lg font-medium text-slate-100\">\n          Generate More Flashcards with AI\n        </h3>\n      </div>\n\n      {/* Number of Cards */}\n      <div>\n        <Label htmlFor=\"number-of-cards\" className=\"text-slate-300\">\n          Number of Flashcards*\n        </Label>\n        <Input\n          id=\"number-of-cards\"\n          type=\"number\"\n          min=\"1\"\n          max=\"50\"\n          value={numberOfCards}\n          onChange={(e) => setNumberOfCards(parseInt(e.target.value) || 10)}\n          placeholder=\"10\"\n          disabled={isGenerating}\n          className=\"mt-1 w-32 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500\"\n        />\n        <p className=\"text-xs text-slate-400 mt-1\">\n          Specify exactly how many flashcards to generate (1-50)\n        </p>\n      </div>\n\n      {/* Document Selection */}\n      <div>\n        <Label className=\"text-slate-300 mb-2 block\">\n          Select Documents for AI Generation*\n        </Label>\n        {loadingDocuments ? (\n          <div className=\"flex justify-center items-center py-4\">\n            <Spinner size=\"sm\" />\n            <span className=\"ml-2 text-purple-300\">Loading documents...</span>\n          </div>\n        ) : availableDocuments.length === 0 ? (\n          <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n            No extracted documents available. Please upload and process documents first.\n          </p>\n        ) : (\n          <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n            {availableDocuments.map((doc) => (\n              <div\n                key={doc.id}\n                className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n              >\n                <Checkbox\n                  id={`doc-${doc.id}`}\n                  checked={selectedDocumentIds.includes(doc.id)}\n                  onCheckedChange={() => handleDocumentSelection(doc.id)}\n                  disabled={isGenerating}\n                />\n                <Label\n                  htmlFor={`doc-${doc.id}`}\n                  className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                  title={doc.file_name}\n                >\n                  {doc.file_name}\n                </Label>\n              </div>\n            ))}\n          </div>\n        )}\n        {selectedDocumentIds.length > 0 && (\n          <p className=\"text-xs text-purple-400 mt-1\">\n            {selectedDocumentIds.length} document(s) selected.\n          </p>\n        )}\n      </div>\n\n      {/* Custom Prompt */}\n      <div>\n        <Label htmlFor=\"custom-prompt\" className=\"text-slate-300\">\n          Custom Prompt (Optional)\n        </Label>\n        <Textarea\n          id=\"custom-prompt\"\n          value={customPrompt}\n          onChange={(e) => setCustomPrompt(e.target.value)}\n          placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n          rows={3}\n          className=\"mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n          disabled={isGenerating}\n        />\n        <p className=\"text-xs text-purple-400 mt-1\">\n          Add specific instructions for the AI on what kind of flashcards you want.\n        </p>\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <Terminal className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <Button\n        onClick={handleGenerate}\n        disabled={\n          isGenerating ||\n          selectedDocumentIds.length === 0 ||\n          numberOfCards < 1 ||\n          numberOfCards > 50 ||\n          !isAIProviderConfigured()\n        }\n        className=\"w-full bg-purple-600 hover:bg-purple-700 text-white\"\n      >\n        {isGenerating ? (\n          <>\n            <Spinner size=\"sm\" className=\"mr-2\" />\n            Generating Flashcards...\n          </>\n        ) : (\n          <>\n            <Sparkles className=\"h-4 w-4 mr-2\" />\n            Generate Flashcards with AI\n          </>\n        )}\n      </Button>\n    </div>\n  );\n};\n\nexport default AiFlashcardGenerator;\n", "modifiedCode": "import React, { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\nimport { Terminal, Sparkles } from \"lucide-react\";\nimport { useAuth } from \"@/hooks/useAuth\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { supabase } from \"@/lib/supabaseClient\";\nimport { Tables } from \"@/types/supabase\";\nimport { generateFlashcardsAPI } from \"@/lib/api\";\nimport { getAIProviderSettings, isAIProviderConfigured } from \"@/lib/ai-provider\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ntype Flashcard = Tables<\"flashcards\">;\n\ninterface AiFlashcardGeneratorProps {\n  selectedDeckId: string;\n  selectedDeckName: string;\n  onGenerationComplete: () => void;\n}\n\nconst AiFlashcardGenerator: React.FC<AiFlashcardGeneratorProps> = ({\n  selectedDeckId,\n  selectedDeckName,\n  onGenerationComplete,\n}) => {\n  const { user } = useAuth();\n  const { toast } = useToast();\n  \n  // Document selection states\n  const [availableDocuments, setAvailableDocuments] = useState<Tables<\"study_documents\">[]>([]);\n  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);\n  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);\n  \n  // Generation options\n  const [numberOfCards, setNumberOfCards] = useState<number>(10);\n  const [customPrompt, setCustomPrompt] = useState<string>(\"\");\n  \n  // UI states\n  const [isGenerating, setIsGenerating] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [isAIConfigured, setIsAIConfigured] = useState<boolean>(false);\n\n  // Check AI configuration on mount\n  useEffect(() => {\n    const checkAIConfig = async () => {\n      const configured = await isAIProviderConfigured();\n      setIsAIConfigured(configured);\n    };\n    checkAIConfig();\n  }, []);\n\n  // Load documents on mount\n  useEffect(() => {\n    const fetchDocuments = async () => {\n      if (user) {\n        setLoadingDocuments(true);\n        try {\n          const { data, error: dbError } = await supabase\n            .from(\"study_documents\")\n            .select(\"*\")\n            .eq(\"user_id\", user.id)\n            .eq(\"status\", \"extracted\")\n            .order(\"created_at\", { ascending: false });\n\n          if (dbError) throw dbError;\n          setAvailableDocuments(data || []);\n        } catch (err: any) {\n          console.error(\"Error fetching documents:\", err);\n          toast({\n            title: \"Error\",\n            description: \"Could not load documents for AI generation.\",\n            variant: \"destructive\",\n          });\n        } finally {\n          setLoadingDocuments(false);\n        }\n      }\n    };\n    fetchDocuments();\n  }, [user, toast]);\n\n  const handleDocumentSelection = (documentId: string) => {\n    setSelectedDocumentIds((prev) =>\n      prev.includes(documentId)\n        ? prev.filter((id) => id !== documentId)\n        : [...prev, documentId]\n    );\n  };\n\n  const handleGenerate = async () => {\n    const isConfigured = await isAIProviderConfigured();\n    if (!isConfigured) {\n      setError(\"AI Provider not configured. Please configure your AI settings first.\");\n      return;\n    }\n\n    if (selectedDocumentIds.length === 0) {\n      setError(\"Please select at least one document.\");\n      return;\n    }\n\n    if (numberOfCards < 1 || numberOfCards > 50) {\n      setError(\"Number of cards must be between 1 and 50.\");\n      return;\n    }\n\n    setIsGenerating(true);\n    setError(null);\n\n    try {\n      const aiSettings = getAIProviderSettings();\n      const { data: { session } } = await supabase.auth.getSession();\n\n      if (!session?.access_token) {\n        throw new Error(\"No authentication session found. Please log in again.\");\n      }\n\n      // Fetch document contents\n      const documentContents: string[] = [];\n      for (const docId of selectedDocumentIds) {\n        try {\n          const response = await fetch(`/api/documents/${docId}/content`, {\n            method: \"GET\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n              Authorization: `Bearer ${session.access_token}`,\n            },\n          });\n\n          if (!response.ok) {\n            throw new Error(`Failed to fetch document ${docId}: HTTP ${response.status}`);\n          }\n\n          const textContent = await response.text();\n          if (textContent.trim()) {\n            documentContents.push(textContent);\n          }\n        } catch (err) {\n          console.error(`Error fetching document ${docId}:`, err);\n          throw new Error(`Failed to fetch document ${docId}: ${err instanceof Error ? err.message : 'Unknown error'}`);\n        }\n      }\n\n      if (documentContents.length === 0) {\n        throw new Error(\"No valid document content found.\");\n      }\n\n      // Combine all document contents\n      const combinedContent = documentContents.join(\"\\n\\n--- Document Separator ---\\n\\n\");\n\n      // API payload\n      const payload = {\n        textContent: combinedContent,\n        documentId: selectedDocumentIds.join(\",\"),\n        deckTitle: `Additional cards for ${selectedDeckName}`,\n        count: numberOfCards,\n        customPrompt: customPrompt || undefined,\n        // aiSettings removed - credentials are retrieved from secure backend storage\n      };\n\n      console.log(\"📤 Generating additional flashcards:\", payload);\n      const response = await generateFlashcardsAPI(payload);\n      console.log(\"📥 Received flashcard generation response:\", response);\n\n      if (!response) {\n        throw new Error(\"No response received from flashcard generation API\");\n      }\n\n      // Process the response and save flashcards\n      const deckData = response.deck || response;\n      const flashcardsData = deckData.flashcards || [];\n\n      if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {\n        // Prepare flashcards for batch insert\n        const flashcardsToInsert = flashcardsData.map((fc: any) => ({\n          set_id: selectedDeckId,\n          user_id: user!.id,\n          front_text: fc.question || fc.front_text || \"No question\",\n          back_text: fc.answer || fc.back_text || \"No answer\",\n        }));\n\n        // Insert flashcards into Supabase\n        const { error: insertError } = await supabase\n          .from(\"flashcards\")\n          .insert(flashcardsToInsert);\n\n        if (insertError) {\n          throw new Error(`Failed to save flashcards: ${insertError.message}`);\n        }\n\n        toast({\n          title: \"Success!\",\n          description: `Generated ${flashcardsData.length} new flashcards for \"${selectedDeckName}\".`,\n        });\n\n        // Clear selections and notify parent\n        setSelectedDocumentIds([]);\n        setCustomPrompt(\"\");\n        setNumberOfCards(10);\n        onGenerationComplete();\n      } else {\n        throw new Error(\"No flashcards were generated.\");\n      }\n    } catch (e) {\n      console.error(\"Error generating flashcards:\", e);\n      setError(\n        e instanceof Error ? e.message : \"Failed to generate flashcards.\"\n      );\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center space-x-2\">\n        <Sparkles className=\"h-5 w-5 text-purple-400\" />\n        <h3 className=\"text-lg font-medium text-slate-100\">\n          Generate More Flashcards with AI\n        </h3>\n      </div>\n\n      {/* Number of Cards */}\n      <div>\n        <Label htmlFor=\"number-of-cards\" className=\"text-slate-300\">\n          Number of Flashcards*\n        </Label>\n        <Input\n          id=\"number-of-cards\"\n          type=\"number\"\n          min=\"1\"\n          max=\"50\"\n          value={numberOfCards}\n          onChange={(e) => setNumberOfCards(parseInt(e.target.value) || 10)}\n          placeholder=\"10\"\n          disabled={isGenerating}\n          className=\"mt-1 w-32 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500\"\n        />\n        <p className=\"text-xs text-slate-400 mt-1\">\n          Specify exactly how many flashcards to generate (1-50)\n        </p>\n      </div>\n\n      {/* Document Selection */}\n      <div>\n        <Label className=\"text-slate-300 mb-2 block\">\n          Select Documents for AI Generation*\n        </Label>\n        {loadingDocuments ? (\n          <div className=\"flex justify-center items-center py-4\">\n            <Spinner size=\"sm\" />\n            <span className=\"ml-2 text-purple-300\">Loading documents...</span>\n          </div>\n        ) : availableDocuments.length === 0 ? (\n          <p className=\"text-sm text-purple-300 p-3 bg-slate-700 rounded-md\">\n            No extracted documents available. Please upload and process documents first.\n          </p>\n        ) : (\n          <div className=\"max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600\">\n            {availableDocuments.map((doc) => (\n              <div\n                key={doc.id}\n                className=\"flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors\"\n              >\n                <Checkbox\n                  id={`doc-${doc.id}`}\n                  checked={selectedDocumentIds.includes(doc.id)}\n                  onCheckedChange={() => handleDocumentSelection(doc.id)}\n                  disabled={isGenerating}\n                />\n                <Label\n                  htmlFor={`doc-${doc.id}`}\n                  className=\"font-normal text-purple-300 cursor-pointer flex-1 truncate\"\n                  title={doc.file_name}\n                >\n                  {doc.file_name}\n                </Label>\n              </div>\n            ))}\n          </div>\n        )}\n        {selectedDocumentIds.length > 0 && (\n          <p className=\"text-xs text-purple-400 mt-1\">\n            {selectedDocumentIds.length} document(s) selected.\n          </p>\n        )}\n      </div>\n\n      {/* Custom Prompt */}\n      <div>\n        <Label htmlFor=\"custom-prompt\" className=\"text-slate-300\">\n          Custom Prompt (Optional)\n        </Label>\n        <Textarea\n          id=\"custom-prompt\"\n          value={customPrompt}\n          onChange={(e) => setCustomPrompt(e.target.value)}\n          placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n          rows={3}\n          className=\"mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500\"\n          disabled={isGenerating}\n        />\n        <p className=\"text-xs text-purple-400 mt-1\">\n          Add specific instructions for the AI on what kind of flashcards you want.\n        </p>\n      </div>\n\n      {error && (\n        <Alert variant=\"destructive\">\n          <Terminal className=\"h-4 w-4\" />\n          <AlertDescription>{error}</AlertDescription>\n        </Alert>\n      )}\n\n      <Button\n        onClick={handleGenerate}\n        disabled={\n          isGenerating ||\n          selectedDocumentIds.length === 0 ||\n          numberOfCards < 1 ||\n          numberOfCards > 50 ||\n          !isAIConfigured\n        }\n        className=\"w-full bg-purple-600 hover:bg-purple-700 text-white\"\n      >\n        {isGenerating ? (\n          <>\n            <Spinner size=\"sm\" className=\"mr-2\" />\n            Generating Flashcards...\n          </>\n        ) : (\n          <>\n            <Sparkles className=\"h-4 w-4 mr-2\" />\n            Generate Flashcards with AI\n          </>\n        )}\n      </Button>\n    </div>\n  );\n};\n\nexport default AiFlashcardGenerator;\n"}