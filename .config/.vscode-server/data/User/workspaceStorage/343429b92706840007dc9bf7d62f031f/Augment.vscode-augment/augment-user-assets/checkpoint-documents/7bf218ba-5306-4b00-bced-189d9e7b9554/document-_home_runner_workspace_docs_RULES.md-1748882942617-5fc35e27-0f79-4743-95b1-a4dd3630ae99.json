{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/RULES.md"}, "modifiedCode": "# ChewyAI Development Rules\n\n## 🎯 Core Principles\n\n### 1. Security First\n- **Never expose API keys to the client-side**\n- All user-provided AI credentials are handled ephemerally by the backend\n- Use environment variables for all sensitive configuration\n- Implement proper authentication and authorization\n\n### 2. Production-Ready Architecture\n- Separate development and production configurations\n- Optimize builds for performance and caching\n- Implement proper error handling and logging\n- Use TypeScript for type safety\n\n### 3. API Design Standards\n- All backend API endpoints MUST use the `/api` prefix\n- Implement proper HTTP status codes and error responses\n- Use consistent request/response formats\n- Validate all inputs with Zod schemas\n\n## 🏗️ Code Organization\n\n### Frontend Structure\n```\nclient/src/\n├── components/     # Reusable UI components\n├── pages/         # Route components\n├── lib/           # Utilities and API clients\n├── hooks/         # Custom React hooks\n├── contexts/      # React contexts\n└── types/         # TypeScript type definitions\n```\n\n### Backend Structure\n```\nserver/\n├── routes/        # API route handlers\n├── middleware/    # Express middleware\n├── db/           # Database configuration\n└── config.ts     # Environment configuration\n```\n\n## 🔐 Security Rules\n\n### Environment Variables\n- Use `VITE_` prefix for client-side environment variables\n- Never include fallback values for sensitive data in production\n- Validate required environment variables on startup\n\n### API Key Management\n- User AI provider credentials are handled in-memory only\n- No logging of sensitive credentials\n- Backend-owned credentials managed via environment variables\n\n### Authentication\n- Use Supabase Auth for user authentication\n- Implement proper JWT token validation\n- Include authorization checks on all protected routes\n\n## 📦 Package Management\n\n### Dependencies\n- Use npm for package management\n- Keep dependencies up to date\n- Separate dev and production dependencies\n- Use exact versions for critical packages\n\n### Build Process\n- Clean dist folder before each build\n- Optimize client bundle for production\n- Minify server code for production\n- Generate source maps for development only\n\n## 🚀 Deployment Standards\n\n### Production Build\n- Set `NODE_ENV=production`\n- Use optimized Vite build configuration\n- Implement proper caching headers\n- Include security headers\n\n### Replit Configuration\n- Use autoscale deployment target\n- Configure proper health checks\n- Set environment variables in deployment config\n- Use compiled JavaScript (not TypeScript) for production\n\n## 🧪 Testing Guidelines\n\n### Code Quality\n- Run TypeScript checks before deployment\n- Test build process before production deployment\n- Validate environment variable configuration\n- Check API endpoint functionality\n\n### Performance\n- Monitor bundle sizes\n- Optimize chunk splitting\n- Implement proper caching strategies\n- Use CDN for static assets when possible\n\n## 📝 Documentation Standards\n\n### Code Documentation\n- Document all public APIs\n- Include JSDoc comments for complex functions\n- Maintain up-to-date README files\n- Document environment variable requirements\n\n### Architecture Documentation\n- Keep MEMORIES.md updated with important decisions\n- Document security practices in SECURITY.md\n- Maintain deployment procedures in DEPLOYMENT.md\n- Update API documentation when endpoints change\n"}