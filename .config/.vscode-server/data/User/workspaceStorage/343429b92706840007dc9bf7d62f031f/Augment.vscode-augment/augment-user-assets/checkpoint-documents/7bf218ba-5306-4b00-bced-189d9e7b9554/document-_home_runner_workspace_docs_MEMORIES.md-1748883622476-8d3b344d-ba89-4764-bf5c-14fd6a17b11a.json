{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/MEMORIES.md"}, "originalCode": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement ephemeral API key handling for user AI provider credentials\n**Rationale**: \n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security by not storing user credentials\n- Compliance with data protection principles\n\n**Implementation**:\n- User AI credentials sent to backend per request\n- In-memory only processing, no persistent storage\n- No logging of sensitive credentials\n- Backend validates and forwards to AI provider\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS asse ts: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n", "modifiedCode": "# ChewyAI System Memories\n\n## 🧠 Important Architectural Decisions\n\n### Security Architecture (2025-06-02)\n**Decision**: Implement encrypted database storage for user AI provider credentials\n**Rationale**:\n- Users maintain control over their AI usage and costs\n- No liability for ChewyAI regarding AI provider billing\n- Enhanced security with AES-256-GCM encryption\n- Compliance with data protection principles\n- Better user experience (no need to re-enter credentials)\n\n**Implementation**:\n- User AI credentials encrypted with AES-256-GCM before database storage\n- Row Level Security (RLS) policies enforce user data isolation\n- API keys retrieved and decrypted only during AI API calls (ephemeral use)\n- No logging of sensitive credentials or headers\n- Client-side localStorage only stores non-sensitive configuration\n\n**Security Audit Results (2025-06-02)**:\n- Fixed critical hardcoded credentials vulnerability\n- Eliminated client-side API key exposure risks\n- Implemented secure encrypted credential storage\n- Enhanced authentication and error handling\n\n### Full-Stack Architecture (2025-06-02)\n**Decision**: Single-server deployment with Express.js serving both API and static files\n**Rationale**:\n- Simplified deployment on Replit\n- Reduced complexity compared to separate frontend/backend deployments\n- Better performance with single-origin requests\n- Easier CORS management\n\n**Implementation**:\n- Vite builds React app to `dist/public/`\n- Express serves static files with proper caching\n- API routes prefixed with `/api` to avoid conflicts\n- SPA fallback routing for client-side navigation\n\n### Database Strategy (2025-06-02)\n**Decision**: Use Supabase as primary database with Row Level Security\n**Rationale**:\n- Built-in authentication and authorization\n- Real-time capabilities for future features\n- Managed PostgreSQL with good performance\n- Row Level Security for data isolation\n\n**Implementation**:\n- Supabase client for frontend authentication\n- Service role key for backend operations\n- RLS policies enforce user data access\n- JWT tokens for API authentication\n\n### Build System (2025-06-02)\n**Decision**: Vite for frontend, esbuild for backend bundling\n**Rationale**:\n- Fast development builds with Vite\n- Optimized production builds with code splitting\n- Single JavaScript bundle for easy deployment\n- TypeScript support throughout\n\n**Implementation**:\n- Vite handles React app with optimizations\n- esbuild bundles server code with minification\n- Separate build steps for frontend and backend\n- Production-ready asset optimization\n\n## 🔧 Technical Implementation Details\n\n### API Route Organization\n**Current Structure**:\n- `/api/health` - Health checks and monitoring\n- `/api/flashcards/*` - Flashcard CRUD operations\n- `/api/quizzes/*` - Quiz management and generation\n- `/api/documents/*` - Document processing and storage\n- `/api/ai/*` - AI integration endpoints\n\n**Design Principles**:\n- RESTful API design where applicable\n- Consistent error response format\n- Proper HTTP status codes\n- Input validation with Zod schemas\n\n### Environment Variable Strategy\n**Development vs Production**:\n- Development: Fallback values for quick setup\n- Production: Required environment variables with validation\n- Client variables prefixed with `VITE_`\n- Server variables without prefix\n\n**Security Considerations**:\n- No sensitive fallbacks in production\n- Startup validation for required variables\n- Clear separation of client/server configuration\n\n### Static File Serving Strategy\n**Caching Strategy**:\n- HTML files: No cache (always fresh)\n- JS/CSS asse ts: 1 year cache with immutable flag\n- Images and fonts: Standard caching\n- API responses: No cache by default\n\n**Security Headers**:\n- Content Security Policy for XSS protection\n- Frame options to prevent clickjacking\n- Content type sniffing prevention\n- Referrer policy for privacy\n\n## 🚀 Deployment Decisions\n\n### Replit Configuration\n**Deployment Target**: Autoscale\n**Rationale**: \n- Automatic scaling based on traffic\n- Cost-effective for variable usage\n- Built-in load balancing\n- Easy environment management\n\n**Build Process**:\n- Single build command: `npm run build`\n- Health check endpoint: `/api/health`\n- Environment variables in deployment config\n- Automatic HTTPS and domain management\n\n### Production Optimizations\n**Frontend Optimizations**:\n- Code splitting by vendor and route\n- Asset hashing for cache busting\n- Tree shaking for smaller bundles\n- Lazy loading for non-critical components\n\n**Backend Optimizations**:\n- Minified server bundle\n- External package handling\n- Efficient static file serving\n- Proper error handling without stack traces\n\n## 📝 Development Workflow Decisions\n\n### Package Management\n**Decision**: Use npm as primary package manager\n**Rationale**:\n- Consistent with Node.js ecosystem\n- Good lock file support\n- Wide compatibility\n- Replit native support\n\n### TypeScript Configuration\n**Decision**: Strict TypeScript throughout the application\n**Rationale**:\n- Better developer experience\n- Catch errors at compile time\n- Improved code documentation\n- Better IDE support\n\n### Testing Strategy\n**Current Approach**: Build verification and manual testing\n**Future Considerations**:\n- Unit tests for critical business logic\n- Integration tests for API endpoints\n- End-to-end tests for user workflows\n- Performance testing for production loads\n\n## 🔄 Evolution and Future Considerations\n\n### Scalability Considerations\n- Database connection pooling for high traffic\n- CDN integration for static assets\n- Caching layer for frequently accessed data\n- Microservices migration if needed\n\n### Security Enhancements\n- Rate limiting for API endpoints\n- Advanced monitoring and alerting\n- Automated security scanning\n- Regular dependency updates\n\n### Feature Expansion\n- Real-time collaboration features\n- Advanced AI model support\n- Mobile application development\n- Offline functionality\n\n## 📊 Performance Benchmarks\n\n### Current Performance Targets\n- Page load time: < 2 seconds\n- API response time: < 500ms\n- Build time: < 2 minutes\n- Bundle size: < 1MB (gzipped)\n\n### Monitoring Metrics\n- Health check response time\n- Error rates by endpoint\n- User authentication success rate\n- AI provider integration reliability\n"}