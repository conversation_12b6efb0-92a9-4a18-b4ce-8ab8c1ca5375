{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/ai-provider.ts"}, "originalCode": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n * This function checks if credentials are stored in the backend\n */\nexport async function isAIProviderConfigured(): Promise<boolean> {\n  try {\n    const settings = getAIProviderSettings();\n\n    // Import the API function dynamically to avoid circular dependencies\n    const { getCredentialsAPI } = await import('./api');\n\n    // Check if credentials exist in the backend for the current provider\n    const credentialsResult = await getCredentialsAPI(settings.provider);\n\n    return credentialsResult.success && credentialsResult.hasCredentials;\n  } catch (error) {\n    console.error('Error checking AI provider configuration:', error);\n    return false;\n  }\n}\n\n/**\n * Synchronous version that checks localStorage only (for backward compatibility)\n * This should be used sparingly and eventually replaced with the async version\n */\nexport function isAIProviderConfiguredSync(): boolean {\n  const settings = getAIProviderSettings();\n  // Check if we have basic configuration (provider, models, etc.)\n  return Boolean(\n    settings.provider &&\n    settings.extractionModel &&\n    settings.generationModel &&\n    settings.baseUrl\n  );\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 2.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 2.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n", "modifiedCode": "import { AIProviderSettings } from \"@/types\";\n\n/**\n * Set AI provider settings (excluding API key which is stored securely on server)\n */\nexport function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {\n  // Store only non-sensitive configuration in localStorage\n  const safeSettings = {\n    provider: settings.provider,\n    baseUrl: settings.baseUrl,\n    extractionModel: settings.extractionModel,\n    generationModel: settings.generationModel,\n    model: settings.generationModel, // Keep model field aligned with generationModel\n    // Never store apiKey in localStorage\n  };\n  localStorage.setItem(\"aiProviderSettings\", JSON.stringify(safeSettings));\n}\n\n/**\n * Get AI provider settings from localStorage\n */\nexport function getAIProviderSettings(): AIProviderSettings {\n  const systemDefaultProvider = \"OpenRouter\";\n  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);\n  const systemDefaultModels = getAvailableModels(systemDefaultProvider);\n\n  const defaultExtractionModelId = \"google/gemini-2.5-flash-preview-05-20\";\n  const defaultGenerationModelId = \"google/gemini-2.5-pro-preview\";\n\n  const systemDefaultExtractionModel =\n    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n  const systemDefaultGenerationModel =\n    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||\n    systemDefaultModels[0]?.id ||\n    \"\";\n\n  const systemDefaults: AIProviderSettings = {\n    provider: systemDefaultProvider,\n    baseUrl: systemDefaultBaseUrl,\n    apiKey: \"\", // Never use environment variables for API keys on client-side\n    extractionModel: systemDefaultExtractionModel,\n    generationModel: systemDefaultGenerationModel,\n    model: systemDefaultGenerationModel, // Legacy field, matches generationModel\n  };\n\n  const storedSettings = localStorage.getItem(\"aiProviderSettings\");\n  if (!storedSettings) {\n    return systemDefaults;\n  }\n\n  try {\n    const parsedSettings = JSON.parse(\n      storedSettings\n    ) as Partial<AIProviderSettings>;\n\n    const currentProvider = parsedSettings.provider || systemDefaults.provider;\n    const modelsForCurrentProvider = getAvailableModels(currentProvider);\n\n    let finalExtractionModel = parsedSettings.extractionModel;\n    if (\n      !finalExtractionModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)\n    ) {\n      finalExtractionModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.extractionModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    let finalGenerationModel = parsedSettings.generationModel;\n    if (\n      !finalGenerationModel ||\n      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)\n    ) {\n      finalGenerationModel =\n        modelsForCurrentProvider.find(\n          (m) => m.id === systemDefaults.generationModel\n        )?.id ||\n        modelsForCurrentProvider[0]?.id ||\n        \"\";\n    }\n\n    return {\n      provider: currentProvider,\n      baseUrl:\n        parsedSettings.baseUrl ||\n        getDefaultBaseUrl(currentProvider) ||\n        systemDefaults.baseUrl,\n      apiKey: \"\", // API keys are never stored in localStorage - retrieved from server when needed\n      extractionModel: finalExtractionModel,\n      generationModel: finalGenerationModel,\n      model: finalGenerationModel, // Legacy field always matches generationModel\n    };\n  } catch (e) {\n    // console.error(\"Failed to parse AI provider settings, returning system defaults:\", e);\n    return systemDefaults;\n  }\n}\n\n/**\n * Check if AI provider settings are configured\n * This function checks if credentials are stored in the backend\n */\nexport async function isAIProviderConfigured(): Promise<boolean> {\n  try {\n    const settings = getAIProviderSettings();\n\n    // Import the API function dynamically to avoid circular dependencies\n    const { getCredentialsAPI } = await import('./api');\n\n    // Check if credentials exist in the backend for the current provider\n    const credentialsResult = await getCredentialsAPI(settings.provider);\n\n    return credentialsResult.success && credentialsResult.hasCredentials;\n  } catch (error) {\n    console.error('Error checking AI provider configuration:', error);\n    return false;\n  }\n}\n\n/**\n * Synchronous version that checks localStorage only (for backward compatibility)\n * This should be used sparingly and eventually replaced with the async version\n */\nexport function isAIProviderConfiguredSync(): boolean {\n  const settings = getAIProviderSettings();\n  // Check if we have basic configuration (provider, models, etc.)\n  return Boolean(\n    settings.provider &&\n    settings.extractionModel &&\n    settings.generationModel &&\n    settings.baseUrl\n  );\n}\n\n/**\n * Get AI provider settings with API key from backend\n * This function retrieves the complete configuration including the decrypted API key\n */\nexport async function getAIProviderSettingsWithApiKey(): Promise<AIProviderSettings & { apiKey: string }> {\n  try {\n    const localSettings = getAIProviderSettings();\n\n    // Import the API function dynamically to avoid circular dependencies\n    const { getCredentialsAPI } = await import('./api');\n\n    // Get credentials from backend\n    const credentialsResult = await getCredentialsAPI(localSettings.provider);\n\n    if (!credentialsResult.success || !credentialsResult.hasCredentials || !credentialsResult.configuration) {\n      throw new Error('No credentials found for the current provider');\n    }\n\n    // For now, we'll need to get the API key from the backend when making actual API calls\n    // The backend will handle decryption and use the key directly\n    return {\n      ...localSettings,\n      apiKey: '', // API key will be handled by backend\n      provider: credentialsResult.configuration.provider,\n      baseUrl: credentialsResult.configuration.baseUrl,\n      extractionModel: credentialsResult.configuration.extractionModel,\n      generationModel: credentialsResult.configuration.generationModel,\n      model: credentialsResult.configuration.generationModel,\n    };\n  } catch (error) {\n    console.error('Error getting AI provider settings with API key:', error);\n    throw error;\n  }\n}\n\n/**\n * Get list of available AI models by provider\n */\nexport function getAvailableModels(\n  provider: string\n): { id: string; name: string }[] {\n  switch (provider) {\n    case \"OpenRouter\":\n      return [\n        { id: \"google/gemini-2.5-flash-preview-05-20\", name: \"Google Gemini Flash 2.5\" },\n        {\n          id: \"google/gemini-2.5-pro-preview\",\n          name: \"Google Gemini Pro 2.5 (Recommended)\",\n        },\n      ];\n    default:\n      return [];\n  }\n}\n\n/**\n * Get available AI providers\n */\nexport function getAvailableProviders(): string[] {\n  return [\"OpenRouter\"];\n}\n\n/**\n * Get default base URL for a provider\n */\nexport function getDefaultBaseUrl(provider: string): string {\n  switch (provider) {\n    case \"OpenRouter\":\n      return \"https://openrouter.ai/api/v1\";\n    default:\n      return \"\";\n  }\n}\n\n/**\n * Get recommended model for specific tasks\n * This follows the PRD recommendations\n */\nexport function getRecommendedModelForTask(\n  task: \"extraction\" | \"generation\"\n): string {\n  const settings = getAIProviderSettings();\n  switch (task) {\n    case \"extraction\":\n      return settings.extractionModel || \"google/gemini-2.5-flash-preview-05-20\";\n    case \"generation\":\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n    default:\n      return settings.generationModel || \"google/gemini-2.5-pro-preview\";\n  }\n}\n"}