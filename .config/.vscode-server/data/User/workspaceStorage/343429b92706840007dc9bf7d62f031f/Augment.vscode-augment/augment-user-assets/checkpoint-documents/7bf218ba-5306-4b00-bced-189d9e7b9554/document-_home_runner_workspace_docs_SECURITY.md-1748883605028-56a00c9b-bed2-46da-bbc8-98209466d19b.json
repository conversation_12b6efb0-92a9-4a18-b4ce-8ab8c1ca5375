{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/SECURITY.md"}, "originalCode": "# ChewyAI Security Documentation\n\n## 🔐 Security Architecture\n\n### API Key Management\n\n#### Client-Side Security\n- **NO API keys are stored on the client-side** (localStorage or sessionStorage)\n- User AI credentials are stored encrypted in Supabase database\n- Only non-sensitive configuration stored in localStorage (provider, models, base URL)\n- Supabase anonymous key is safe for client-side use (read-only access)\n- All sensitive operations require authentication\n\n#### Backend Security\n- User AI provider credentials encrypted with AES-256-GCM before database storage\n- API keys retrieved and decrypted only during AI API calls (ephemeral use)\n- No logging of sensitive credentials (API keys, tokens, headers)\n- Backend-owned credentials managed via environment variables only\n- Proper input validation and sanitization with Zod schemas\n- Row Level Security (RLS) enforces user data isolation\n\n### Authentication & Authorization\n\n#### Supabase Authentication\n- JWT-based authentication using Supabase Auth\n- Row Level Security (RLS) policies enforce data access\n- Session management handled by Supabase client\n- Automatic token refresh\n\n#### API Protection\n- All protected endpoints require valid JWT token\n- Authorization header: `Bearer <jwt_token>`\n- User context extracted from token for data access\n- Proper error handling for unauthorized requests\n\n## 🛡️ Security Headers\n\n### Production Security Headers\n```\nX-Content-Type-Options: nosniff\nX-Frame-Options: DENY\nX-XSS-Protection: 1; mode=block\nReferrer-Policy: strict-origin-when-cross-origin\nContent-Security-Policy: [configured for app requirements]\n```\n\n### CORS Configuration\n- Configured for specific origins in production\n- Credentials allowed for authenticated requests\n- Proper preflight handling for complex requests\n\n## 🔒 Environment Variables\n\n### Required Production Variables\n```bash\n# Supabase Configuration\nSUPABASE_URL=https://your-project.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=your-service-role-key\n\n# Client Environment Variables\nVITE_SUPABASE_URL=https://your-project.supabase.co\nVITE_SUPABASE_ANON_KEY=your-anon-key\nVITE_API_BASE_URL=https://your-domain.com/api\n\n# Application Configuration\nNODE_ENV=production\nPORT=80\n```\n\n### Security Best Practices\n- Never commit `.env` files to version control\n- Use different keys for development and production\n- Rotate keys regularly\n- Monitor for exposed credentials in logs\n\n## 🚨 Data Protection\n\n### User Data Handling\n- Text extraction performed client-side when possible\n- Content sent to self-hosted backend, then to user's AI provider\n- User AI credentials encrypted and stored securely in database\n- User data stored in Supabase with RLS policies enforcing isolation\n\n### AI Provider Integration\n- User's own API keys used for AI requests (retrieved from encrypted storage)\n- No ChewyAI-owned AI credits exposed to users\n- Transparent data flow: Client → ChewyAI Backend → User's AI Provider\n- No caching of AI responses containing user data\n- API keys decrypted only during request processing (ephemeral use)\n\n## 🔍 Security Monitoring\n\n### Logging Practices\n- Log authentication attempts and failures\n- Monitor API usage patterns\n- Never log sensitive credentials or user data\n- Implement proper error handling without exposing internals\n\n### Health Checks\n- `/api/health` endpoint for monitoring\n- No sensitive information in health check responses\n- Proper error responses without stack traces in production\n\n## 🛠️ Development Security\n\n### Local Development\n- Use `.env.example` as template for local environment\n- Never use production credentials in development\n- Test with development Supabase project\n- Validate security headers in development\n\n### Code Security\n- Input validation using Zod schemas\n- SQL injection prevention with parameterized queries\n- XSS prevention with proper output encoding\n- CSRF protection for state-changing operations\n\n## 📋 Security Checklist\n\n### Pre-Deployment\n- [ ] All environment variables configured\n- [ ] No hardcoded credentials in code\n- [ ] Security headers implemented\n- [ ] CORS properly configured\n- [ ] Authentication working correctly\n- [ ] Input validation in place\n- [ ] Error handling doesn't expose internals\n\n### Post-Deployment\n- [ ] Health check endpoint responding\n- [ ] Authentication flow working\n- [ ] API endpoints properly protected\n- [ ] Security headers present in responses\n- [ ] No sensitive data in logs\n- [ ] HTTPS enforced (handled by Replit)\n\n## 🚨 Incident Response\n\n### If Credentials Are Compromised\n1. Immediately rotate affected credentials\n2. Update environment variables in deployment\n3. Redeploy application\n4. Monitor for unauthorized access\n5. Notify users if necessary\n\n### Security Updates\n- Monitor dependencies for security vulnerabilities\n- Update packages regularly\n- Test security updates in development first\n- Document security-related changes\n", "modifiedCode": "# ChewyAI Security Documentation\n\n## 🔐 Security Architecture\n\n### API Key Management\n\n#### Client-Side Security\n- **NO API keys are stored on the client-side** (localStorage or sessionStorage)\n- User AI credentials are stored encrypted in Supabase database\n- Only non-sensitive configuration stored in localStorage (provider, models, base URL)\n- Supabase anonymous key is safe for client-side use (read-only access)\n- All sensitive operations require authentication\n\n#### Backend Security\n- User AI provider credentials encrypted with AES-256-GCM before database storage\n- API keys retrieved and decrypted only during AI API calls (ephemeral use)\n- No logging of sensitive credentials (API keys, tokens, headers)\n- Backend-owned credentials managed via environment variables only\n- Proper input validation and sanitization with Zod schemas\n- Row Level Security (RLS) enforces user data isolation\n\n### Authentication & Authorization\n\n#### Supabase Authentication\n- JWT-based authentication using Supabase Auth\n- Row Level Security (RLS) policies enforce data access\n- Session management handled by Supabase client\n- Automatic token refresh\n\n#### API Protection\n- All protected endpoints require valid JWT token\n- Authorization header: `Bearer <jwt_token>`\n- User context extracted from token for data access\n- Proper error handling for unauthorized requests\n\n## 🛡️ Security Headers\n\n### Production Security Headers\n```\nX-Content-Type-Options: nosniff\nX-Frame-Options: DENY\nX-XSS-Protection: 1; mode=block\nReferrer-Policy: strict-origin-when-cross-origin\nContent-Security-Policy: [configured for app requirements]\n```\n\n### CORS Configuration\n- Configured for specific origins in production\n- Credentials allowed for authenticated requests\n- Proper preflight handling for complex requests\n\n## 🔒 Environment Variables\n\n### Required Production Variables\n```bash\n# Supabase Configuration\nSUPABASE_URL=https://your-project.supabase.co\nSUPABASE_SERVICE_ROLE_KEY=your-service-role-key\n\n# Client Environment Variables\nVITE_SUPABASE_URL=https://your-project.supabase.co\nVITE_SUPABASE_ANON_KEY=your-anon-key\nVITE_API_BASE_URL=https://your-domain.com/api\n\n# Application Configuration\nNODE_ENV=production\nPORT=80\n```\n\n### Security Best Practices\n- Never commit `.env` files to version control\n- Use different keys for development and production\n- Rotate keys regularly\n- Monitor for exposed credentials in logs\n\n## 🚨 Data Protection\n\n### User Data Handling\n- Text extraction performed client-side when possible\n- Content sent to self-hosted backend, then to user's AI provider\n- User AI credentials encrypted and stored securely in database\n- User data stored in Supabase with RLS policies enforcing isolation\n\n### AI Provider Integration\n- User's own API keys used for AI requests (retrieved from encrypted storage)\n- No ChewyAI-owned AI credits exposed to users\n- Transparent data flow: Client → ChewyAI Backend → User's AI Provider\n- No caching of AI responses containing user data\n- API keys decrypted only during request processing (ephemeral use)\n\n## 🔍 Security Monitoring\n\n### Logging Practices\n- Log authentication attempts and failures\n- Monitor API usage patterns\n- Never log sensitive credentials or user data\n- Implement proper error handling without exposing internals\n\n### Health Checks\n- `/api/health` endpoint for monitoring\n- No sensitive information in health check responses\n- Proper error responses without stack traces in production\n\n## 🛠️ Development Security\n\n### Local Development\n- Use `.env.example` as template for local environment\n- Never use production credentials in development\n- Test with development Supabase project\n- Validate security headers in development\n\n### Code Security\n- Input validation using Zod schemas\n- SQL injection prevention with parameterized queries\n- XSS prevention with proper output encoding\n- CSRF protection for state-changing operations\n\n## 📋 Security Checklist\n\n### Pre-Deployment\n- [ ] All environment variables configured\n- [ ] No hardcoded credentials in code\n- [ ] Security headers implemented\n- [ ] CORS properly configured\n- [ ] Authentication working correctly\n- [ ] Input validation in place\n- [ ] Error handling doesn't expose internals\n\n### Post-Deployment\n- [ ] Health check endpoint responding\n- [ ] Authentication flow working\n- [ ] API endpoints properly protected\n- [ ] Security headers present in responses\n- [ ] No sensitive data in logs\n- [ ] HTTPS enforced (handled by Replit)\n\n## 🚨 Incident Response\n\n### If Credentials Are Compromised\n1. Immediately rotate affected credentials\n2. Update environment variables in deployment\n3. Redeploy application\n4. Monitor for unauthorized access\n5. Notify users if necessary\n\n### Security Updates\n- Monitor dependencies for security vulnerabilities\n- Update packages regularly\n- Test security updates in development first\n- Document security-related changes\n\n## 🔍 Security Audit History\n\n### 2025-06-02 - Comprehensive Security Audit\n**Critical Vulnerabilities Found and Fixed:**\n\n1. **CRITICAL: Hardcoded Production Credentials**\n   - **Issue**: Supabase service role key hardcoded in `server/config.ts`\n   - **Risk**: Full database access exposed in source code\n   - **Fix**: Removed hardcoded fallback, enforced environment variables\n\n2. **HIGH: Client-Side API Key Exposure**\n   - **Issue**: Attempted to use `VITE_OPENROUTER_API_KEY` environment variable\n   - **Risk**: API keys would be exposed in browser bundle\n   - **Fix**: Removed client-side API key references\n\n3. **HIGH: Insecure API Key Storage**\n   - **Issue**: User API keys stored unencrypted in localStorage\n   - **Risk**: Keys accessible to any script on domain\n   - **Fix**: Implemented AES-256-GCM encryption with database storage\n\n**Moderate Issues Fixed:**\n- Removed sensitive header logging from middleware\n- Updated npm packages to fix known vulnerabilities\n- Enhanced authentication error handling\n\n**New Security Features Implemented:**\n- Encrypted user credential storage in database\n- Row Level Security policies for credential access\n- Secure API key retrieval for ephemeral use\n- Enhanced environment variable validation\n\n**Remaining Development Dependencies:**\n- esbuild vulnerability affects development only (not production)\n- Vite vulnerability affects development server only\n- No impact on production deployment security\n"}