{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/flashcards/FlashcardGenerationPopup.tsx"}, "originalCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>Trigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface FlashcardGenerationOptions {\n  numberOfCards: number;\n  customPrompt: string;\n}\n\ninterface FlashcardGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: FlashcardGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxCards?: number;\n}\n\nexport const FlashcardGenerationPopup: React.FC<FlashcardGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxCards = 50,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<FlashcardGenerationOptions>({\n    numberOfCards: 10,\n    customPrompt: \"\",\n  });\n\n  const handleGenerate = async () => {\n    if (options.numberOfCards <= 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Flashcard generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.numberOfCards > 0 && options.numberOfCards <= maxCards;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-card border-border\">\n        <DialogHeader>\n          <DialogTitle className=\"text-card-foreground\">Generate Flashcards Options</DialogTitle>\n        </DialogHeader>\n        \n        <div className=\"space-y-6 py-4\">\n          {/* Number of Cards */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfCards\" className=\"text-card-foreground\">\n              Number of Flashcards\n            </Label>\n            <Input\n              id=\"numberOfCards\"\n              type=\"number\"\n              min=\"1\"\n              max={maxCards}\n              value={options.numberOfCards}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfCards: Math.max(1, Math.min(maxCards, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-background border-border text-foreground\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Choose between 1 and {maxCards} flashcards\n            </p>\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-card-foreground\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-background border-border text-foreground min-h-[80px]\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Add specific instructions for the AI on what kind of flashcards you want.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isGenerating || !isValidOptions}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {isGenerating ? (\n              <>\n                <Spinner size=\"sm\" className=\"mr-2\" />\n                Generating...\n              </>\n            ) : (\n              \"Generate Flashcards\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n", "modifiedCode": "import React, { useState } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>Trigger,\n} from \"@/components/ui/dialog\";\nimport { Button } from \"@/components/ui/button\";\nimport { Label } from \"@/components/ui/label\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport Spinner from \"@/components/ui/Spinner\";\n\ninterface FlashcardGenerationOptions {\n  numberOfCards: number;\n  customPrompt: string;\n}\n\ninterface FlashcardGenerationPopupProps {\n  trigger: React.ReactNode;\n  onGenerate: (options: FlashcardGenerationOptions) => Promise<void>;\n  isGenerating: boolean;\n  disabled?: boolean;\n  maxCards?: number;\n}\n\nexport const FlashcardGenerationPopup: React.FC<FlashcardGenerationPopupProps> = ({\n  trigger,\n  onGenerate,\n  isGenerating,\n  disabled = false,\n  maxCards = 50,\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [options, setOptions] = useState<FlashcardGenerationOptions>({\n    numberOfCards: 10,\n    customPrompt: \"\",\n  });\n\n  const handleGenerate = async () => {\n    if (options.numberOfCards <= 0) {\n      return; // Validation handled by UI\n    }\n\n    try {\n      await onGenerate(options);\n      setIsOpen(false);\n    } catch (error) {\n      // Error handling is done by parent component\n      console.error(\"Flashcard generation failed:\", error);\n    }\n  };\n\n  const isValidOptions = options.numberOfCards > 0 && options.numberOfCards <= maxCards;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={setIsOpen}>\n      <DialogTrigger asChild disabled={disabled}>\n        {trigger}\n      </DialogTrigger>\n      <DialogContent className=\"sm:max-w-[500px] bg-card border-border\">\n        <DialogHeader>\n          <DialogTitle className=\"text-card-foreground\">Generate Flashcards Options</DialogTitle>\n        </DialogHeader>\n        \n        <div className=\"space-y-6 py-4\">\n          {/* Number of Cards */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"numberOfCards\" className=\"text-card-foreground\">\n              Number of Flashcards\n            </Label>\n            <Input\n              id=\"numberOfCards\"\n              type=\"number\"\n              min=\"1\"\n              max={maxCards}\n              value={options.numberOfCards}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  numberOfCards: Math.max(1, Math.min(maxCards, parseInt(e.target.value) || 1)),\n                }))\n              }\n              className=\"bg-background border-border text-foreground\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Choose between 1 and {maxCards} flashcards\n            </p>\n          </div>\n\n          {/* Custom Prompt */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"customPrompt\" className=\"text-card-foreground\">\n              Custom Prompt (Optional)\n            </Label>\n            <Textarea\n              id=\"customPrompt\"\n              placeholder=\"e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'\"\n              value={options.customPrompt}\n              onChange={(e) =>\n                setOptions((prev) => ({\n                  ...prev,\n                  customPrompt: e.target.value,\n                }))\n              }\n              className=\"bg-background border-border text-foreground min-h-[80px]\"\n            />\n            <p className=\"text-xs text-muted-foreground\">\n              Add specific instructions for the AI on what kind of flashcards you want.\n            </p>\n          </div>\n        </div>\n\n        <div className=\"flex justify-end space-x-2 pt-4\">\n          <Button\n            variant=\"outline\"\n            onClick={() => setIsOpen(false)}\n            disabled={isGenerating}\n          >\n            Cancel\n          </Button>\n          <Button\n            onClick={handleGenerate}\n            disabled={isGenerating || !isValidOptions}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            {isGenerating ? (\n              <>\n                <Spinner size=\"sm\" className=\"mr-2\" />\n                Generating...\n              </>\n            ) : (\n              \"Generate Flashcards\"\n            )}\n          </Button>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n};\n"}