{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apply-migration.js"}, "modifiedCode": "#!/usr/bin/env node\n\n// <PERSON><PERSON><PERSON> to apply the user_ai_credentials migration\nimport { createClient } from '@supabase/supabase-js';\nimport fs from 'fs';\n\n// Environment variables\nconst SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\n\n// Create Supabase client\nconst supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);\n\nasync function applyMigration() {\n  console.log('📄 Reading migration file...');\n  \n  try {\n    const migrationSQL = fs.readFileSync('supabase/migrations/20250602_create_user_ai_credentials.sql', 'utf8');\n    console.log('✅ Migration file loaded');\n    \n    console.log('🗄️ Applying migration to database...');\n    \n    // Execute the migration SQL\n    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });\n    \n    if (error) {\n      console.error('❌ Migration failed:', error);\n      return false;\n    }\n    \n    console.log('✅ Migration applied successfully!');\n    \n    // Test if the table exists now\n    console.log('🔍 Verifying table creation...');\n    const { data: tableData, error: tableError } = await supabase\n      .from('user_ai_credentials')\n      .select('id')\n      .limit(1);\n    \n    if (tableError) {\n      console.error('❌ Table verification failed:', tableError);\n      return false;\n    }\n    \n    console.log('✅ Table verified successfully!');\n    return true;\n    \n  } catch (error) {\n    console.error('❌ Error applying migration:', error);\n    return false;\n  }\n}\n\nasync function main() {\n  console.log('🚀 Starting migration application...\\n');\n  \n  const success = await applyMigration();\n  \n  if (success) {\n    console.log('\\n🎉 Migration completed successfully!');\n    console.log('The user_ai_credentials table is now ready for use.');\n  } else {\n    console.log('\\n❌ Migration failed. Please check the errors above.');\n  }\n}\n\nmain().catch(console.error);\n"}