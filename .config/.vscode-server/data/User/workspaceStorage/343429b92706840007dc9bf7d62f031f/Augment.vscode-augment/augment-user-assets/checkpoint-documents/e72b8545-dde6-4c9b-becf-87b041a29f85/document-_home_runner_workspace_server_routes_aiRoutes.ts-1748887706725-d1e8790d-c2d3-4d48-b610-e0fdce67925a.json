{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/aiRoutes.ts"}, "originalCode": "import express, { Request, Response } from \"express\";\nimport axios from \"axios\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  FlashcardDeck,\n  Flashcard,\n  GenerateFlashcardsRequest,\n  GenerateFlashcardsResponse,\n} from \"@shared/types/flashcards\";\n\nconst router = express.Router();\n\n// POST /api/flashcards/generate\n// Changed from /generate-flashcards to /flashcards/generate to match client expectations\nrouter.post(\"/flashcards/generate\", async (req: Request, res: Response) => {\n  console.log(\"Received request at /flashcards/generate:\", req.body);\n\n  const { textContent, documentId, deckTitle, count, customPrompt } = req.body;\n\n  if (!textContent || !documentId) {\n    return res.status(400).json({\n      error: \"Missing required fields. Need textContent and documentId.\",\n    });\n  }\n\n  // Get user ID from JWT token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({ error: \"Authentication required\" });\n  }\n\n  const token = authHeader.substring(7);\n  let userId: string;\n\n  try {\n    // Import supabase client for JWT verification\n    const { supabaseClient } = await import('../middleware/supabaseMiddleware');\n    const { data: { user }, error } = await supabaseClient.auth.getUser(token);\n\n    if (error || !user) {\n      return res.status(401).json({ error: \"Invalid authentication token\" });\n    }\n\n    userId = user.id;\n  } catch (error) {\n    console.error(\"Authentication error:\", error);\n    return res.status(401).json({ error: \"Authentication failed\" });\n  }\n\n  // Use the user-specified count or default to 10\n  const flashcardCount = count && count > 0 ? count : 10;\n\n  // Get AI credentials from secure storage\n  const { getEphemeralUserCredentials } = await import('../middleware/apiKeyStorage');\n  const credentialsResult = await getEphemeralUserCredentials(userId, 'OpenRouter');\n\n  if (!credentialsResult.success || !credentialsResult.credentials) {\n    return res.status(400).json({\n      error: \"AI Provider not configured. Please configure your AI settings first.\",\n      details: \"No stored credentials found for OpenRouter provider\"\n    });\n  }\n\n  const { apiKey, baseUrl, generationModel } = credentialsResult.credentials;\n\n  console.log(\n    `Generating flashcards for document ${documentId} using model ${generationModel} via OpenRouter at ${baseUrl}`\n  );\n\n  // Build the prompt with optional custom instructions\n  let prompt = `Based on the following text, generate exactly ${flashcardCount} flashcards (question and answer pairs).\nEach flashcard should focus on a key concept, term, or fact from the text.\n\nText:\n\"\"\"\n${textContent}\n\"\"\"\n\nGuidelines for flashcard generation:\n1. Create exactly ${flashcardCount} flashcards\n2. Questions should be clear and specific\n3. Answers should be concise but complete\n4. Focus on the most important concepts\n5. Include a mix of definition-based and application-based cards\n6. Make sure questions test understanding, not just memorization\n7. If the text content is limited, prioritize the most essential concepts`;\n\n  // Add custom prompt instructions if provided\n  if (customPrompt && customPrompt.trim()) {\n    prompt += `\\n\\nAdditional Instructions:\n${customPrompt.trim()}`;\n  }\n\n  prompt += `\\n\\nResponse Format:\nReturn a JSON object containing a \"flashcards\" array with exactly ${flashcardCount} elements, where each element has a \"question\" and \"answer\" property.\nExample: {\"flashcards\": [{\"question\": \"What is anthropology?\", \"answer\": \"The study of human cultures and societies.\"}]}\n`;\n\n  try {\n    const response = await axios.post(\n      `${baseUrl}/chat/completions`,\n      {\n        model: generationModel,\n        messages: [{ role: \"user\", content: prompt }],\n        temperature: 0.5,\n        response_format: { type: \"json_object\" },\n      },\n      {\n        headers: {\n          Authorization: `Bearer ${apiKey}`,\n          \"Content-Type\": \"application/json\",\n          \"HTTP-Referer\": \"https://chewyai.app\",\n          \"X-Title\": \"ChewyAI\",\n        },\n      }\n    );\n\n    console.log(\"API response received:\", response.status);\n\n    let rawFlashcards = [];\n    if (\n      response.data &&\n      response.data.choices &&\n      response.data.choices[0]?.message?.content\n    ) {\n      const content = response.data.choices[0].message.content;\n      console.log(\n        \"Raw AI Response (first 100 chars):\",\n        content.substring(0, 100)\n      );\n\n      try {\n        // Parse the JSON response\n        const parsedContent = JSON.parse(content);\n\n        if (\n          parsedContent.flashcards &&\n          Array.isArray(parsedContent.flashcards)\n        ) {\n          rawFlashcards = parsedContent.flashcards;\n        } else {\n          console.error(\n            \"Parsed content does not contain a valid flashcards array:\",\n            parsedContent\n          );\n          throw new Error(\"Invalid flashcard structure in AI response\");\n        }\n      } catch (parseError) {\n        console.error(\"Failed to parse AI response JSON:\", parseError);\n\n        // Try to extract JSON from markdown code blocks as fallback\n        try {\n          const jsonMatch =\n            content.match(/```json\\n([\\s\\S]*?)\\n```/) ||\n            content.match(/```\\n([\\s\\S]*?)\\n```/) ||\n            content.match(/{[\\s\\S]*}/);\n\n          if (jsonMatch) {\n            const jsonContent = jsonMatch[1] || jsonMatch[0];\n            const parsedContent = JSON.parse(jsonContent);\n\n            if (\n              parsedContent.flashcards &&\n              Array.isArray(parsedContent.flashcards)\n            ) {\n              rawFlashcards = parsedContent.flashcards;\n            }\n          }\n        } catch (e) {\n          console.error(\"Failed fallback parsing:\", e);\n        }\n\n        if (rawFlashcards.length === 0) {\n          return res\n            .status(500)\n            .json({ error: \"Failed to parse flashcards from AI response\" });\n        }\n      }\n    } else {\n      console.error(\"Unexpected AI response structure:\", response.data);\n      return res\n        .status(500)\n        .json({ error: \"Invalid response structure from AI provider\" });\n    }\n\n    // Create a flashcard deck using the format expected by the client\n    const deckId = uuidv4();\n    const flashcardDeck: FlashcardDeck = {\n      id: deckId,\n      documentId: documentId,\n      title: deckTitle || `Flashcards for ${documentId}`,\n      flashcards: rawFlashcards.map((fc) => ({\n        id: uuidv4(),\n        question: fc.question,\n        answer: fc.answer,\n        deckId: deckId,\n      })),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    // Return in the format expected by client (using deck property)\n    const responsePayload: GenerateFlashcardsResponse = {\n      deck: flashcardDeck,\n    };\n\n    console.log(\n      `Successfully generated deck with ${flashcardDeck.flashcards.length} flashcards.`\n    );\n    return res.status(201).json(responsePayload);\n  } catch (error: any) {\n    console.error(\n      \"Error calling AI provider:\",\n      error.response?.data || error.message\n    );\n    return res.status(500).json({\n      error: \"Failed to generate flashcards from AI provider\",\n      details: error.response?.data?.error || error.message,\n    });\n  }\n});\n\n// POST /api/extract-and-format\n// AI-powered text extraction and markdown formatting using Gemini 2.5 Flash\nrouter.post(\"/extract-and-format\", async (req: Request, res: Response) => {\n  console.log(\"Received request at /extract-and-format:\", req.body);\n\n  const { rawText, fileName, aiSettings } = req.body;\n\n  if (!rawText || !aiSettings || !aiSettings.apiKey) {\n    return res.status(400).json({\n      error: \"Missing required fields. Need rawText and aiSettings with apiKey.\",\n    });\n  }\n\n  const { provider, baseUrl, apiKey } = aiSettings;\n\n  // Use the extractionModel for text processing as specified in the PRD\n  // Fall back to generationModel, then model for backward compatibility\n  const model =\n    aiSettings.extractionModel ||\n    aiSettings.generationModel ||\n    aiSettings.model ||\n    \"google/gemini-flash-1.5\";\n\n  console.log(\n    `Processing text extraction and formatting for ${fileName} using model ${model} via ${provider} at ${baseUrl}`\n  );\n\n  // Create AI prompt for text extraction and formatting\n  const prompt = `You are an expert document processor specialized in converting raw extracted text into clean, well-formatted, visually appealing Markdown. Your goal is to preserve the document's structure and meaning while making it highly readable, legible, and aesthetically pleasing.\n\nDocument: ${fileName}\n\nCRITICAL INSTRUCTIONS FOR BEAUTIFUL, LEGIBLE MARKDOWN:\n\n🎯 **PRIMARY GOALS:**\n- Transform raw text into visually stunning, highly readable markdown\n- Create clear visual hierarchy with proper spacing and formatting\n- Make content scannable and aesthetically pleasing\n\n📝 **FORMATTING EXCELLENCE:**\n1. **Document Structure:**\n   - Use clear heading hierarchy (# ## ### ####) with descriptive titles\n   - Add proper spacing between sections (double line breaks)\n   - Create logical content flow with smooth transitions\n\n2. **Visual Enhancement:**\n   - **Bold** key terms, definitions, and important concepts\n   - *Italicize* emphasis, foreign terms, and book/article titles\n   - Use \\`inline code\\` for technical terms, variables, and specific values\n   - Create > blockquotes for important notes, definitions, or key insights\n\n3. **List Optimization:**\n   - Convert bullet points (●, ○, •) to clean markdown bullets (-)\n   - Use numbered lists (1. 2. 3.) for sequential information\n   - Add proper spacing between list items for readability\n   - Nest sub-items with proper indentation\n\n4. **Content Cleanup:**\n   - Fix OCR errors, broken words, and formatting artifacts\n   - Remove excessive whitespace while maintaining readability\n   - Ensure proper sentence structure and paragraph flow\n   - Add line breaks between distinct concepts or topics\n\n5. **Academic/Technical Preservation:**\n   - Keep all technical terms, formulas, and data intact\n   - Preserve citation formats and references\n   - Maintain numerical data and statistics accuracy\n   - Convert tables to proper markdown table format\n\n6. **Visual Separators:**\n   - Use horizontal rules (---) to separate major sections\n   - Add appropriate spacing around headings and sections\n   - Ensure consistent formatting throughout the document\n\nRaw Text (first 50,000 characters):\n---\n${rawText.substring(0, 50000)}\n---\n\n✨ **FINAL REQUIREMENTS:**\n- Output should be visually stunning and professional\n- Every element should be properly spaced and formatted\n- Content should be immediately scannable and easy to read\n- Use markdown features to create visual interest and clarity\n\nReturn ONLY the beautifully formatted Markdown content. Do not include explanations, metadata, or commentary.`;\n\n  try {\n    const response = await axios.post(\n      `${baseUrl}/chat/completions`,\n      {\n        model: model,\n        messages: [{ role: \"user\", content: prompt }],\n        temperature: 0.1, // Low temperature for consistent formatting\n        max_tokens: 12000, // Increased for beautifully formatted content\n      },\n      {\n        headers: {\n          Authorization: `Bearer ${apiKey}`,\n          \"Content-Type\": \"application/json\",\n          \"HTTP-Referer\": \"https://chewyai.app\",\n          \"X-Title\": \"ChewyAI\",\n        },\n      }\n    );\n\n    console.log(\"AI extraction response received:\", response.status);\n\n    if (response.data && response.data.choices && response.data.choices[0]) {\n      const formattedContent = response.data.choices[0].message.content;\n\n      console.log(\"Successfully processed text extraction and formatting\");\n      return res.status(200).json({\n        success: true,\n        formattedContent: formattedContent.trim(),\n        originalLength: rawText.length,\n        formattedLength: formattedContent.length,\n      });\n    } else {\n      console.error(\"Unexpected AI response structure:\", response.data);\n      return res\n        .status(500)\n        .json({ error: \"Invalid response structure from AI provider\" });\n    }\n  } catch (error: any) {\n    console.error(\n      \"Error calling AI provider for text extraction:\",\n      error.response?.data || error.message\n    );\n    return res.status(500).json({\n      error: \"Failed to process text extraction with AI provider\",\n      details: error.response?.data?.error || error.message,\n    });\n  }\n});\n\nexport default router;\n", "modifiedCode": "import express, { Request, Response } from \"express\";\nimport axios from \"axios\";\nimport { v4 as uuidv4 } from \"uuid\";\nimport {\n  FlashcardDeck,\n  Flashcard,\n  GenerateFlashcardsRequest,\n  GenerateFlashcardsResponse,\n} from \"@shared/types/flashcards\";\n\nconst router = express.Router();\n\n// POST /api/flashcards/generate\n// Changed from /generate-flashcards to /flashcards/generate to match client expectations\nrouter.post(\"/flashcards/generate\", async (req: Request, res: Response) => {\n  console.log(\"Received request at /flashcards/generate:\", req.body);\n\n  const { textContent, documentId, deckTitle, count, customPrompt } = req.body;\n\n  if (!textContent || !documentId) {\n    return res.status(400).json({\n      error: \"Missing required fields. Need textContent and documentId.\",\n    });\n  }\n\n  // Get user ID from JWT token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({ error: \"Authentication required\" });\n  }\n\n  const token = authHeader.substring(7);\n  let userId: string;\n\n  try {\n    // Import supabase client for JWT verification\n    const { supabaseClient } = await import('../middleware/supabaseMiddleware');\n    const { data: { user }, error } = await supabaseClient.auth.getUser(token);\n\n    if (error || !user) {\n      return res.status(401).json({ error: \"Invalid authentication token\" });\n    }\n\n    userId = user.id;\n  } catch (error) {\n    console.error(\"Authentication error:\", error);\n    return res.status(401).json({ error: \"Authentication failed\" });\n  }\n\n  // Use the user-specified count or default to 10\n  const flashcardCount = count && count > 0 ? count : 10;\n\n  // Get AI credentials from secure storage\n  const { getEphemeralUserCredentials } = await import('../middleware/apiKeyStorage');\n  const credentialsResult = await getEphemeralUserCredentials(userId, 'OpenRouter');\n\n  if (!credentialsResult.success || !credentialsResult.credentials) {\n    return res.status(400).json({\n      error: \"AI Provider not configured. Please configure your AI settings first.\",\n      details: \"No stored credentials found for OpenRouter provider\"\n    });\n  }\n\n  const { apiKey, baseUrl, generationModel } = credentialsResult.credentials;\n\n  console.log(\n    `Generating flashcards for document ${documentId} using model ${generationModel} via OpenRouter at ${baseUrl}`\n  );\n\n  // Build the prompt with optional custom instructions\n  let prompt = `Based on the following text, generate exactly ${flashcardCount} flashcards (question and answer pairs).\nEach flashcard should focus on a key concept, term, or fact from the text.\n\nText:\n\"\"\"\n${textContent}\n\"\"\"\n\nGuidelines for flashcard generation:\n1. Create exactly ${flashcardCount} flashcards\n2. Questions should be clear and specific\n3. Answers should be concise but complete\n4. Focus on the most important concepts\n5. Include a mix of definition-based and application-based cards\n6. Make sure questions test understanding, not just memorization\n7. If the text content is limited, prioritize the most essential concepts`;\n\n  // Add custom prompt instructions if provided\n  if (customPrompt && customPrompt.trim()) {\n    prompt += `\\n\\nAdditional Instructions:\n${customPrompt.trim()}`;\n  }\n\n  prompt += `\\n\\nResponse Format:\nReturn a JSON object containing a \"flashcards\" array with exactly ${flashcardCount} elements, where each element has a \"question\" and \"answer\" property.\nExample: {\"flashcards\": [{\"question\": \"What is anthropology?\", \"answer\": \"The study of human cultures and societies.\"}]}\n`;\n\n  try {\n    const response = await axios.post(\n      `${baseUrl}/chat/completions`,\n      {\n        model: generationModel,\n        messages: [{ role: \"user\", content: prompt }],\n        temperature: 0.5,\n        response_format: { type: \"json_object\" },\n      },\n      {\n        headers: {\n          Authorization: `Bearer ${apiKey}`,\n          \"Content-Type\": \"application/json\",\n          \"HTTP-Referer\": \"https://chewyai.app\",\n          \"X-Title\": \"ChewyAI\",\n        },\n      }\n    );\n\n    console.log(\"API response received:\", response.status);\n\n    let rawFlashcards = [];\n    if (\n      response.data &&\n      response.data.choices &&\n      response.data.choices[0]?.message?.content\n    ) {\n      const content = response.data.choices[0].message.content;\n      console.log(\n        \"Raw AI Response (first 100 chars):\",\n        content.substring(0, 100)\n      );\n\n      try {\n        // Parse the JSON response\n        const parsedContent = JSON.parse(content);\n\n        if (\n          parsedContent.flashcards &&\n          Array.isArray(parsedContent.flashcards)\n        ) {\n          rawFlashcards = parsedContent.flashcards;\n        } else {\n          console.error(\n            \"Parsed content does not contain a valid flashcards array:\",\n            parsedContent\n          );\n          throw new Error(\"Invalid flashcard structure in AI response\");\n        }\n      } catch (parseError) {\n        console.error(\"Failed to parse AI response JSON:\", parseError);\n\n        // Try to extract JSON from markdown code blocks as fallback\n        try {\n          const jsonMatch =\n            content.match(/```json\\n([\\s\\S]*?)\\n```/) ||\n            content.match(/```\\n([\\s\\S]*?)\\n```/) ||\n            content.match(/{[\\s\\S]*}/);\n\n          if (jsonMatch) {\n            const jsonContent = jsonMatch[1] || jsonMatch[0];\n            const parsedContent = JSON.parse(jsonContent);\n\n            if (\n              parsedContent.flashcards &&\n              Array.isArray(parsedContent.flashcards)\n            ) {\n              rawFlashcards = parsedContent.flashcards;\n            }\n          }\n        } catch (e) {\n          console.error(\"Failed fallback parsing:\", e);\n        }\n\n        if (rawFlashcards.length === 0) {\n          return res\n            .status(500)\n            .json({ error: \"Failed to parse flashcards from AI response\" });\n        }\n      }\n    } else {\n      console.error(\"Unexpected AI response structure:\", response.data);\n      return res\n        .status(500)\n        .json({ error: \"Invalid response structure from AI provider\" });\n    }\n\n    // Create a flashcard deck using the format expected by the client\n    const deckId = uuidv4();\n    const flashcardDeck: FlashcardDeck = {\n      id: deckId,\n      documentId: documentId,\n      title: deckTitle || `Flashcards for ${documentId}`,\n      flashcards: rawFlashcards.map((fc) => ({\n        id: uuidv4(),\n        question: fc.question,\n        answer: fc.answer,\n        deckId: deckId,\n      })),\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    // Return in the format expected by client (using deck property)\n    const responsePayload: GenerateFlashcardsResponse = {\n      deck: flashcardDeck,\n    };\n\n    console.log(\n      `Successfully generated deck with ${flashcardDeck.flashcards.length} flashcards.`\n    );\n    return res.status(201).json(responsePayload);\n  } catch (error: any) {\n    console.error(\n      \"Error calling AI provider:\",\n      error.response?.data || error.message\n    );\n    return res.status(500).json({\n      error: \"Failed to generate flashcards from AI provider\",\n      details: error.response?.data?.error || error.message,\n    });\n  }\n});\n\n// POST /api/extract-and-format\n// AI-powered text extraction and markdown formatting using Gemini 2.5 Flash\nrouter.post(\"/extract-and-format\", async (req: Request, res: Response) => {\n  console.log(\"Received request at /extract-and-format:\", req.body);\n\n  const { rawText, fileName } = req.body;\n\n  if (!rawText) {\n    return res.status(400).json({\n      error: \"Missing required fields. Need rawText.\",\n    });\n  }\n\n  // Get user ID from JWT token\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({ error: \"Authentication required\" });\n  }\n\n  const token = authHeader.substring(7);\n  let userId: string;\n\n  try {\n    // Import supabase client for JWT verification\n    const { supabaseClient } = await import('../middleware/supabaseMiddleware');\n    const { data: { user }, error } = await supabaseClient.auth.getUser(token);\n\n    if (error || !user) {\n      return res.status(401).json({ error: \"Invalid authentication token\" });\n    }\n\n    userId = user.id;\n  } catch (error) {\n    console.error(\"Authentication error:\", error);\n    return res.status(401).json({ error: \"Authentication failed\" });\n  }\n\n  // Get AI credentials from secure storage\n  const { getEphemeralUserCredentials } = await import('../middleware/apiKeyStorage');\n  const credentialsResult = await getEphemeralUserCredentials(userId, 'OpenRouter');\n\n  if (!credentialsResult.success || !credentialsResult.credentials) {\n    return res.status(400).json({\n      error: \"AI Provider not configured. Please configure your AI settings first.\",\n      details: \"No stored credentials found for OpenRouter provider\"\n    });\n  }\n\n  const { apiKey, baseUrl, extractionModel } = credentialsResult.credentials;\n\n  console.log(\n    `Processing text extraction and formatting for ${fileName} using model ${model} via ${provider} at ${baseUrl}`\n  );\n\n  // Create AI prompt for text extraction and formatting\n  const prompt = `You are an expert document processor specialized in converting raw extracted text into clean, well-formatted, visually appealing Markdown. Your goal is to preserve the document's structure and meaning while making it highly readable, legible, and aesthetically pleasing.\n\nDocument: ${fileName}\n\nCRITICAL INSTRUCTIONS FOR BEAUTIFUL, LEGIBLE MARKDOWN:\n\n🎯 **PRIMARY GOALS:**\n- Transform raw text into visually stunning, highly readable markdown\n- Create clear visual hierarchy with proper spacing and formatting\n- Make content scannable and aesthetically pleasing\n\n📝 **FORMATTING EXCELLENCE:**\n1. **Document Structure:**\n   - Use clear heading hierarchy (# ## ### ####) with descriptive titles\n   - Add proper spacing between sections (double line breaks)\n   - Create logical content flow with smooth transitions\n\n2. **Visual Enhancement:**\n   - **Bold** key terms, definitions, and important concepts\n   - *Italicize* emphasis, foreign terms, and book/article titles\n   - Use \\`inline code\\` for technical terms, variables, and specific values\n   - Create > blockquotes for important notes, definitions, or key insights\n\n3. **List Optimization:**\n   - Convert bullet points (●, ○, •) to clean markdown bullets (-)\n   - Use numbered lists (1. 2. 3.) for sequential information\n   - Add proper spacing between list items for readability\n   - Nest sub-items with proper indentation\n\n4. **Content Cleanup:**\n   - Fix OCR errors, broken words, and formatting artifacts\n   - Remove excessive whitespace while maintaining readability\n   - Ensure proper sentence structure and paragraph flow\n   - Add line breaks between distinct concepts or topics\n\n5. **Academic/Technical Preservation:**\n   - Keep all technical terms, formulas, and data intact\n   - Preserve citation formats and references\n   - Maintain numerical data and statistics accuracy\n   - Convert tables to proper markdown table format\n\n6. **Visual Separators:**\n   - Use horizontal rules (---) to separate major sections\n   - Add appropriate spacing around headings and sections\n   - Ensure consistent formatting throughout the document\n\nRaw Text (first 50,000 characters):\n---\n${rawText.substring(0, 50000)}\n---\n\n✨ **FINAL REQUIREMENTS:**\n- Output should be visually stunning and professional\n- Every element should be properly spaced and formatted\n- Content should be immediately scannable and easy to read\n- Use markdown features to create visual interest and clarity\n\nReturn ONLY the beautifully formatted Markdown content. Do not include explanations, metadata, or commentary.`;\n\n  try {\n    const response = await axios.post(\n      `${baseUrl}/chat/completions`,\n      {\n        model: model,\n        messages: [{ role: \"user\", content: prompt }],\n        temperature: 0.1, // Low temperature for consistent formatting\n        max_tokens: 12000, // Increased for beautifully formatted content\n      },\n      {\n        headers: {\n          Authorization: `Bearer ${apiKey}`,\n          \"Content-Type\": \"application/json\",\n          \"HTTP-Referer\": \"https://chewyai.app\",\n          \"X-Title\": \"ChewyAI\",\n        },\n      }\n    );\n\n    console.log(\"AI extraction response received:\", response.status);\n\n    if (response.data && response.data.choices && response.data.choices[0]) {\n      const formattedContent = response.data.choices[0].message.content;\n\n      console.log(\"Successfully processed text extraction and formatting\");\n      return res.status(200).json({\n        success: true,\n        formattedContent: formattedContent.trim(),\n        originalLength: rawText.length,\n        formattedLength: formattedContent.length,\n      });\n    } else {\n      console.error(\"Unexpected AI response structure:\", response.data);\n      return res\n        .status(500)\n        .json({ error: \"Invalid response structure from AI provider\" });\n    }\n  } catch (error: any) {\n    console.error(\n      \"Error calling AI provider for text extraction:\",\n      error.response?.data || error.message\n    );\n    return res.status(500).json({\n      error: \"Failed to process text extraction with AI provider\",\n      details: error.response?.data?.error || error.message,\n    });\n  }\n});\n\nexport default router;\n"}