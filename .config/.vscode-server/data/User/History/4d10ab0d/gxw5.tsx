import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Terminal, Sparkles } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabaseClient";
import { Tables } from "@/types/supabase";
import { generateFlashcardsAPI } from "@/lib/api";
import { getAIProviderSettings, isAIProviderConfigured } from "@/lib/ai-provider";
import Spinner from "@/components/ui/Spinner";

type Flashcard = Tables<"flashcards">;

interface AiFlashcardGeneratorProps {
  selectedDeckId: string;
  selectedDeckName: string;
  onGenerationComplete: () => void;
}

const AiFlashcardGenerator: React.FC<AiFlashcardGeneratorProps> = ({
  selectedDeckId,
  selectedDeckName,
  onGenerationComplete,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // Document selection states
  const [availableDocuments, setAvailableDocuments] = useState<Tables<"study_documents">[]>([]);
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const [loadingDocuments, setLoadingDocuments] = useState<boolean>(false);
  
  // Generation options
  const [numberOfCards, setNumberOfCards] = useState<number>(10);
  const [customPrompt, setCustomPrompt] = useState<string>("");
  
  // UI states
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Load documents on mount
  useEffect(() => {
    const fetchDocuments = async () => {
      if (user) {
        setLoadingDocuments(true);
        try {
          const { data, error: dbError } = await supabase
            .from("study_documents")
            .select("*")
            .eq("user_id", user.id)
            .eq("status", "extracted")
            .order("created_at", { ascending: false });

          if (dbError) throw dbError;
          setAvailableDocuments(data || []);
        } catch (err: any) {
          console.error("Error fetching documents:", err);
          toast({
            title: "Error",
            description: "Could not load documents for AI generation.",
            variant: "destructive",
          });
        } finally {
          setLoadingDocuments(false);
        }
      }
    };
    fetchDocuments();
  }, [user, toast]);

  const handleDocumentSelection = (documentId: string) => {
    setSelectedDocumentIds((prev) =>
      prev.includes(documentId)
        ? prev.filter((id) => id !== documentId)
        : [...prev, documentId]
    );
  };

  const handleGenerate = async () => {
    const isConfigured = await isAIProviderConfigured();
    if (!isConfigured) {
      setError("AI Provider not configured. Please configure your AI settings first.");
      return;
    }

    if (selectedDocumentIds.length === 0) {
      setError("Please select at least one document.");
      return;
    }

    if (numberOfCards < 1 || numberOfCards > 50) {
      setError("Number of cards must be between 1 and 50.");
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const aiSettings = getAIProviderSettings();
      const { data: { session } } = await supabase.auth.getSession();

      if (!session?.access_token) {
        throw new Error("No authentication session found. Please log in again.");
      }

      // Fetch document contents
      const documentContents: string[] = [];
      for (const docId of selectedDocumentIds) {
        try {
          const response = await fetch(`/api/documents/${docId}/content`, {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session.access_token}`,
            },
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch document ${docId}: HTTP ${response.status}`);
          }

          const textContent = await response.text();
          if (textContent.trim()) {
            documentContents.push(textContent);
          }
        } catch (err) {
          console.error(`Error fetching document ${docId}:`, err);
          throw new Error(`Failed to fetch document ${docId}: ${err instanceof Error ? err.message : 'Unknown error'}`);
        }
      }

      if (documentContents.length === 0) {
        throw new Error("No valid document content found.");
      }

      // Combine all document contents
      const combinedContent = documentContents.join("\n\n--- Document Separator ---\n\n");

      // API payload
      const payload = {
        textContent: combinedContent,
        documentId: selectedDocumentIds.join(","),
        deckTitle: `Additional cards for ${selectedDeckName}`,
        count: numberOfCards,
        customPrompt: customPrompt || undefined,
        // aiSettings removed - credentials are retrieved from secure backend storage
      };

      console.log("📤 Generating additional flashcards:", payload);
      const response = await generateFlashcardsAPI(payload);
      console.log("📥 Received flashcard generation response:", response);

      if (!response) {
        throw new Error("No response received from flashcard generation API");
      }

      // Process the response and save flashcards
      const deckData = response.deck || response;
      const flashcardsData = deckData.flashcards || [];

      if (Array.isArray(flashcardsData) && flashcardsData.length > 0) {
        // Prepare flashcards for batch insert
        const flashcardsToInsert = flashcardsData.map((fc: any) => ({
          set_id: selectedDeckId,
          user_id: user!.id,
          front_text: fc.question || fc.front_text || "No question",
          back_text: fc.answer || fc.back_text || "No answer",
        }));

        // Insert flashcards into Supabase
        const { error: insertError } = await supabase
          .from("flashcards")
          .insert(flashcardsToInsert);

        if (insertError) {
          throw new Error(`Failed to save flashcards: ${insertError.message}`);
        }

        toast({
          title: "Success!",
          description: `Generated ${flashcardsData.length} new flashcards for "${selectedDeckName}".`,
        });

        // Clear selections and notify parent
        setSelectedDocumentIds([]);
        setCustomPrompt("");
        setNumberOfCards(10);
        onGenerationComplete();
      } else {
        throw new Error("No flashcards were generated.");
      }
    } catch (e) {
      console.error("Error generating flashcards:", e);
      setError(
        e instanceof Error ? e.message : "Failed to generate flashcards."
      );
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Sparkles className="h-5 w-5 text-purple-400" />
        <h3 className="text-lg font-medium text-slate-100">
          Generate More Flashcards with AI
        </h3>
      </div>

      {/* Number of Cards */}
      <div>
        <Label htmlFor="number-of-cards" className="text-slate-300">
          Number of Flashcards*
        </Label>
        <Input
          id="number-of-cards"
          type="number"
          min="1"
          max="50"
          value={numberOfCards}
          onChange={(e) => setNumberOfCards(parseInt(e.target.value) || 10)}
          placeholder="10"
          disabled={isGenerating}
          className="mt-1 w-32 !bg-slate-900 !border-slate-700 !text-slate-200 placeholder:text-slate-500 focus:ring-purple-500 focus:border-purple-500"
        />
        <p className="text-xs text-slate-400 mt-1">
          Specify exactly how many flashcards to generate (1-50)
        </p>
      </div>

      {/* Document Selection */}
      <div>
        <Label className="text-slate-300 mb-2 block">
          Select Documents for AI Generation*
        </Label>
        {loadingDocuments ? (
          <div className="flex justify-center items-center py-4">
            <Spinner size="sm" />
            <span className="ml-2 text-purple-300">Loading documents...</span>
          </div>
        ) : availableDocuments.length === 0 ? (
          <p className="text-sm text-purple-300 p-3 bg-slate-700 rounded-md">
            No extracted documents available. Please upload and process documents first.
          </p>
        ) : (
          <div className="max-h-60 overflow-y-auto space-y-2 p-3 bg-slate-700/50 rounded-md border border-slate-600">
            {availableDocuments.map((doc) => (
              <div
                key={doc.id}
                className="flex items-center space-x-2 p-2 bg-slate-700 rounded hover:bg-slate-600/70 transition-colors"
              >
                <Checkbox
                  id={`doc-${doc.id}`}
                  checked={selectedDocumentIds.includes(doc.id)}
                  onCheckedChange={() => handleDocumentSelection(doc.id)}
                  disabled={isGenerating}
                />
                <Label
                  htmlFor={`doc-${doc.id}`}
                  className="font-normal text-purple-300 cursor-pointer flex-1 truncate"
                  title={doc.file_name}
                >
                  {doc.file_name}
                </Label>
              </div>
            ))}
          </div>
        )}
        {selectedDocumentIds.length > 0 && (
          <p className="text-xs text-purple-400 mt-1">
            {selectedDocumentIds.length} document(s) selected.
          </p>
        )}
      </div>

      {/* Custom Prompt */}
      <div>
        <Label htmlFor="custom-prompt" className="text-slate-300">
          Custom Prompt (Optional)
        </Label>
        <Textarea
          id="custom-prompt"
          value={customPrompt}
          onChange={(e) => setCustomPrompt(e.target.value)}
          placeholder="e.g., 'Focus on key definitions', 'Create scenario-based questions', 'Make flashcards suitable for beginners'"
          rows={3}
          className="mt-1 w-full p-2 border rounded-md bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:ring-purple-500 focus:border-purple-500"
          disabled={isGenerating}
        />
        <p className="text-xs text-purple-400 mt-1">
          Add specific instructions for the AI on what kind of flashcards you want.
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Button
        onClick={handleGenerate}
        disabled={
          isGenerating ||
          selectedDocumentIds.length === 0 ||
          numberOfCards < 1 ||
          numberOfCards > 50 ||
          !isAIProviderConfigured()
        }
        className="w-full bg-purple-600 hover:bg-purple-700 text-white"
      >
        {isGenerating ? (
          <>
            <Spinner size="sm" className="mr-2" />
            Generating Flashcards...
          </>
        ) : (
          <>
            <Sparkles className="h-4 w-4 mr-2" />
            Generate Flashcards with AI
          </>
        )}
      </Button>
    </div>
  );
};

export default AiFlashcardGenerator;
