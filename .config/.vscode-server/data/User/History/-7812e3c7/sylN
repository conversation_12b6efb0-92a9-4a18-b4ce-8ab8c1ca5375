# Development & Documentation
- Always maintain and update markdown files in /docs/ folder with accurate, specific information about the codebase, keeping documentation current with any changes made.

# Security
- Backend should handle all AI provider credentials securely by retrieving encrypted keys from database during API calls, never transmitting API keys to frontend.
- Frontend should check backend for credential existence rather than localStorage.

# Deployment
- ChewyAI production setup completed with security-first architecture, optimized builds, proper environment variable handling, comprehensive documentation, and successful Replit deployment configuration.
`