import { extractTextFromPdf } from './pdf-parser';
import { extractTextFromDocx, extractTextFromTxt, extractTextFromMd } from './docx-parser';
import { Document } from '@/types';
import { getAIProviderSettings, isAIProviderConfigured } from './ai-provider';
import { supabase } from './supabaseClient';

/**
 * AI-powered text extraction and formatting using Gemini 2.5 Flash
 */
async function enhanceTextWithAI(rawText: string, fileName: string): Promise<string> {
  // Skip AI enhancement if provider not configured
  const isConfigured = await isAIProviderConfigured();
  if (!isConfigured) {
    console.log('🔧 AI provider not configured, using basic text extraction');
    return rawText;
  }

  // Skip AI enhancement for very short texts (less than 100 characters)
  if (rawText.length < 100) {
    console.log('📄 Text too short for AI enhancement, using raw text');
    return rawText;
  }

  try {
    console.log(`🤖 Enhancing text extraction for "${fileName}" using Gemini 2.5 Flash...`);

    // Get the current session for auth token
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.access_token) {
      console.warn('⚠️ No authentication token available, falling back to basic text extraction');
      return rawText;
    }

    const response = await fetch('/api/extract-and-format', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify({
        rawText,
        fileName,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.warn(`⚠️ AI extraction failed (${response.status}): ${errorText}`);
      console.log('📄 Falling back to basic text extraction');
      return rawText;
    }

    const data = await response.json();
    if (data.success && data.formattedContent) {
      const improvement = ((data.formattedLength / data.originalLength) * 100).toFixed(1);
      console.log(`✅ AI extraction successful: ${data.originalLength} → ${data.formattedLength} chars (${improvement}% of original)`);
      return data.formattedContent;
    } else {
      console.warn('⚠️ AI extraction returned invalid response structure');
      console.log('📄 Falling back to basic text extraction');
      return rawText;
    }
  } catch (error) {
    console.warn('❌ AI extraction error:', error);
    console.log('📄 Falling back to basic text extraction');
    return rawText;
  }
}

/**
 * Extract text from a file based on its type
 * Unified interface for all file parsers with AI enhancement
 */
export async function extractTextFromFile(file: File): Promise<Document> {
  const fileType = file.name.toLowerCase();
  let rawDocument: Document;

  // First, extract raw text using appropriate parser
  if (fileType.endsWith('.pdf')) {
    rawDocument = await extractTextFromPdf(file);
  } else if (fileType.endsWith('.docx')) {
    rawDocument = await extractTextFromDocx(file);
  } else if (fileType.endsWith('.txt')) {
    rawDocument = await extractTextFromTxt(file);
  } else if (fileType.endsWith('.md')) {
    rawDocument = await extractTextFromMd(file);
  } else {
    throw new Error('Unsupported file format. Please upload a PDF, DOCX, TXT, or MD file.');
  }

  // Enhance text with AI if available (uses Gemini 2.5 Flash for extraction)
  const enhancedContent = await enhanceTextWithAI(rawDocument.content, file.name);

  return {
    ...rawDocument,
    content: enhancedContent,
  };
}