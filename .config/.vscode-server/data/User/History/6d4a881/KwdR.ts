import { AIProviderSettings } from "@/types"; // Removed Document import
import {
  GenerateFlashcardsResponse,
  FlashcardSet,
} from "@shared/types/flashcards";

import { Quiz, AIProviderConfig, GetAllQuizzesApiResponse, QuestionType as SharedQuestionType } from "@shared/types/quiz";
import { supabase } from "./supabaseClient"; // Added Tables import
import { Tables } from "../types/supabase";

interface GenerateFlashcardsApiPayload {
  textContent: string;
  documentId: string;
  deckTitle?: string;
  count?: number; // Optional: based on server/routes.ts schema
  customPrompt?: string; // New: custom prompt for AI generation
  // aiSettings removed - credentials are retrieved from secure backend storage
}

/**
 * Calls the backend API to generate flashcards.
 */
export async function generateFlashcardsAPI(
  payload: GenerateFlashcardsApiPayload
): Promise<any> {
  // AI credentials are now handled by the backend
  const apiPayload = {
    ...payload,
  };

  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    console.log("📤 generateFlashcardsAPI sending request:", {
      url: "/api/flashcards/generate",
      payload: apiPayload,
      hasAuth: !!session?.access_token
    });

    const response = await fetch("/api/flashcards/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
      body: JSON.stringify(apiPayload),
    });

    console.log("📥 generateFlashcardsAPI response status:", response.status, response.statusText);

    if (!response.ok) {
      // Try to get the response text first for debugging
      const responseText = await response.text();

      let errorData;
      try {
        // Try to parse the text as JSON if possible
        errorData = JSON.parse(responseText);
      } catch (e) {
        // If it's not JSON, create a more descriptive error message
        throw new Error(
          `Failed to generate flashcards: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to generate flashcards: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    // Get response text first for debugging
    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText);
      console.log("📥 generateFlashcardsAPI parsed response:", data);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response: ${errorMessage}`);
    }

    // Return the full response - the mapping function will handle the structure
    return data;
  } catch (error) {
    throw error;
  }
}

export interface GenerateAiQuizApiPayload {
  textContent?: string; // Optional: Direct text content for immediate generation (UploadSection)
  documentId?: string; // Optional: ID of the study document for single document generation
  documentIds?: string[]; // Array of document IDs for multi-document generation
  quizName: string;
  quizDescription?: string;
  customPrompt?: string; // This is used by CreateQuizForm, not directly by UploadSection's simplified form
  generationOptions: {
    numberOfQuestions: number;
    questionTypes: string[];
  };
  aiConfig?: AIProviderConfig;
}

export interface GenerateAiQuizApiResponse {
  success: boolean;
  quiz?: GeneratedQuizStructure; // Use the more specific type
  quizId: string;
  id: string; // This seems redundant with quizId, but matches existing server response
  name: string;
}

// Define a more specific type for the quiz structure returned by /generate endpoint
interface GeneratedQuizStructure {
  id: string;
  name: string;
  // other quiz metadata fields from Tables<"quizzes"> can be added if needed
  questions: Array<{
    id: string;
    questionText: string; // Matches server response structure
    type: SharedQuestionType; 
    options?: any; 
    correctAnswer?: string | boolean | string[] | null; // Matches server response structure
    explanation?: string | null;
  }>;
}

export interface CreateQuizApiPayload {
  name: string;
  description?: string;
  study_document_id?: string;
}

export interface CreateQuizApiResponse {
  id: string;
  name: string;
}

/**
 * Calls the backend API to create a quiz manually.
 */
export async function createQuizAPI(
  payload: CreateQuizApiPayload
): Promise<CreateQuizApiResponse> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch("/api/quizzes", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to create quiz: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to create quiz: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as CreateQuizApiResponse;
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Create Quiz API: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to generate a quiz using AI.
 */
export async function generateAiQuizAPI(
  payload: GenerateAiQuizApiPayload
): Promise<GenerateAiQuizApiResponse> {
  // Ensure backward compatibility
  let apiPayload: any = {
    ...payload,
    // If documentIds is provided, use it; otherwise use documentId wrapped in an array
    documentIds:
      payload.documentIds && payload.documentIds.length > 0 ? payload.documentIds : (payload.documentId ? [payload.documentId] : []),
    customPrompt: payload.customPrompt,
  };
  // Remove the old single documentId to avoid confusion
  if ("documentId" in apiPayload) {
    delete apiPayload.documentId;
  }

  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch("/api/quizzes/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
      body: JSON.stringify(apiPayload),
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to generate AI quiz: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to generate AI quiz: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as GenerateAiQuizApiResponse; // Assuming the direct response is the quiz object
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from AI Quiz Generation API: ${errorMessage}`);
    }

    // Ensure the returned data matches GenerateAiQuizApiResponse, specifically id and name
    if (!data || typeof data.id !== "string" || typeof data.name !== "string") {
      throw new Error(
        "Unexpected response structure from AI Quiz Generation API. Expected id and name."
      );
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to fetch all quizzes for the authenticated user.
 */
export async function getAllQuizzesAPI(): Promise<GetAllQuizzesApiResponse> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // Make sure the URL matches exactly what the server expects (removing trailing slash)
    const response = await fetch("/api/quizzes/", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to fetch quizzes: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to fetch quizzes: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as GetAllQuizzesApiResponse;
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Get All Quizzes API: ${errorMessage}`);
    }

    // Basic validation for the response structure
    if (!data || !Array.isArray(data.quizzes)) {
      throw new Error(
        "Unexpected response structure from Get All Quizzes API. Expected an object with a 'quizzes' array."
      );
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to fetch a single quiz by ID.
 */
export async function getQuizByIdAPI(quizId: string): Promise<any> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch(`/api/quizzes/${quizId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to fetch quiz: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to fetch quiz: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText);
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Get Quiz API: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

// Document API Functions
// export interface GetDocumentsApiResponse { // This type is not used by getDocumentsAPI
//   documents: Document[];
// }

export interface CreateDocumentApiPayload {
  name: string;
  content: string;
  type: string;
  createdAt: number;
  size: number;
}

export interface UpdateDocumentApiPayload {
  name?: string;
  content?: string;
  type?: string;
  size?: number;
}

/**
 * Calls the backend API to fetch all documents for the authenticated user.
 */
export async function getDocumentsAPI(): Promise<Tables<"study_documents">[]> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch("/api/documents", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to fetch documents: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to fetch documents: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as Tables<"study_documents">[];
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Get Documents API: ${errorMessage}`);
    }

    // Basic validation for the response structure
    if (!Array.isArray(data)) {
      throw new Error(
        "Unexpected response structure from Get Documents API. Expected an array of documents."
      );
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to fetch a single document by ID.
 */
export async function getDocumentAPI(
  id: string
): Promise<Tables<"study_documents">> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch(`/api/documents/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to fetch document: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to fetch document: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as Tables<"study_documents">;
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Get Document API: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to create a new document.
 */
export async function createDocumentAPI(
  payload: CreateDocumentApiPayload
): Promise<Tables<"study_documents">> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch("/api/documents", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to create document: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to create document: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as Tables<"study_documents">;
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Create Document API: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to update a document.
 */
export async function updateDocumentAPI(
  id: string,
  payload: UpdateDocumentApiPayload
): Promise<Tables<"study_documents">> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch(`/api/documents/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to update document: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to update document: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }

    const responseText = await response.text();

    let data;
    try {
      data = JSON.parse(responseText) as Tables<"study_documents">;
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      throw new Error(`Invalid JSON response from Update Document API: ${errorMessage}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to delete a document.
 */
export async function deleteDocumentAPI(id: string): Promise<void> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    const response = await fetch(`/api/documents/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session?.access_token || ""}`,
      },
    });

    if (!response.ok) {
      const responseText = await response.text();

      let errorData;
      try {
        errorData = JSON.parse(responseText);
      } catch (e) {
        throw new Error(
          `Failed to delete document: ${response.status} ${response.statusText}.\n` +
            `Server returned non-JSON response: ${responseText.substring(
              0,
              200
            )}...`
        );
      }

      throw new Error(
        `Failed to delete document: ${response.status} ${response.statusText}.\n` +
          (errorData.message ? `Message: ${errorData.message}` : "") +
          (errorData.error ? ` Details: ${errorData.error}` : "") +
          (errorData.errors
            ? ` Validation: ${JSON.stringify(errorData.errors)}`
            : "")
      );
    }
  } catch (error) {
    throw error;
  }
}

// Get the backend API URL from environment variables
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:5000/api";

// Helper to get the current user's auth token
async function getAuthToken(): Promise<string | null> {
  try {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    return session?.access_token || null;
  } catch (error) {
    return null;
  }
}

// Helper to create authenticated request headers
async function getAuthHeaders(): Promise<HeadersInit> {
  const token = await getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
}

// Document upload types
export interface SecureUploadRequest {
  fileName: string;
  content: string;
  contentType: string;
  sizeBytes: number;
  documentId?: string;
}

export interface SecureUploadResponse {
  success: boolean;
  document: {
    id: string;
    user_id: string;
    file_name: string;
    file_path: string;
    content_type: string;
    size_bytes: number;
    status: string;
    extracted_text_path: string;
    extracted_text_summary: string;
    created_at: string;
    updated_at: string;
  };
  message: string;
}

// Secure document upload through backend
export async function uploadDocumentSecure(
  uploadData: SecureUploadRequest
): Promise<SecureUploadResponse> {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/documents/secure-upload`, {
      method: "POST",
      headers,
      body: JSON.stringify(uploadData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const result = await response.json();
    return result;
  } catch (error) {
    throw error;
  }
}

// Get all documents for the current user (using Supabase directly for now, but could be moved to backend)
export async function getUserDocuments() {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(`${API_BASE_URL}/documents`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const documents = await response.json();
    return documents;
  } catch (error) {
    throw error;
  }
}

// Get document content securely through backend
export async function getDocumentContentAPI(
  documentId: string
): Promise<string> {
  try {
    const headers = await getAuthHeaders();

    const response = await fetch(
      `${API_BASE_URL}/documents/${documentId}/content`,
      {
        method: "GET",
        headers,
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const content = await response.text();
    return content;
  } catch (error) {
    throw error;
  }
}

/**
 * Calls the backend API to delete a quiz.
 */
export async function deleteQuizAPI(quizId: string): Promise<void> {
  const {
    data: { session },
  } = await supabase.auth.getSession();

  const response = await fetch(`/api/quizzes/${quizId}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${session?.access_token || ""}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error || `Failed to delete quiz: ${response.statusText}`
    );
  }
}

// Types for AI Question Generation via Server
interface AIGenerationOptions {
  numberOfQuestions: number;
  questionTypes: string[];
}

// Types for Credentials API
interface StoreCredentialsRequest {
  provider: string;
  apiKey: string;
  baseUrl: string;
  extractionModel: string;
  generationModel: string;
}

interface StoreCredentialsResponse {
  success: boolean;
  message: string;
}

interface GetCredentialsResponse {
  success: boolean;
  hasCredentials: boolean;
  configuration?: {
    provider: string;
    baseUrl: string;
    extractionModel: string;
    generationModel: string;
  };
}

/**
 * Store user's AI provider credentials securely on the backend
 */
export async function storeCredentialsAPI(
  credentials: StoreCredentialsRequest
): Promise<StoreCredentialsResponse> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error("Authentication required to save credentials");
    }

    console.log("📤 storeCredentialsAPI sending request:", {
      url: "/api/credentials",
      provider: credentials.provider,
      hasAuth: !!session?.access_token
    });

    const response = await fetch("/api/credentials", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.access_token}`,
      },
      body: JSON.stringify(credentials),
    });

    console.log("📥 storeCredentialsAPI response status:", response.status, response.statusText);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `Failed to store credentials: ${response.statusText}`
      );
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("❌ storeCredentialsAPI error:", error);
    throw error;
  }
}

/**
 * Get user's stored credentials configuration (without API key)
 */
export async function getCredentialsAPI(
  provider: string
): Promise<GetCredentialsResponse> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error("Authentication required to retrieve credentials");
    }

    const response = await fetch(`/api/credentials/${provider}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return {
          success: false,
          hasCredentials: false,
        };
      }
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `Failed to retrieve credentials: ${response.statusText}`
      );
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("❌ getCredentialsAPI error:", error);
    throw error;
  }
}

/**
 * Delete user's stored credentials
 */
export async function deleteCredentialsAPI(provider: string): Promise<void> {
  try {
    // Get the current session for auth token
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session?.access_token) {
      throw new Error("Authentication required to delete credentials");
    }

    const response = await fetch(`/api/credentials/${provider}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.access_token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `Failed to delete credentials: ${response.statusText}`
      );
    }
  } catch (error) {
    console.error("❌ deleteCredentialsAPI error:", error);
    throw error;
  }
}

export interface GenerateAiQuestionsFromServerPayload {
  documentId: string;
  quizName: string;
  customPrompt?: string;
  generationOptions: AIGenerationOptions;
}

// Matches the AIGeneratedQuestion interface in the backend/edge function
export interface AIGeneratedQuestion {
  question_text: string;
  type:
    | "multiple_choice"
    | "true_false"
    | "short_answer"
    | "select_all_that_apply";
  options?: { text: string; is_correct: boolean }[];
  correct_answer: string;
  explanation?: string;
}

export async function generateAiQuestionsFromDocumentAPI(
  payload: GenerateAiQuestionsFromServerPayload
): Promise<AIGeneratedQuestion[]> {
  const headers = await getAuthHeaders();
  const response = await fetch(
    `${API_BASE_URL}/ai/generate-questions-from-document`,
    {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error ||
        `Failed to generate AI questions: ${response.statusText}`
    );
  }
  return response.json();
}

// Types for adding questions to quiz
// This should align with your quiz_questions table structure, excluding quiz_id/user_id managed by backend
export interface QuizQuestionBatchInsertPayload {
  question_text: string;
  type: string; // Should match Enums<"question_type">
  options?: any; // JSONB
  correct_answer?: string;
  explanation?: string;
}

export async function addQuestionsToQuizAPI(
  quizId: string,
  questions: QuizQuestionBatchInsertPayload[]
): Promise<Tables<"quiz_questions">[]> {
  const headers = await getAuthHeaders();
  const response = await fetch(
    `${API_BASE_URL}/quizzes/${quizId}/questions/batch`,
    {
      method: "POST",
      headers,
      body: JSON.stringify({ questions }),
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error ||
        `Failed to add questions to quiz: ${response.statusText}`
    );
  }
  return response.json();
}

// Define the structure for a multiple-choice option
export interface McqOption {
  text: string;
  is_correct: boolean;
}

export interface CreateQuizApiPayload {
  name: string;
  description?: string;
  study_document_id?: string;
}

export interface CreateQuizApiResponse {
  id: string;
  name: string;
}
