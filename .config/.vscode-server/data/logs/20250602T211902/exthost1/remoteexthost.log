2025-06-02 21:19:06.079 [info] Extension host with pid 248 started
2025-06-02 21:19:06.079 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock'
2025-06-02 21:19:06.079 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-06-02 21:19:06.083 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock': The pid 2689 appears to be gone.
2025-06-02 21:19:06.083 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock': Deleting a stale lock.
2025-06-02 21:19:06.173 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/343429b92706840007dc9bf7d62f031f/vscode.lock': Lock acquired.
2025-06-02 21:19:06.251 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onView:augment-chat'
2025-06-02 21:19:06.253 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-06-02 21:19:06.253 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-06-02 21:19:06.253 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescriptreact'
2025-06-02 21:19:06.254 [info] ExtensionService#_doActivateExtension christian-kohler.npm-intellisense, startup: false, activationEvent: 'onLanguage:typescriptreact'
2025-06-02 21:19:06.254 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-02 21:19:06.254 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-06-02 21:19:07.419 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onTerminalQuickFixRequest:copilot-chat.terminalToDebuggingSuccess'
2025-06-02 21:19:08.618 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-06-02 21:19:08.957 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-06-02 21:19:08.957 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-06-02 21:19:09.065 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-06-02 21:19:09.605 [info] Eager extensions activated
2025-06-02 21:19:09.606 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-06-02 21:19:09.606 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-06-02 21:19:09.607 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-06-02 21:19:12.382 [info] ExtensionService#_doActivateExtension GitHub.vscode-pull-request-github, startup: false, activationEvent: 'onStartupFinished'
2025-06-02 21:19:16.338 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-06-02 21:19:23.155 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Cmfave%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at n_e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
