2025-06-02 21:19:11.425 [info] Can't use the Electron fetcher in this environment.
2025-06-02 21:19:11.425 [info] Using the Node fetch fetcher.
2025-06-02 21:19:11.425 [info] Initializing Git extension service.
2025-06-02 21:19:11.425 [info] Successfully activated the vscode.git extension.
2025-06-02 21:19:11.425 [info] Enablement state of the vscode.git extension: true.
2025-06-02 21:19:11.425 [info] Successfully registered Git commit message provider.
2025-06-02 21:19:14.993 [info] Logged in as Chewy42
2025-06-02 21:19:21.436 [info] Got Copilot token for Chewy42
2025-06-02 21:19:21.765 [info] TypeScript server plugin activated.
2025-06-02 21:19:21.766 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-02 21:19:22.429 [info] Fetched model metadata in 957ms fcc9d79b-be08-4f59-ba0f-bf6da9779257
2025-06-02 21:19:22.449 [info] activationBlocker from 'languageModelAccess' took for 10846ms
2025-06-02 21:19:22.694 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-02 21:19:22.737 [info] Registering default platform agent...
2025-06-02 21:19:23.145 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-02 21:19:23.147 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-02 21:19:23.147 [info] Successfully registered GitHub PR title and description provider.
2025-06-02 21:19:23.148 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-02 21:19:23.361 [info] BYOK: Copilot Chat known models list fetched successfully.
