2025-06-02 21:19:12.681 [info] [main] Log level: Info
2025-06-02 21:19:12.681 [info] [main] Validating found git in: "git"
2025-06-02 21:19:12.681 [info] [main] Using git "2.47.2" from "git"
2025-06-02 21:19:12.681 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 21:19:12.681 [info] > git rev-parse --show-toplevel [89ms]
2025-06-02 21:19:12.681 [info] > git rev-parse --git-dir --git-common-dir [3ms]
2025-06-02 21:19:12.681 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 21:19:12.681 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 21:19:12.681 [info] > git rev-parse --show-toplevel [113ms]
2025-06-02 21:19:12.681 [info] > git config --get commit.template [121ms]
2025-06-02 21:19:12.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [297ms]
2025-06-02 21:19:12.681 [info] > git check-ignore -v -z --stdin [276ms]
2025-06-02 21:19:12.683 [info] > git status -z -uall [24ms]
2025-06-02 21:19:12.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-02 21:19:12.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [130ms]
2025-06-02 21:19:12.862 [info] > git rev-parse --show-toplevel [180ms]
2025-06-02 21:19:12.898 [info] > git config --get --local branch.main.vscode-merge-base [37ms]
2025-06-02 21:19:12.905 [info] > git config --get commit.template [158ms]
2025-06-02 21:19:13.509 [info] > git rev-parse --show-toplevel [623ms]
2025-06-02 21:19:13.851 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [946ms]
2025-06-02 21:19:13.918 [info] > git merge-base refs/heads/main refs/remotes/origin/main [58ms]
2025-06-02 21:19:13.926 [info] > git rev-parse --show-toplevel [375ms]
2025-06-02 21:19:13.927 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [391ms]
2025-06-02 21:19:14.286 [info] > git rev-parse --show-toplevel [9ms]
2025-06-02 21:19:14.286 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [360ms]
2025-06-02 21:19:14.340 [info] > git status -z -uall [37ms]
2025-06-02 21:19:14.340 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [31ms]
2025-06-02 21:19:14.344 [info] > git rev-parse --show-toplevel [48ms]
2025-06-02 21:19:14.365 [info] > git rev-parse --show-toplevel [13ms]
2025-06-02 21:19:14.379 [info] > git rev-parse --show-toplevel [6ms]
2025-06-02 21:19:14.773 [info] > git rev-parse --show-toplevel [155ms]
2025-06-02 21:19:15.135 [info] > git rev-parse --show-toplevel [243ms]
2025-06-02 21:19:15.209 [info] > git rev-parse --show-toplevel [50ms]
2025-06-02 21:19:15.243 [info] > git rev-parse --show-toplevel [21ms]
2025-06-02 21:19:15.353 [info] > git rev-parse --show-toplevel [94ms]
2025-06-02 21:19:15.951 [info] > git rev-parse --show-toplevel [118ms]
2025-06-02 21:19:16.080 [info] > git rev-parse --show-toplevel [117ms]
2025-06-02 21:19:16.082 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 21:19:16.244 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [149ms]
2025-06-02 21:19:16.244 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [138ms]
2025-06-02 21:19:16.513 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [256ms]
2025-06-02 21:19:16.524 [info] > git config --get commit.template [292ms]
2025-06-02 21:19:16.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 21:19:17.189 [info] > git status -z -uall [90ms]
2025-06-02 21:19:17.190 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [84ms]
2025-06-02 21:19:17.485 [info] > git config --get --local branch.main.github-pr-owner-number [265ms]
2025-06-02 21:19:17.485 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 21:19:21.269 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ai/AIConfigurationSection.tsx [3514ms]
2025-06-02 21:20:03.774 [info] > git config --get commit.template [1ms]
2025-06-02 21:20:03.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:20:03.826 [info] > git status -z -uall [12ms]
2025-06-02 21:20:03.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 21:20:04.051 [info] > git fetch [302ms]
2025-06-02 21:20:04.051 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 21:20:04.064 [info] > git config --get commit.template [0ms]
2025-06-02 21:20:04.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 21:20:04.097 [info] > git status -z -uall [10ms]
2025-06-02 21:20:04.102 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 21:20:05.238 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [6ms]
2025-06-02 21:20:05.251 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [2ms]
2025-06-02 21:20:05.357 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [9ms]
2025-06-02 21:20:24.356 [info] > git config --get commit.template [4ms]
2025-06-02 21:20:24.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 21:20:24.366 [info] > git status -z -uall [5ms]
2025-06-02 21:20:24.367 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 21:20:35.160 [info] > git config --get commit.template [2ms]
2025-06-02 21:20:35.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 21:20:35.196 [info] > git status -z -uall [9ms]
2025-06-02 21:20:35.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 21:20:43.631 [info] > git config --get commit.template [20ms]
2025-06-02 21:20:43.633 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 21:20:43.664 [info] > git status -z -uall [15ms]
2025-06-02 21:20:43.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 21:21:12.007 [info] > git config --get commit.template [6ms]
2025-06-02 21:21:12.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:21:12.020 [info] > git status -z -uall [7ms]
2025-06-02 21:21:12.022 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 21:21:17.059 [info] > git config --get commit.template [20ms]
2025-06-02 21:21:17.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 21:21:17.089 [info] > git status -z -uall [13ms]
2025-06-02 21:21:17.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 21:21:22.108 [info] > git config --get commit.template [5ms]
2025-06-02 21:21:22.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 21:21:22.119 [info] > git status -z -uall [5ms]
2025-06-02 21:21:22.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
