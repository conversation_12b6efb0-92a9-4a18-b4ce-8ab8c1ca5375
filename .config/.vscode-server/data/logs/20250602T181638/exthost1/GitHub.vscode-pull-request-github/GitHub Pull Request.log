2025-06-02 18:16:49.992 [warning] /home/<USER>/.ssh/config: ENOENT: no such file or directory, open '/home/<USER>/.ssh/config'
2025-06-02 18:16:49.992 [info] [Activation] Extension version: 0.110.0
2025-06-02 18:16:51.075 [info] [Authentication] Creating hub for .com
2025-06-02 18:16:51.998 [info] [Activation] Looking for git repository
2025-06-02 18:16:51.998 [info] [Activation] Found 0 repositories during activation
2025-06-02 18:16:51.998 [info] [Activation] Git repository found, initializing review manager and pr tree view.
2025-06-02 18:16:52.002 [info] [GitAPI] Registering git provider
2025-06-02 18:16:52.277 [info] [Activation] Git initialization state changed: state=initialized
2025-06-02 18:16:52.277 [info] [Review+0] Validate state in progress
2025-06-02 18:16:52.277 [info] [Review+0] Validating state...
2025-06-02 18:16:52.461 [info] [FolderRepositoryManager+0] Found GitHub remote for folder /home/<USER>/workspace
2025-06-02 18:16:53.110 [info] [FolderRepositoryManager+0] Trying to use globalState for assignableUsers.
2025-06-02 18:16:53.400 [info] [Review+0] No matching pull request metadata found locally for current branch main
2025-06-02 18:16:53.461 [info] [Activation] Repo state for file:///home/<USER>/workspace changed.
2025-06-02 18:16:53.461 [info] [Activation] Repo file:///home/<USER>/workspace has already been setup.
2025-06-02 18:16:57.014 [info] [Review+0] No matching pull request metadata found on GitHub for current branch main
2025-06-02 18:16:57.040 [info] [FolderRepositoryManager+0] Using globalState assignableUsers for 1.
