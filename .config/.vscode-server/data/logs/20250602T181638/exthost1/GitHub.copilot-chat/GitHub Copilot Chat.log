2025-06-02 18:16:49.210 [info] Can't use the Electron fetcher in this environment.
2025-06-02 18:16:49.210 [info] Using the Node fetch fetcher.
2025-06-02 18:16:49.211 [info] Initializing Git extension service.
2025-06-02 18:16:49.211 [info] Successfully activated the vscode.git extension.
2025-06-02 18:16:49.211 [info] Enablement state of the vscode.git extension: true.
2025-06-02 18:16:49.211 [info] Successfully registered Git commit message provider.
2025-06-02 18:16:51.075 [info] Logged in as Chewy42
2025-06-02 18:16:53.759 [info] Got Copilot token for Chewy42
2025-06-02 18:16:57.107 [info] TypeScript server plugin activated.
2025-06-02 18:16:57.108 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-02 18:16:57.783 [info] Fetched model metadata in 4014ms ce8abc46-778b-4473-8808-a6d57033e397
2025-06-02 18:16:57.813 [info] activationBlocker from 'languageModelAccess' took for 11122ms
2025-06-02 18:16:58.310 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-02 18:16:58.428 [info] Registering default platform agent...
2025-06-02 18:16:58.872 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-02 18:16:58.872 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-02 18:16:58.872 [info] Successfully registered GitHub PR title and description provider.
2025-06-02 18:16:58.872 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-02 18:16:59.008 [info] BYOK: Copilot Chat known models list fetched successfully.
