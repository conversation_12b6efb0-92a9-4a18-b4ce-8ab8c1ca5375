2025-06-02 18:16:49.258 [info] [main] Log level: Info
2025-06-02 18:16:49.259 [info] [main] Validating found git in: "git"
2025-06-02 18:16:49.259 [info] [main] Using git "2.47.2" from "git"
2025-06-02 18:16:49.259 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 18:16:49.260 [info] > git rev-parse --show-toplevel [79ms]
2025-06-02 18:16:49.260 [info] > git rev-parse --git-dir --git-common-dir [11ms]
2025-06-02 18:16:49.260 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 18:16:49.260 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 18:16:49.260 [info] > git rev-parse --show-toplevel [114ms]
2025-06-02 18:16:49.260 [info] > git config --get commit.template [138ms]
2025-06-02 18:16:49.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [425ms]
2025-06-02 18:16:49.260 [info] > git fetch [625ms]
2025-06-02 18:16:49.260 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 18:16:49.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [111ms]
2025-06-02 18:16:49.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [124ms]
2025-06-02 18:16:49.260 [info] > git config --get commit.template [371ms]
2025-06-02 18:16:49.260 [info] > git config --get commit.template [384ms]
2025-06-02 18:16:49.260 [info] > git rev-parse --show-toplevel [397ms]
2025-06-02 18:16:49.260 [info] > git check-ignore -v -z --stdin [847ms]
2025-06-02 18:16:49.260 [info] > git config --get --local branch.main.vscode-merge-base [1188ms]
2025-06-02 18:16:49.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [256ms]
2025-06-02 18:16:49.260 [info] > git rev-parse --show-toplevel [292ms]
2025-06-02 18:16:49.260 [info] > git merge-base refs/heads/main refs/remotes/origin/main [117ms]
2025-06-02 18:16:49.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [178ms]
2025-06-02 18:16:49.260 [info] > git rev-parse --show-toplevel [72ms]
2025-06-02 18:16:49.260 [info] > git merge-base refs/heads/main refs/remotes/origin/main [88ms]
2025-06-02 18:16:49.272 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [64ms]
2025-06-02 18:16:49.895 [info] > git rev-parse --show-toplevel [627ms]
2025-06-02 18:16:49.895 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [642ms]
2025-06-02 18:16:50.015 [info] > git status -z -uall [47ms]
2025-06-02 18:16:50.016 [info] > git rev-parse --show-toplevel [72ms]
2025-06-02 18:16:50.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [44ms]
2025-06-02 18:16:50.135 [info] > git rev-parse --show-toplevel [47ms]
2025-06-02 18:16:50.684 [info] > git rev-parse --show-toplevel [458ms]
2025-06-02 18:16:50.986 [info] > git rev-parse --show-toplevel [188ms]
2025-06-02 18:16:51.059 [info] > git rev-parse --show-toplevel [28ms]
2025-06-02 18:16:51.240 [info] > git rev-parse --show-toplevel [17ms]
2025-06-02 18:16:51.431 [info] > git rev-parse --show-toplevel [179ms]
2025-06-02 18:16:51.837 [info] > git rev-parse --show-toplevel [140ms]
2025-06-02 18:16:52.003 [info] > git rev-parse --show-toplevel [155ms]
2025-06-02 18:16:52.241 [info] > git rev-parse --show-toplevel [108ms]
2025-06-02 18:16:52.244 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 18:16:52.389 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [123ms]
2025-06-02 18:16:52.406 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [129ms]
2025-06-02 18:16:52.557 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [118ms]
2025-06-02 18:16:52.558 [info] > git config --get commit.template [264ms]
2025-06-02 18:16:52.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [87ms]
2025-06-02 18:16:53.072 [info] > git status -z -uall [22ms]
2025-06-02 18:16:53.399 [info] > git config --get --local branch.main.github-pr-owner-number [275ms]
2025-06-02 18:16:53.399 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 18:16:53.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [395ms]
2025-06-02 18:16:53.567 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ai/AIConfigurationSection.tsx [178ms]
2025-06-02 18:16:53.751 [info] > git config --get commit.template [216ms]
2025-06-02 18:16:56.769 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2983ms]
2025-06-02 18:16:57.134 [info] > git status -z -uall [37ms]
2025-06-02 18:16:57.269 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [162ms]
2025-06-02 18:17:02.326 [info] > git config --get commit.template [13ms]
2025-06-02 18:17:02.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 18:17:02.370 [info] > git status -z -uall [11ms]
2025-06-02 18:17:02.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:17:07.395 [info] > git config --get commit.template [3ms]
2025-06-02 18:17:07.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:17:07.445 [info] > git status -z -uall [26ms]
2025-06-02 18:17:07.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:17:12.477 [info] > git config --get commit.template [8ms]
2025-06-02 18:17:12.496 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:17:12.525 [info] > git status -z -uall [12ms]
2025-06-02 18:17:12.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:17.542 [info] > git config --get commit.template [5ms]
2025-06-02 18:17:17.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:17:17.555 [info] > git status -z -uall [7ms]
2025-06-02 18:17:17.556 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:22.579 [info] > git config --get commit.template [12ms]
2025-06-02 18:17:22.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:17:22.591 [info] > git status -z -uall [5ms]
2025-06-02 18:17:22.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:17:27.611 [info] > git config --get commit.template [6ms]
2025-06-02 18:17:27.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:17:27.624 [info] > git status -z -uall [6ms]
2025-06-02 18:17:27.625 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:32.644 [info] > git config --get commit.template [8ms]
2025-06-02 18:17:32.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:17:32.664 [info] > git status -z -uall [11ms]
2025-06-02 18:17:32.665 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:17:37.686 [info] > git config --get commit.template [10ms]
2025-06-02 18:17:37.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:17:37.707 [info] > git status -z -uall [9ms]
2025-06-02 18:17:37.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:42.729 [info] > git config --get commit.template [8ms]
2025-06-02 18:17:42.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:17:42.742 [info] > git status -z -uall [6ms]
2025-06-02 18:17:42.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:47.764 [info] > git config --get commit.template [10ms]
2025-06-02 18:17:47.765 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:17:47.780 [info] > git status -z -uall [6ms]
2025-06-02 18:17:47.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:52.802 [info] > git config --get commit.template [9ms]
2025-06-02 18:17:52.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:17:52.821 [info] > git status -z -uall [9ms]
2025-06-02 18:17:52.823 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:17:57.840 [info] > git config --get commit.template [2ms]
2025-06-02 18:17:57.849 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:17:57.868 [info] > git status -z -uall [9ms]
2025-06-02 18:17:57.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:18:02.883 [info] > git config --get commit.template [1ms]
2025-06-02 18:18:02.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:18:02.916 [info] > git status -z -uall [10ms]
2025-06-02 18:18:02.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:07.938 [info] > git config --get commit.template [1ms]
2025-06-02 18:18:07.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:18:07.973 [info] > git status -z -uall [11ms]
2025-06-02 18:18:07.974 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:18:12.997 [info] > git config --get commit.template [11ms]
2025-06-02 18:18:13.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:18:13.015 [info] > git status -z -uall [6ms]
2025-06-02 18:18:13.017 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:18:18.049 [info] > git config --get commit.template [16ms]
2025-06-02 18:18:18.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 18:18:18.075 [info] > git status -z -uall [8ms]
2025-06-02 18:18:18.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:23.098 [info] > git config --get commit.template [6ms]
2025-06-02 18:18:23.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:18:23.188 [info] > git status -z -uall [79ms]
2025-06-02 18:18:23.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [71ms]
2025-06-02 18:18:28.211 [info] > git config --get commit.template [10ms]
2025-06-02 18:18:28.212 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:18:28.242 [info] > git status -z -uall [19ms]
2025-06-02 18:18:28.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:18:33.259 [info] > git config --get commit.template [5ms]
2025-06-02 18:18:33.947 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [689ms]
2025-06-02 18:18:33.964 [info] > git status -z -uall [6ms]
2025-06-02 18:18:33.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:18:38.985 [info] > git config --get commit.template [7ms]
2025-06-02 18:18:38.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:18:39.001 [info] > git status -z -uall [10ms]
2025-06-02 18:18:39.003 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:44.026 [info] > git config --get commit.template [8ms]
2025-06-02 18:18:44.028 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:18:44.046 [info] > git status -z -uall [10ms]
2025-06-02 18:18:44.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:49.066 [info] > git config --get commit.template [8ms]
2025-06-02 18:18:49.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:18:49.082 [info] > git status -z -uall [6ms]
2025-06-02 18:18:49.083 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:54.108 [info] > git config --get commit.template [11ms]
2025-06-02 18:18:54.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:18:54.127 [info] > git status -z -uall [10ms]
2025-06-02 18:18:54.128 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:18:59.160 [info] > git config --get commit.template [13ms]
2025-06-02 18:18:59.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:18:59.177 [info] > git status -z -uall [6ms]
2025-06-02 18:18:59.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:19:04.203 [info] > git config --get commit.template [13ms]
2025-06-02 18:19:04.205 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:04.218 [info] > git status -z -uall [6ms]
2025-06-02 18:19:04.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:19:09.258 [info] > git config --get commit.template [11ms]
2025-06-02 18:19:09.259 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:19:09.277 [info] > git status -z -uall [9ms]
2025-06-02 18:19:09.278 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:19:14.309 [info] > git config --get commit.template [14ms]
2025-06-02 18:19:14.310 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:14.333 [info] > git status -z -uall [12ms]
2025-06-02 18:19:14.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:19:19.356 [info] > git config --get commit.template [9ms]
2025-06-02 18:19:19.357 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:19:19.375 [info] > git status -z -uall [11ms]
2025-06-02 18:19:19.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:19:24.390 [info] > git config --get commit.template [1ms]
2025-06-02 18:19:24.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:24.425 [info] > git status -z -uall [14ms]
2025-06-02 18:19:24.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:19:29.454 [info] > git config --get commit.template [9ms]
2025-06-02 18:19:29.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:29.472 [info] > git status -z -uall [10ms]
2025-06-02 18:19:29.472 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:19:34.503 [info] > git config --get commit.template [13ms]
2025-06-02 18:19:34.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:34.529 [info] > git status -z -uall [14ms]
2025-06-02 18:19:34.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:19:39.568 [info] > git config --get commit.template [14ms]
2025-06-02 18:19:39.569 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:39.587 [info] > git status -z -uall [8ms]
2025-06-02 18:19:39.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:19:44.611 [info] > git config --get commit.template [1ms]
2025-06-02 18:19:44.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:19:44.656 [info] > git status -z -uall [18ms]
2025-06-02 18:19:44.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:19:49.676 [info] > git config --get commit.template [2ms]
2025-06-02 18:19:49.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:19:49.709 [info] > git status -z -uall [9ms]
2025-06-02 18:19:49.711 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:19:54.728 [info] > git config --get commit.template [5ms]
2025-06-02 18:19:54.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:19:54.738 [info] > git status -z -uall [4ms]
2025-06-02 18:19:54.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:19:59.760 [info] > git config --get commit.template [9ms]
2025-06-02 18:19:59.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:19:59.782 [info] > git status -z -uall [10ms]
2025-06-02 18:19:59.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:20:04.804 [info] > git config --get commit.template [6ms]
2025-06-02 18:20:04.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:20:04.819 [info] > git status -z -uall [7ms]
2025-06-02 18:20:04.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:25:13.263 [info] > git config --get commit.template [14ms]
2025-06-02 18:25:13.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:25:13.285 [info] > git status -z -uall [9ms]
2025-06-02 18:25:13.286 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:25:18.318 [info] > git config --get commit.template [14ms]
2025-06-02 18:25:18.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:25:18.336 [info] > git status -z -uall [8ms]
2025-06-02 18:25:18.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:25:23.360 [info] > git config --get commit.template [7ms]
2025-06-02 18:25:23.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:25:23.386 [info] > git status -z -uall [16ms]
2025-06-02 18:25:23.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:25:50.864 [info] > git config --get commit.template [14ms]
2025-06-02 18:25:50.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:25:50.886 [info] > git status -z -uall [11ms]
2025-06-02 18:25:50.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:25:55.923 [info] > git config --get commit.template [14ms]
2025-06-02 18:25:55.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:25:55.946 [info] > git status -z -uall [9ms]
2025-06-02 18:25:55.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:00.974 [info] > git config --get commit.template [10ms]
2025-06-02 18:26:00.975 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:26:00.993 [info] > git status -z -uall [9ms]
2025-06-02 18:26:00.994 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:06.022 [info] > git config --get commit.template [11ms]
2025-06-02 18:26:06.023 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:06.041 [info] > git status -z -uall [9ms]
2025-06-02 18:26:06.042 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:11.080 [info] > git config --get commit.template [18ms]
2025-06-02 18:26:11.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:11.110 [info] > git status -z -uall [17ms]
2025-06-02 18:26:11.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:16.148 [info] > git config --get commit.template [16ms]
2025-06-02 18:26:16.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:16.173 [info] > git status -z -uall [9ms]
2025-06-02 18:26:16.175 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:21.198 [info] > git config --get commit.template [7ms]
2025-06-02 18:26:21.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:26:21.262 [info] > git status -z -uall [56ms]
2025-06-02 18:26:21.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [50ms]
2025-06-02 18:26:26.290 [info] > git config --get commit.template [9ms]
2025-06-02 18:26:26.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:26.321 [info] > git status -z -uall [16ms]
2025-06-02 18:26:26.322 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:26:31.350 [info] > git config --get commit.template [12ms]
2025-06-02 18:26:31.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:31.375 [info] > git status -z -uall [12ms]
2025-06-02 18:26:31.376 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:26:36.406 [info] > git config --get commit.template [12ms]
2025-06-02 18:26:36.407 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:26:36.426 [info] > git status -z -uall [10ms]
2025-06-02 18:26:36.427 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:26:58.313 [info] > git config --get commit.template [2ms]
2025-06-02 18:26:58.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:26:58.344 [info] > git status -z -uall [13ms]
2025-06-02 18:26:58.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:27:03.377 [info] > git config --get commit.template [15ms]
2025-06-02 18:27:03.378 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:27:03.405 [info] > git status -z -uall [12ms]
2025-06-02 18:27:03.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:27:08.427 [info] > git config --get commit.template [1ms]
2025-06-02 18:27:08.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:27:08.469 [info] > git status -z -uall [12ms]
2025-06-02 18:27:08.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:27:26.103 [info] > git config --get commit.template [8ms]
2025-06-02 18:27:26.104 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:27:26.143 [info] > git status -z -uall [20ms]
2025-06-02 18:27:26.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:27:31.181 [info] > git config --get commit.template [15ms]
2025-06-02 18:27:31.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:27:31.206 [info] > git status -z -uall [14ms]
2025-06-02 18:27:31.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:27:36.240 [info] > git config --get commit.template [14ms]
2025-06-02 18:27:36.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:27:36.258 [info] > git status -z -uall [7ms]
2025-06-02 18:27:36.267 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 18:27:41.298 [info] > git config --get commit.template [13ms]
2025-06-02 18:27:41.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:27:41.337 [info] > git status -z -uall [15ms]
2025-06-02 18:27:41.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:29:35.063 [info] > git config --get commit.template [1ms]
2025-06-02 18:29:35.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:29:35.095 [info] > git status -z -uall [9ms]
2025-06-02 18:29:35.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:29:40.120 [info] > git config --get commit.template [9ms]
2025-06-02 18:29:40.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:29:40.137 [info] > git status -z -uall [8ms]
2025-06-02 18:29:40.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:29:45.169 [info] > git config --get commit.template [1ms]
2025-06-02 18:29:45.285 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [89ms]
2025-06-02 18:29:45.397 [info] > git status -z -uall [82ms]
2025-06-02 18:29:45.404 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [76ms]
2025-06-02 18:29:50.440 [info] > git config --get commit.template [14ms]
2025-06-02 18:29:50.442 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:29:50.467 [info] > git status -z -uall [15ms]
2025-06-02 18:29:50.469 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:29:55.503 [info] > git config --get commit.template [16ms]
2025-06-02 18:29:55.504 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:29:55.543 [info] > git status -z -uall [17ms]
2025-06-02 18:29:55.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-02 18:30:00.571 [info] > git config --get commit.template [10ms]
2025-06-02 18:30:00.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:30:00.592 [info] > git status -z -uall [11ms]
2025-06-02 18:30:00.593 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:30:05.630 [info] > git config --get commit.template [18ms]
2025-06-02 18:30:05.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:30:05.659 [info] > git status -z -uall [15ms]
2025-06-02 18:30:05.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:30:10.692 [info] > git config --get commit.template [15ms]
2025-06-02 18:30:10.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:30:10.723 [info] > git status -z -uall [16ms]
2025-06-02 18:30:10.724 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
