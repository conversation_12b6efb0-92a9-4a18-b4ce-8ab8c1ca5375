2025-06-02 17:45:22.056 [info] 




2025-06-02 17:45:22.077 [info] Extension host agent started.
2025-06-02 17:45:22.286 [info] Marked extension as removed github.copilot-1.326.0
2025-06-02 17:45:22.324 [error] [<unknown>][5a43c97b][ManagementConnection] Unknown reconnection token (never seen).
2025-06-02 17:45:22.331 [info] Deleted marked for removal extension from disk github.copilot /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
2025-06-02 17:45:28.821 [info] [<unknown>][24efdad0][ManagementConnection] New connection established.
2025-06-02 17:45:28.823 [info] [<unknown>][c9006d69][ExtensionHostConnection] New connection established.
2025-06-02 17:45:28.827 [info] [<unknown>][c9006d69][ExtensionHostConnection] <3393> Launched Extension Host Process.
2025-06-02 17:45:29.065 [info] ComputeTargetPlatform: linux-x64
2025-06-02 17:45:33.072 [info] ComputeTargetPlatform: linux-x64
2025-06-02 17:45:34.496 [info] Getting Manifest... augment.vscode-augment
2025-06-02 17:45:34.622 [info] Installing extension: augment.vscode-augment {"productVersion":{"version":"1.100.2","date":"2025-05-14T21:47:40.416Z"},"pinned":false,"operation":3,"isApplicationScoped":false,"donotVerifySignature":false,"context":{"clientTargetPlatform":"win32-x64"},"profileLocation":{"$mid":1,"fsPath":"/home/<USER>/.vscode-server/extensions/extensions.json","external":"file:///home/<USER>/.vscode-server/extensions/extensions.json","path":"/home/<USER>/.vscode-server/extensions/extensions.json","scheme":"file"}}
2025-06-02 17:45:37.136 [info] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1834ms.
2025-06-02 17:45:38.623 [info] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1: augment.vscode-augment
2025-06-02 17:45:38.663 [info] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1
2025-06-02 17:45:38.721 [info] Marked extension as removed augment.vscode-augment-0.467.1
2025-06-02 17:45:38.750 [info] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
2025-06-02 17:50:22.079 [info] New EH opened, aborting shutdown
2025-06-02 17:51:37.604 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 17:51:37.606 [error] Error: Unexpected SIGPIPE
    at process.<anonymous> (file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-main.js:190:1060)
    at process.emit (node:events:536:35)
2025-06-02 17:51:37.630 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 17:51:37.867 [error] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 17:52:23.520 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
2025-06-02 17:52:23.723 [error] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
