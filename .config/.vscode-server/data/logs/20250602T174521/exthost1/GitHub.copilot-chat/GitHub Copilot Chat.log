2025-06-02 17:45:37.510 [info] Can't use the Electron fetcher in this environment.
2025-06-02 17:45:37.510 [info] Using the Node fetch fetcher.
2025-06-02 17:45:37.510 [info] Initializing Git extension service.
2025-06-02 17:45:37.510 [info] Successfully activated the vscode.git extension.
2025-06-02 17:45:37.511 [info] Enablement state of the vscode.git extension: true.
2025-06-02 17:45:37.511 [info] Successfully registered Git commit message provider.
2025-06-02 17:45:39.363 [info] Logged in as Chewy42
2025-06-02 17:45:43.082 [info] TypeScript server plugin activated.
2025-06-02 17:45:43.082 [info] Registered TypeScript context provider with Copilot inline completions.
2025-06-02 17:45:43.291 [info] Got Copilot token for Chewy42
2025-06-02 17:45:43.807 [info] Fetched model metadata in 509ms ee0b3bdd-91a8-4d0c-aea7-b8f2ccec39d0
2025-06-02 17:45:43.823 [info] activationBlocker from 'languageModelAccess' took for 7531ms
2025-06-02 17:45:44.262 [info] copilot token chat_enabled: true, sku: free_educational
2025-06-02 17:45:44.280 [info] Registering default platform agent...
2025-06-02 17:45:44.498 [info] Successfully activated the GitHub.vscode-pull-request-github extension.
2025-06-02 17:45:44.498 [info] [githubTitleAndDescriptionProvider] Initializing GitHub PR title and description provider provider.
2025-06-02 17:45:44.498 [info] Successfully registered GitHub PR title and description provider.
2025-06-02 17:45:44.498 [info] Successfully registered GitHub PR reviewer comments provider.
2025-06-02 17:45:44.608 [info] BYOK: Copilot Chat known models list fetched successfully.
2025-06-02 18:00:46.326 [info] Fetched model metadata in 526ms a73ae30f-147a-4b35-ac5e-b73ffba141fc
