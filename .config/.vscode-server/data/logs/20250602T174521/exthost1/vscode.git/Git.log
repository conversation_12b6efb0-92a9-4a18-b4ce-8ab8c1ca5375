2025-06-02 17:45:36.419 [info] [main] Log level: Info
2025-06-02 17:45:36.420 [info] [main] Validating found git in: "git"
2025-06-02 17:45:36.420 [info] [main] Using git "2.47.2" from "git"
2025-06-02 17:45:36.420 [info] [Model][doInitialScan] Initial repository scan started
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [141ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --git-dir --git-common-dir [1489ms]
2025-06-02 17:45:36.420 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-02 17:45:36.420 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [530ms]
2025-06-02 17:45:36.420 [info] > git config --get commit.template [546ms]
2025-06-02 17:45:36.420 [info] > git fetch [616ms]
2025-06-02 17:45:36.420 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-02 17:45:36.420 [info] > git check-ignore -v -z --stdin [124ms]
2025-06-02 17:45:36.420 [info] > git config --get commit.template [103ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [112ms]
2025-06-02 17:45:36.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [713ms]
2025-06-02 17:45:36.420 [info] > git rev-parse --show-toplevel [364ms]
2025-06-02 17:45:36.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [311ms]
2025-06-02 17:45:36.520 [info] > git rev-parse --show-toplevel [246ms]
2025-06-02 17:45:36.560 [info] > git rev-parse --show-toplevel [32ms]
2025-06-02 17:45:36.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [63ms]
2025-06-02 17:45:37.741 [info] > git config --get --local branch.main.vscode-merge-base [1115ms]
2025-06-02 17:45:37.753 [info] > git rev-parse --show-toplevel [1141ms]
2025-06-02 17:45:37.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1168ms]
2025-06-02 17:45:37.763 [info] > git config --get commit.template [1196ms]
2025-06-02 17:45:37.817 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [65ms]
2025-06-02 17:45:37.836 [info] > git merge-base refs/heads/main refs/remotes/origin/main [14ms]
2025-06-02 17:45:37.864 [info] > git rev-parse --show-toplevel [87ms]
2025-06-02 17:45:37.864 [info] > git config --get --local branch.main.vscode-merge-base [101ms]
2025-06-02 17:45:38.239 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [395ms]
2025-06-02 17:45:38.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [408ms]
2025-06-02 17:45:38.260 [info] > git rev-parse --show-toplevel [384ms]
2025-06-02 17:45:38.260 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [391ms]
2025-06-02 17:45:38.324 [info] > git status -z -uall [22ms]
2025-06-02 17:45:38.325 [info] > git rev-parse --show-toplevel [50ms]
2025-06-02 17:45:38.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [42ms]
2025-06-02 17:45:38.527 [info] > git rev-parse --show-toplevel [188ms]
2025-06-02 17:45:39.215 [info] > git rev-parse --show-toplevel [520ms]
2025-06-02 17:45:39.466 [info] > git rev-parse --show-toplevel [225ms]
2025-06-02 17:45:39.576 [info] > git rev-parse --show-toplevel [95ms]
2025-06-02 17:45:40.207 [info] > git rev-parse --show-toplevel [620ms]
2025-06-02 17:45:40.390 [info] > git rev-parse --show-toplevel [13ms]
2025-06-02 17:45:40.392 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-02 17:45:40.650 [info] > git show --textconv :client/src/components/ai/AIConfigurationSection.tsx [245ms]
2025-06-02 17:45:40.651 [info] > git ls-files --stage -- client/src/components/ai/AIConfigurationSection.tsx [227ms]
2025-06-02 17:45:40.746 [info] > git cat-file -s 9866f6070f83511f5fc1ade6ff77310ecb057367 [63ms]
2025-06-02 17:45:40.747 [info] > git config --get commit.template [208ms]
2025-06-02 17:45:40.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [37ms]
2025-06-02 17:45:40.869 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [18ms]
2025-06-02 17:45:40.872 [info] > git status -z -uall [31ms]
2025-06-02 17:45:41.334 [info] > git config --get --local branch.main.github-pr-owner-number [241ms]
2025-06-02 17:45:41.334 [warning] [Git][config] git config failed: Failed to execute git
2025-06-02 17:45:43.084 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- client/src/components/ai/AIConfigurationSection.tsx [1757ms]
2025-06-02 17:45:48.476 [info] > git config --get commit.template [25ms]
2025-06-02 17:45:48.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 17:45:48.497 [info] > git status -z -uall [5ms]
2025-06-02 17:45:48.498 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:45:53.516 [info] > git config --get commit.template [6ms]
2025-06-02 17:45:53.517 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:45:53.529 [info] > git status -z -uall [6ms]
2025-06-02 17:45:53.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:45:58.566 [info] > git config --get commit.template [1ms]
2025-06-02 17:45:58.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:45:58.662 [info] > git status -z -uall [48ms]
2025-06-02 17:45:58.663 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:46:03.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:46:03.706 [info] > git config --get commit.template [21ms]
2025-06-02 17:46:03.729 [info] > git status -z -uall [13ms]
2025-06-02 17:46:03.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:46:09.728 [info] > git config --get commit.template [5ms]
2025-06-02 17:46:09.729 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:09.741 [info] > git status -z -uall [8ms]
2025-06-02 17:46:09.743 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:14.767 [info] > git config --get commit.template [11ms]
2025-06-02 17:46:14.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:14.782 [info] > git status -z -uall [7ms]
2025-06-02 17:46:14.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:19.801 [info] > git config --get commit.template [6ms]
2025-06-02 17:46:19.802 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:46:19.814 [info] > git status -z -uall [6ms]
2025-06-02 17:46:19.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:46:24.833 [info] > git config --get commit.template [6ms]
2025-06-02 17:46:24.834 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:24.848 [info] > git status -z -uall [7ms]
2025-06-02 17:46:24.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:26.715 [info] > git check-ignore -v -z --stdin [1ms]
2025-06-02 17:46:28.877 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-02 17:46:29.892 [info] > git config --get commit.template [5ms]
2025-06-02 17:46:29.896 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:46:29.906 [info] > git status -z -uall [5ms]
2025-06-02 17:46:29.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:34.921 [info] > git config --get commit.template [4ms]
2025-06-02 17:46:34.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:34.935 [info] > git status -z -uall [7ms]
2025-06-02 17:46:34.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:46:39.960 [info] > git config --get commit.template [12ms]
2025-06-02 17:46:39.972 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:46:39.990 [info] > git status -z -uall [9ms]
2025-06-02 17:46:39.993 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:46:45.007 [info] > git config --get commit.template [3ms]
2025-06-02 17:46:45.015 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:45.030 [info] > git status -z -uall [7ms]
2025-06-02 17:46:45.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:46:58.599 [info] > git config --get commit.template [8ms]
2025-06-02 17:46:58.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:46:58.614 [info] > git status -z -uall [9ms]
2025-06-02 17:46:58.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:03.628 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:03.629 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:03.638 [info] > git status -z -uall [5ms]
2025-06-02 17:47:03.639 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:08.687 [info] > git config --get commit.template [2ms]
2025-06-02 17:47:08.698 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 17:47:08.723 [info] > git status -z -uall [8ms]
2025-06-02 17:47:08.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:13.744 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:13.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:13.761 [info] > git status -z -uall [8ms]
2025-06-02 17:47:13.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:18.779 [info] > git config --get commit.template [5ms]
2025-06-02 17:47:18.780 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:18.797 [info] > git status -z -uall [10ms]
2025-06-02 17:47:18.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:47:23.809 [info] > git config --get commit.template [1ms]
2025-06-02 17:47:23.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:23.836 [info] > git status -z -uall [8ms]
2025-06-02 17:47:23.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:47:28.849 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:28.850 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:28.859 [info] > git status -z -uall [4ms]
2025-06-02 17:47:28.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:47:41.149 [info] > git config --get commit.template [11ms]
2025-06-02 17:47:41.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:47:41.167 [info] > git status -z -uall [8ms]
2025-06-02 17:47:41.169 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:46.182 [info] > git config --get commit.template [4ms]
2025-06-02 17:47:46.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:47:46.195 [info] > git status -z -uall [8ms]
2025-06-02 17:47:46.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:47:51.214 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:51.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:51.229 [info] > git status -z -uall [5ms]
2025-06-02 17:47:51.231 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:47:56.249 [info] > git config --get commit.template [7ms]
2025-06-02 17:47:56.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:47:56.262 [info] > git status -z -uall [5ms]
2025-06-02 17:47:56.264 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:01.283 [info] > git config --get commit.template [9ms]
2025-06-02 17:48:01.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:01.309 [info] > git status -z -uall [14ms]
2025-06-02 17:48:01.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:06.325 [info] > git config --get commit.template [4ms]
2025-06-02 17:48:06.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:06.336 [info] > git status -z -uall [5ms]
2025-06-02 17:48:06.337 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:11.356 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:11.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:11.371 [info] > git status -z -uall [6ms]
2025-06-02 17:48:11.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:16.384 [info] > git config --get commit.template [4ms]
2025-06-02 17:48:16.385 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:16.394 [info] > git status -z -uall [4ms]
2025-06-02 17:48:16.395 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:21.411 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:21.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:21.422 [info] > git status -z -uall [4ms]
2025-06-02 17:48:21.424 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:26.438 [info] > git config --get commit.template [2ms]
2025-06-02 17:48:26.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-06-02 17:48:26.482 [info] > git status -z -uall [7ms]
2025-06-02 17:48:26.483 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:31.500 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:31.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:31.514 [info] > git status -z -uall [6ms]
2025-06-02 17:48:31.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:48:38.146 [info] > git config --get commit.template [5ms]
2025-06-02 17:48:38.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:48:38.159 [info] > git status -z -uall [6ms]
2025-06-02 17:48:38.160 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:43.198 [info] > git config --get commit.template [19ms]
2025-06-02 17:48:43.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:43.218 [info] > git status -z -uall [9ms]
2025-06-02 17:48:43.220 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:48:50.725 [info] > git config --get commit.template [7ms]
2025-06-02 17:48:50.726 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:50.740 [info] > git status -z -uall [6ms]
2025-06-02 17:48:50.742 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:48:55.767 [info] > git config --get commit.template [7ms]
2025-06-02 17:48:55.768 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:48:55.780 [info] > git status -z -uall [5ms]
2025-06-02 17:48:55.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:49:13.182 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:13.183 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:13.197 [info] > git status -z -uall [7ms]
2025-06-02 17:49:13.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:19.188 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:19.190 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:19.202 [info] > git status -z -uall [6ms]
2025-06-02 17:49:19.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:49:24.223 [info] > git config --get commit.template [1ms]
2025-06-02 17:49:24.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:24.243 [info] > git status -z -uall [6ms]
2025-06-02 17:49:24.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:29.264 [info] > git config --get commit.template [9ms]
2025-06-02 17:49:29.265 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:29.277 [info] > git status -z -uall [5ms]
2025-06-02 17:49:29.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:34.297 [info] > git config --get commit.template [6ms]
2025-06-02 17:49:34.298 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:34.310 [info] > git status -z -uall [6ms]
2025-06-02 17:49:34.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:39.330 [info] > git config --get commit.template [8ms]
2025-06-02 17:49:39.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:39.350 [info] > git status -z -uall [10ms]
2025-06-02 17:49:39.351 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:44.366 [info] > git config --get commit.template [5ms]
2025-06-02 17:49:44.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:44.379 [info] > git status -z -uall [6ms]
2025-06-02 17:49:44.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:49.415 [info] > git config --get commit.template [11ms]
2025-06-02 17:49:49.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:49:49.428 [info] > git status -z -uall [6ms]
2025-06-02 17:49:49.430 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:54.441 [info] > git config --get commit.template [2ms]
2025-06-02 17:49:54.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:54.459 [info] > git status -z -uall [5ms]
2025-06-02 17:49:54.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:49:59.475 [info] > git config --get commit.template [0ms]
2025-06-02 17:49:59.489 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:49:59.506 [info] > git status -z -uall [7ms]
2025-06-02 17:49:59.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:50:04.527 [info] > git config --get commit.template [8ms]
2025-06-02 17:50:04.528 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:04.541 [info] > git status -z -uall [6ms]
2025-06-02 17:50:04.542 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:50:09.562 [info] > git config --get commit.template [7ms]
2025-06-02 17:50:09.563 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:09.580 [info] > git status -z -uall [8ms]
2025-06-02 17:50:09.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:50:14.615 [info] > git config --get commit.template [14ms]
2025-06-02 17:50:14.617 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:14.723 [info] > git status -z -uall [93ms]
2025-06-02 17:50:14.725 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [79ms]
2025-06-02 17:50:19.749 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:19.754 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:50:19.780 [info] > git status -z -uall [14ms]
2025-06-02 17:50:19.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:24.809 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:24.810 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:24.828 [info] > git status -z -uall [9ms]
2025-06-02 17:50:24.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:29.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:50:29.865 [info] > git config --get commit.template [17ms]
2025-06-02 17:50:29.898 [info] > git status -z -uall [18ms]
2025-06-02 17:50:29.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:50:34.950 [info] > git config --get commit.template [21ms]
2025-06-02 17:50:34.955 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:50:34.983 [info] > git status -z -uall [13ms]
2025-06-02 17:50:34.987 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:50:40.028 [info] > git config --get commit.template [28ms]
2025-06-02 17:50:40.031 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-06-02 17:50:40.060 [info] > git status -z -uall [12ms]
2025-06-02 17:50:40.063 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:50:45.081 [info] > git config --get commit.template [1ms]
2025-06-02 17:50:45.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:45.140 [info] > git status -z -uall [13ms]
2025-06-02 17:50:45.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:50:50.174 [info] > git config --get commit.template [13ms]
2025-06-02 17:50:50.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:50.203 [info] > git status -z -uall [15ms]
2025-06-02 17:50:50.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:50:55.237 [info] > git config --get commit.template [11ms]
2025-06-02 17:50:55.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:50:55.265 [info] > git status -z -uall [14ms]
2025-06-02 17:50:55.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:00.301 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:00.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:00.334 [info] > git status -z -uall [12ms]
2025-06-02 17:51:00.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:05.360 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:05.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:05.374 [info] > git status -z -uall [5ms]
2025-06-02 17:51:05.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:51:10.400 [info] > git config --get commit.template [10ms]
2025-06-02 17:51:10.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:10.419 [info] > git status -z -uall [9ms]
2025-06-02 17:51:10.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:15.442 [info] > git config --get commit.template [7ms]
2025-06-02 17:51:15.444 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:15.471 [info] > git status -z -uall [18ms]
2025-06-02 17:51:15.476 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:51:20.494 [info] > git config --get commit.template [3ms]
2025-06-02 17:51:20.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:20.525 [info] > git status -z -uall [9ms]
2025-06-02 17:51:20.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:51:25.561 [info] > git config --get commit.template [13ms]
2025-06-02 17:51:25.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:25.592 [info] > git status -z -uall [18ms]
2025-06-02 17:51:25.599 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 17:51:30.631 [info] > git config --get commit.template [10ms]
2025-06-02 17:51:30.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:30.652 [info] > git status -z -uall [11ms]
2025-06-02 17:51:30.655 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:39.884 [info] > git config --get commit.template [3ms]
2025-06-02 17:51:39.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:51:39.923 [info] > git status -z -uall [9ms]
2025-06-02 17:51:39.925 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:51:44.955 [info] > git config --get commit.template [11ms]
2025-06-02 17:51:44.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:51:44.974 [info] > git status -z -uall [9ms]
2025-06-02 17:51:44.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:51:50.001 [info] > git config --get commit.template [9ms]
2025-06-02 17:51:50.035 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:51:50.079 [info] > git status -z -uall [23ms]
2025-06-02 17:51:50.079 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:51:55.106 [info] > git config --get commit.template [15ms]
2025-06-02 17:51:55.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:51:55.125 [info] > git status -z -uall [8ms]
2025-06-02 17:51:55.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:52:31.740 [info] > git config --get commit.template [7ms]
2025-06-02 17:52:31.741 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:52:31.759 [info] > git status -z -uall [10ms]
2025-06-02 17:52:31.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:53:32.678 [info] > git config --get commit.template [9ms]
2025-06-02 17:53:32.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:32.696 [info] > git status -z -uall [10ms]
2025-06-02 17:53:32.696 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:53:37.712 [info] > git config --get commit.template [2ms]
2025-06-02 17:53:37.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:53:37.754 [info] > git status -z -uall [17ms]
2025-06-02 17:53:37.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:53:42.778 [info] > git config --get commit.template [1ms]
2025-06-02 17:53:42.795 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:42.826 [info] > git status -z -uall [17ms]
2025-06-02 17:53:42.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:53:47.852 [info] > git config --get commit.template [10ms]
2025-06-02 17:53:47.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:53:47.873 [info] > git status -z -uall [11ms]
2025-06-02 17:53:47.876 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:53:52.896 [info] > git config --get commit.template [9ms]
2025-06-02 17:53:52.897 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:53:52.915 [info] > git status -z -uall [8ms]
2025-06-02 17:53:52.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:53:57.934 [info] > git config --get commit.template [1ms]
2025-06-02 17:53:57.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:53:57.963 [info] > git status -z -uall [11ms]
2025-06-02 17:53:57.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:03.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:03.014 [info] > git config --get commit.template [31ms]
2025-06-02 17:54:03.045 [info] > git status -z -uall [17ms]
2025-06-02 17:54:03.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:08.082 [info] > git config --get commit.template [15ms]
2025-06-02 17:54:08.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:08.117 [info] > git status -z -uall [9ms]
2025-06-02 17:54:08.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:13.134 [info] > git config --get commit.template [0ms]
2025-06-02 17:54:13.151 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:13.178 [info] > git status -z -uall [17ms]
2025-06-02 17:54:13.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 17:54:18.211 [info] > git config --get commit.template [16ms]
2025-06-02 17:54:18.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:18.241 [info] > git status -z -uall [14ms]
2025-06-02 17:54:18.247 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:54:23.281 [info] > git config --get commit.template [23ms]
2025-06-02 17:54:23.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-02 17:54:23.301 [info] > git status -z -uall [7ms]
2025-06-02 17:54:23.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:54:28.336 [info] > git config --get commit.template [11ms]
2025-06-02 17:54:28.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 17:54:28.377 [info] > git status -z -uall [11ms]
2025-06-02 17:54:28.378 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:54:34.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:54:34.486 [info] > git config --get commit.template [17ms]
2025-06-02 17:54:34.508 [info] > git status -z -uall [12ms]
2025-06-02 17:54:34.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:39.542 [info] > git config --get commit.template [15ms]
2025-06-02 17:54:39.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:39.569 [info] > git status -z -uall [10ms]
2025-06-02 17:54:39.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:54:44.588 [info] > git config --get commit.template [1ms]
2025-06-02 17:54:44.602 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:54:44.620 [info] > git status -z -uall [8ms]
2025-06-02 17:54:44.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:49.640 [info] > git config --get commit.template [2ms]
2025-06-02 17:54:49.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:49.683 [info] > git status -z -uall [9ms]
2025-06-02 17:54:49.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [62ms]
2025-06-02 17:54:54.761 [info] > git config --get commit.template [0ms]
2025-06-02 17:54:54.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:54.790 [info] > git status -z -uall [8ms]
2025-06-02 17:54:54.792 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:54:59.882 [info] > git config --get commit.template [73ms]
2025-06-02 17:54:59.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:54:59.920 [info] > git status -z -uall [16ms]
2025-06-02 17:54:59.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 17:55:04.945 [info] > git config --get commit.template [9ms]
2025-06-02 17:55:04.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:04.960 [info] > git status -z -uall [7ms]
2025-06-02 17:55:04.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:09.980 [info] > git config --get commit.template [4ms]
2025-06-02 17:55:09.997 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-02 17:55:10.022 [info] > git status -z -uall [9ms]
2025-06-02 17:55:10.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:55:15.052 [info] > git config --get commit.template [10ms]
2025-06-02 17:55:15.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:55:15.079 [info] > git status -z -uall [13ms]
2025-06-02 17:55:15.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:21.632 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:21.647 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:21.674 [info] > git status -z -uall [14ms]
2025-06-02 17:55:21.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:55:26.693 [info] > git config --get commit.template [1ms]
2025-06-02 17:55:26.708 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:55:26.733 [info] > git status -z -uall [11ms]
2025-06-02 17:55:26.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:31.757 [info] > git config --get commit.template [8ms]
2025-06-02 17:55:31.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 17:55:31.779 [info] > git status -z -uall [9ms]
2025-06-02 17:55:31.784 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 17:55:36.809 [info] > git config --get commit.template [8ms]
2025-06-02 17:55:36.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:55:36.828 [info] > git status -z -uall [9ms]
2025-06-02 17:55:36.831 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:55:41.898 [info] > git config --get commit.template [19ms]
2025-06-02 17:55:41.898 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 17:55:41.915 [info] > git status -z -uall [7ms]
2025-06-02 17:55:41.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:55:47.070 [info] > git config --get commit.template [26ms]
2025-06-02 17:55:47.076 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:55:47.149 [info] > git status -z -uall [40ms]
2025-06-02 17:55:47.149 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:55:52.168 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:52.180 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:55:52.210 [info] > git status -z -uall [11ms]
2025-06-02 17:55:52.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 17:55:57.233 [info] > git config --get commit.template [2ms]
2025-06-02 17:55:57.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 17:55:57.279 [info] > git status -z -uall [21ms]
2025-06-02 17:55:57.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 17:56:02.299 [info] > git config --get commit.template [2ms]
2025-06-02 17:56:02.332 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 17:56:02.369 [info] > git status -z -uall [17ms]
2025-06-02 17:56:02.372 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:07.389 [info] > git config --get commit.template [1ms]
2025-06-02 17:56:07.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:56:07.456 [info] > git status -z -uall [16ms]
2025-06-02 17:56:07.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:56:12.576 [info] > git config --get commit.template [2ms]
2025-06-02 17:56:12.592 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:56:12.643 [info] > git status -z -uall [22ms]
2025-06-02 17:56:12.646 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:17.665 [info] > git config --get commit.template [1ms]
2025-06-02 17:56:17.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 17:56:17.695 [info] > git status -z -uall [7ms]
2025-06-02 17:56:17.698 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 17:56:22.749 [info] > git config --get commit.template [27ms]
2025-06-02 17:56:22.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:56:22.802 [info] > git status -z -uall [18ms]
2025-06-02 17:56:22.809 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-06-02 17:56:55.957 [info] > git config --get commit.template [12ms]
2025-06-02 17:56:55.958 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:56:55.981 [info] > git status -z -uall [9ms]
2025-06-02 17:56:55.984 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:57:01.159 [info] > git config --get commit.template [4ms]
2025-06-02 17:57:01.185 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 17:57:01.215 [info] > git status -z -uall [12ms]
2025-06-02 17:57:01.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 17:58:55.721 [info] > git config --get commit.template [10ms]
2025-06-02 17:58:55.724 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 17:58:55.754 [info] > git status -z -uall [11ms]
2025-06-02 17:58:55.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:00:17.317 [info] > git config --get commit.template [17ms]
2025-06-02 18:00:17.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:00:17.344 [info] > git status -z -uall [13ms]
2025-06-02 18:00:17.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 18:00:35.604 [info] > git config --get commit.template [10ms]
2025-06-02 18:00:35.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:35.625 [info] > git status -z -uall [12ms]
2025-06-02 18:00:35.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:00:44.617 [info] > git config --get commit.template [1ms]
2025-06-02 18:00:44.628 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:44.650 [info] > git status -z -uall [9ms]
2025-06-02 18:00:44.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:00:49.679 [info] > git config --get commit.template [10ms]
2025-06-02 18:00:49.680 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:00:49.704 [info] > git status -z -uall [11ms]
2025-06-02 18:00:49.708 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:00:54.723 [info] > git config --get commit.template [1ms]
2025-06-02 18:00:54.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:54.751 [info] > git status -z -uall [6ms]
2025-06-02 18:00:54.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:00:59.780 [info] > git config --get commit.template [11ms]
2025-06-02 18:00:59.781 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:00:59.798 [info] > git status -z -uall [9ms]
2025-06-02 18:00:59.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:01:04.819 [info] > git config --get commit.template [7ms]
2025-06-02 18:01:04.821 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:04.843 [info] > git status -z -uall [10ms]
2025-06-02 18:01:04.844 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:01:09.864 [info] > git config --get commit.template [2ms]
2025-06-02 18:01:09.877 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:09.893 [info] > git status -z -uall [7ms]
2025-06-02 18:01:09.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:14.923 [info] > git config --get commit.template [12ms]
2025-06-02 18:01:14.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:01:14.942 [info] > git status -z -uall [9ms]
2025-06-02 18:01:14.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:01:19.956 [info] > git config --get commit.template [1ms]
2025-06-02 18:01:19.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:19.990 [info] > git status -z -uall [12ms]
2025-06-02 18:01:19.990 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:25.015 [info] > git config --get commit.template [10ms]
2025-06-02 18:01:25.016 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:01:25.050 [info] > git status -z -uall [13ms]
2025-06-02 18:01:25.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:30.077 [info] > git config --get commit.template [11ms]
2025-06-02 18:01:30.079 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:30.095 [info] > git status -z -uall [8ms]
2025-06-02 18:01:30.100 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:01:35.111 [info] > git config --get commit.template [0ms]
2025-06-02 18:01:35.121 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:35.138 [info] > git status -z -uall [11ms]
2025-06-02 18:01:35.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:01:40.158 [info] > git config --get commit.template [7ms]
2025-06-02 18:01:40.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:40.181 [info] > git status -z -uall [8ms]
2025-06-02 18:01:40.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:45.206 [info] > git config --get commit.template [9ms]
2025-06-02 18:01:45.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:01:45.224 [info] > git status -z -uall [8ms]
2025-06-02 18:01:45.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:01:50.245 [info] > git config --get commit.template [9ms]
2025-06-02 18:01:50.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:01:50.270 [info] > git status -z -uall [10ms]
2025-06-02 18:01:50.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:01:55.294 [info] > git config --get commit.template [1ms]
2025-06-02 18:01:55.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 18:01:55.352 [info] > git status -z -uall [14ms]
2025-06-02 18:01:55.355 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:00.375 [info] > git config --get commit.template [1ms]
2025-06-02 18:02:00.390 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:00.406 [info] > git status -z -uall [8ms]
2025-06-02 18:02:00.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:02:06.629 [info] > git config --get commit.template [2ms]
2025-06-02 18:02:06.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:06.655 [info] > git status -z -uall [7ms]
2025-06-02 18:02:06.657 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:11.681 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:11.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:02:11.709 [info] > git status -z -uall [10ms]
2025-06-02 18:02:11.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:16.737 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:16.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:16.756 [info] > git status -z -uall [9ms]
2025-06-02 18:02:16.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:21.775 [info] > git config --get commit.template [0ms]
2025-06-02 18:02:21.787 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:21.813 [info] > git status -z -uall [13ms]
2025-06-02 18:02:21.816 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:26.842 [info] > git config --get commit.template [10ms]
2025-06-02 18:02:26.844 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:02:26.882 [info] > git status -z -uall [16ms]
2025-06-02 18:02:26.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:31.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:31.910 [info] > git config --get commit.template [9ms]
2025-06-02 18:02:31.932 [info] > git status -z -uall [10ms]
2025-06-02 18:02:31.942 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-06-02 18:02:36.957 [info] > git config --get commit.template [0ms]
2025-06-02 18:02:36.971 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:02:36.996 [info] > git status -z -uall [15ms]
2025-06-02 18:02:36.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:02:42.018 [info] > git config --get commit.template [1ms]
2025-06-02 18:02:42.100 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [69ms]
2025-06-02 18:02:42.129 [info] > git status -z -uall [13ms]
2025-06-02 18:02:42.130 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:02:47.157 [info] > git config --get commit.template [11ms]
2025-06-02 18:02:47.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:47.190 [info] > git status -z -uall [15ms]
2025-06-02 18:02:47.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:02:52.210 [info] > git config --get commit.template [2ms]
2025-06-02 18:02:52.219 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:02:52.240 [info] > git status -z -uall [13ms]
2025-06-02 18:02:52.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:02:57.274 [info] > git config --get commit.template [11ms]
2025-06-02 18:02:57.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:02:57.300 [info] > git status -z -uall [15ms]
2025-06-02 18:02:57.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:03:02.326 [info] > git config --get commit.template [1ms]
2025-06-02 18:03:02.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 18:03:02.401 [info] > git status -z -uall [29ms]
2025-06-02 18:03:02.402 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:03:07.424 [info] > git config --get commit.template [0ms]
2025-06-02 18:03:07.436 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:03:07.456 [info] > git status -z -uall [12ms]
2025-06-02 18:03:07.459 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:03:12.485 [info] > git config --get commit.template [2ms]
2025-06-02 18:03:12.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:12.526 [info] > git status -z -uall [12ms]
2025-06-02 18:03:12.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:03:17.555 [info] > git config --get commit.template [10ms]
2025-06-02 18:03:17.557 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:03:17.585 [info] > git status -z -uall [14ms]
2025-06-02 18:03:17.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:03:22.614 [info] > git config --get commit.template [10ms]
2025-06-02 18:03:22.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:03:22.658 [info] > git status -z -uall [19ms]
2025-06-02 18:03:22.661 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:03:27.693 [info] > git config --get commit.template [12ms]
2025-06-02 18:03:27.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:27.732 [info] > git status -z -uall [22ms]
2025-06-02 18:03:27.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-06-02 18:03:32.752 [info] > git config --get commit.template [1ms]
2025-06-02 18:03:32.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:32.842 [info] > git status -z -uall [25ms]
2025-06-02 18:03:32.849 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 18:03:37.892 [info] > git config --get commit.template [13ms]
2025-06-02 18:03:37.894 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:03:37.929 [info] > git status -z -uall [18ms]
2025-06-02 18:03:37.930 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:03:42.950 [info] > git config --get commit.template [2ms]
2025-06-02 18:03:42.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:42.994 [info] > git status -z -uall [15ms]
2025-06-02 18:03:42.994 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:03:51.880 [info] > git config --get commit.template [7ms]
2025-06-02 18:03:51.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:03:51.925 [info] > git status -z -uall [16ms]
2025-06-02 18:03:51.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:03:56.958 [info] > git config --get commit.template [5ms]
2025-06-02 18:03:56.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:03:57.138 [info] > git status -z -uall [118ms]
2025-06-02 18:03:57.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [94ms]
2025-06-02 18:04:02.179 [info] > git config --get commit.template [20ms]
2025-06-02 18:04:02.181 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:04:02.222 [info] > git status -z -uall [15ms]
2025-06-02 18:04:02.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:04:07.253 [info] > git config --get commit.template [0ms]
2025-06-02 18:04:07.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:04:07.318 [info] > git status -z -uall [26ms]
2025-06-02 18:04:07.319 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:04:12.357 [info] > git config --get commit.template [15ms]
2025-06-02 18:04:12.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:04:12.393 [info] > git status -z -uall [17ms]
2025-06-02 18:04:12.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 18:04:17.466 [info] > git config --get commit.template [44ms]
2025-06-02 18:04:17.467 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:04:17.521 [info] > git status -z -uall [29ms]
2025-06-02 18:04:17.524 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:04:22.576 [info] > git config --get commit.template [19ms]
2025-06-02 18:04:22.579 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:04:22.621 [info] > git status -z -uall [19ms]
2025-06-02 18:04:22.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:04:27.698 [info] > git config --get commit.template [25ms]
2025-06-02 18:04:27.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-06-02 18:04:27.901 [info] > git status -z -uall [99ms]
2025-06-02 18:04:27.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [38ms]
2025-06-02 18:04:32.937 [info] > git config --get commit.template [3ms]
2025-06-02 18:04:32.964 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:04:33.018 [info] > git status -z -uall [32ms]
2025-06-02 18:04:33.019 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:04:38.066 [info] > git config --get commit.template [21ms]
2025-06-02 18:04:38.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:04:38.130 [info] > git status -z -uall [29ms]
2025-06-02 18:04:38.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 18:04:43.150 [info] > git config --get commit.template [0ms]
2025-06-02 18:04:43.161 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:04:43.200 [info] > git status -z -uall [24ms]
2025-06-02 18:04:43.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:04:48.263 [info] > git config --get commit.template [0ms]
2025-06-02 18:04:48.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 18:04:48.409 [info] > git status -z -uall [94ms]
2025-06-02 18:04:48.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [82ms]
2025-06-02 18:04:53.444 [info] > git config --get commit.template [12ms]
2025-06-02 18:04:53.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:04:53.478 [info] > git status -z -uall [16ms]
2025-06-02 18:04:53.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:04:58.497 [info] > git config --get commit.template [1ms]
2025-06-02 18:04:58.509 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:04:58.532 [info] > git status -z -uall [13ms]
2025-06-02 18:04:58.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:05:03.573 [info] > git config --get commit.template [20ms]
2025-06-02 18:05:03.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-02 18:05:03.604 [info] > git status -z -uall [13ms]
2025-06-02 18:05:03.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:05:08.628 [info] > git config --get commit.template [9ms]
2025-06-02 18:05:08.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:05:08.654 [info] > git status -z -uall [16ms]
2025-06-02 18:05:08.654 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:05:13.686 [info] > git config --get commit.template [16ms]
2025-06-02 18:05:13.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:05:13.719 [info] > git status -z -uall [16ms]
2025-06-02 18:05:13.722 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:05:18.760 [info] > git config --get commit.template [2ms]
2025-06-02 18:05:18.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:05:18.842 [info] > git status -z -uall [36ms]
2025-06-02 18:05:18.843 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:05:23.883 [info] > git config --get commit.template [7ms]
2025-06-02 18:05:23.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:05:23.937 [info] > git status -z -uall [19ms]
2025-06-02 18:05:23.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-02 18:05:28.985 [info] > git config --get commit.template [16ms]
2025-06-02 18:05:28.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:05:29.035 [info] > git status -z -uall [36ms]
2025-06-02 18:05:29.035 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:05:34.058 [info] > git config --get commit.template [2ms]
2025-06-02 18:05:34.078 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:05:34.119 [info] > git status -z -uall [24ms]
2025-06-02 18:05:34.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:05:39.144 [info] > git config --get commit.template [1ms]
2025-06-02 18:05:39.176 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:05:39.315 [info] > git status -z -uall [119ms]
2025-06-02 18:05:39.317 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [90ms]
2025-06-02 18:05:44.338 [info] > git config --get commit.template [0ms]
2025-06-02 18:05:44.355 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:05:44.389 [info] > git status -z -uall [18ms]
2025-06-02 18:05:44.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:05:49.415 [info] > git config --get commit.template [2ms]
2025-06-02 18:05:49.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:05:49.452 [info] > git status -z -uall [11ms]
2025-06-02 18:05:49.455 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:05:54.541 [info] > git config --get commit.template [13ms]
2025-06-02 18:05:54.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:05:54.568 [info] > git status -z -uall [12ms]
2025-06-02 18:05:54.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:05:59.596 [info] > git config --get commit.template [8ms]
2025-06-02 18:05:59.600 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:05:59.635 [info] > git status -z -uall [20ms]
2025-06-02 18:05:59.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:06:04.679 [info] > git config --get commit.template [18ms]
2025-06-02 18:06:04.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:06:04.712 [info] > git status -z -uall [17ms]
2025-06-02 18:06:04.714 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:06:10.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 18:06:10.776 [info] > git config --get commit.template [21ms]
2025-06-02 18:06:10.818 [info] > git status -z -uall [18ms]
2025-06-02 18:06:10.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:06:15.865 [info] > git config --get commit.template [12ms]
2025-06-02 18:06:15.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:06:15.935 [info] > git status -z -uall [19ms]
2025-06-02 18:06:15.935 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:06:20.959 [info] > git config --get commit.template [2ms]
2025-06-02 18:06:20.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:06:21.013 [info] > git status -z -uall [17ms]
2025-06-02 18:06:21.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:06:26.048 [info] > git config --get commit.template [15ms]
2025-06-02 18:06:26.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-06-02 18:06:26.081 [info] > git status -z -uall [12ms]
2025-06-02 18:06:26.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:06:31.108 [info] > git config --get commit.template [12ms]
2025-06-02 18:06:31.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:06:31.136 [info] > git status -z -uall [9ms]
2025-06-02 18:06:31.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:06:36.231 [info] > git config --get commit.template [18ms]
2025-06-02 18:06:36.231 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:06:36.249 [info] > git status -z -uall [8ms]
2025-06-02 18:06:36.252 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:06:41.298 [info] > git config --get commit.template [31ms]
2025-06-02 18:06:41.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:06:41.334 [info] > git status -z -uall [17ms]
2025-06-02 18:06:41.335 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:06:46.373 [info] > git config --get commit.template [18ms]
2025-06-02 18:06:46.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:06:46.404 [info] > git status -z -uall [16ms]
2025-06-02 18:06:46.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:06:51.445 [info] > git config --get commit.template [15ms]
2025-06-02 18:06:51.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:06:51.478 [info] > git status -z -uall [15ms]
2025-06-02 18:06:51.481 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:06:56.641 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [113ms]
2025-06-02 18:06:56.641 [info] > git config --get commit.template [142ms]
2025-06-02 18:06:56.672 [info] > git status -z -uall [17ms]
2025-06-02 18:06:56.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:07:01.694 [info] > git config --get commit.template [2ms]
2025-06-02 18:07:01.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:07:01.736 [info] > git status -z -uall [12ms]
2025-06-02 18:07:01.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:06.764 [info] > git config --get commit.template [12ms]
2025-06-02 18:07:06.766 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:07:06.788 [info] > git status -z -uall [10ms]
2025-06-02 18:07:06.789 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:07:11.810 [info] > git config --get commit.template [7ms]
2025-06-02 18:07:11.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:07:11.836 [info] > git status -z -uall [11ms]
2025-06-02 18:07:11.838 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:16.866 [info] > git config --get commit.template [11ms]
2025-06-02 18:07:16.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:07:16.904 [info] > git status -z -uall [15ms]
2025-06-02 18:07:16.905 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:21.945 [info] > git config --get commit.template [20ms]
2025-06-02 18:07:21.946 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:07:21.969 [info] > git status -z -uall [12ms]
2025-06-02 18:07:21.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:27.002 [info] > git config --get commit.template [12ms]
2025-06-02 18:07:27.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:07:27.030 [info] > git status -z -uall [15ms]
2025-06-02 18:07:27.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:07:35.165 [info] > git config --get commit.template [11ms]
2025-06-02 18:07:35.166 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:07:35.198 [info] > git status -z -uall [19ms]
2025-06-02 18:07:35.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:40.223 [info] > git config --get commit.template [8ms]
2025-06-02 18:07:40.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:07:40.248 [info] > git status -z -uall [12ms]
2025-06-02 18:07:40.249 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:07:45.280 [info] > git config --get commit.template [8ms]
2025-06-02 18:07:45.281 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:07:45.312 [info] > git status -z -uall [11ms]
2025-06-02 18:07:45.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:50.363 [info] > git config --get commit.template [21ms]
2025-06-02 18:07:50.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:07:50.397 [info] > git status -z -uall [12ms]
2025-06-02 18:07:50.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:07:55.434 [info] > git config --get commit.template [18ms]
2025-06-02 18:07:55.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:07:55.459 [info] > git status -z -uall [9ms]
2025-06-02 18:07:55.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:03.265 [info] > git config --get commit.template [90ms]
2025-06-02 18:08:03.290 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 18:08:03.319 [info] > git status -z -uall [12ms]
2025-06-02 18:08:03.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:08.342 [info] > git config --get commit.template [10ms]
2025-06-02 18:08:08.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:08.380 [info] > git status -z -uall [19ms]
2025-06-02 18:08:08.383 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:13.416 [info] > git config --get commit.template [13ms]
2025-06-02 18:08:13.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:08:13.449 [info] > git status -z -uall [16ms]
2025-06-02 18:08:13.451 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:08:18.474 [info] > git config --get commit.template [3ms]
2025-06-02 18:08:18.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:18.518 [info] > git status -z -uall [12ms]
2025-06-02 18:08:18.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:23.539 [info] > git config --get commit.template [0ms]
2025-06-02 18:08:23.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:08:23.584 [info] > git status -z -uall [9ms]
2025-06-02 18:08:23.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:08:28.625 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:08:28.625 [info] > git config --get commit.template [14ms]
2025-06-02 18:08:28.663 [info] > git status -z -uall [15ms]
2025-06-02 18:08:28.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:08:33.693 [info] > git config --get commit.template [10ms]
2025-06-02 18:08:33.694 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:33.718 [info] > git status -z -uall [11ms]
2025-06-02 18:08:33.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:08:38.812 [info] > git config --get commit.template [69ms]
2025-06-02 18:08:38.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:38.859 [info] > git status -z -uall [16ms]
2025-06-02 18:08:38.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:43.879 [info] > git config --get commit.template [1ms]
2025-06-02 18:08:43.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:43.932 [info] > git status -z -uall [16ms]
2025-06-02 18:08:43.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:08:48.957 [info] > git config --get commit.template [3ms]
2025-06-02 18:08:48.977 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:49.004 [info] > git status -z -uall [12ms]
2025-06-02 18:08:49.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:08:54.051 [info] > git config --get commit.template [26ms]
2025-06-02 18:08:54.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:08:54.085 [info] > git status -z -uall [18ms]
2025-06-02 18:08:54.087 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:08:59.112 [info] > git config --get commit.template [1ms]
2025-06-02 18:08:59.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:08:59.169 [info] > git status -z -uall [13ms]
2025-06-02 18:08:59.173 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:09:04.214 [info] > git config --get commit.template [19ms]
2025-06-02 18:09:04.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:09:04.253 [info] > git status -z -uall [15ms]
2025-06-02 18:09:04.255 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:09:09.373 [info] > git config --get commit.template [26ms]
2025-06-02 18:09:09.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-06-02 18:09:09.417 [info] > git status -z -uall [24ms]
2025-06-02 18:09:09.420 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:09:14.448 [info] > git config --get commit.template [11ms]
2025-06-02 18:09:14.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:09:14.480 [info] > git status -z -uall [13ms]
2025-06-02 18:09:14.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:09:19.508 [info] > git config --get commit.template [1ms]
2025-06-02 18:09:19.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:09:19.590 [info] > git status -z -uall [35ms]
2025-06-02 18:09:19.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-06-02 18:09:24.674 [info] > git config --get commit.template [6ms]
2025-06-02 18:09:24.687 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:09:24.704 [info] > git status -z -uall [9ms]
2025-06-02 18:09:24.705 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:09:29.723 [info] > git config --get commit.template [2ms]
2025-06-02 18:09:29.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:09:29.762 [info] > git status -z -uall [12ms]
2025-06-02 18:09:29.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:09:34.797 [info] > git config --get commit.template [12ms]
2025-06-02 18:09:34.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:09:34.834 [info] > git status -z -uall [9ms]
2025-06-02 18:09:34.836 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:09:39.891 [info] > git config --get commit.template [31ms]
2025-06-02 18:09:39.891 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:09:39.920 [info] > git status -z -uall [13ms]
2025-06-02 18:09:39.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:09:48.340 [info] > git config --get commit.template [2ms]
2025-06-02 18:09:48.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:09:48.386 [info] > git status -z -uall [11ms]
2025-06-02 18:09:48.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:09:53.420 [info] > git config --get commit.template [12ms]
2025-06-02 18:09:53.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:09:53.458 [info] > git status -z -uall [14ms]
2025-06-02 18:09:53.460 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:09:58.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:09:58.491 [info] > git config --get commit.template [14ms]
2025-06-02 18:09:58.517 [info] > git status -z -uall [11ms]
2025-06-02 18:09:58.518 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:10:03.544 [info] > git config --get commit.template [12ms]
2025-06-02 18:10:03.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:10:03.568 [info] > git status -z -uall [11ms]
2025-06-02 18:10:03.572 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:10:16.351 [info] > git config --get commit.template [39ms]
2025-06-02 18:10:16.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-06-02 18:10:16.389 [info] > git status -z -uall [21ms]
2025-06-02 18:10:16.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:10:26.321 [info] > git config --get commit.template [2ms]
2025-06-02 18:10:26.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:10:26.351 [info] > git status -z -uall [8ms]
2025-06-02 18:10:26.352 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:10:31.378 [info] > git config --get commit.template [12ms]
2025-06-02 18:10:31.381 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:10:31.415 [info] > git status -z -uall [15ms]
2025-06-02 18:10:31.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:10:36.438 [info] > git config --get commit.template [3ms]
2025-06-02 18:10:36.449 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:10:36.655 [info] > git status -z -uall [193ms]
2025-06-02 18:10:36.656 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [184ms]
2025-06-02 18:10:41.691 [info] > git config --get commit.template [16ms]
2025-06-02 18:10:41.693 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:10:41.734 [info] > git status -z -uall [17ms]
2025-06-02 18:10:41.735 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:10:46.758 [info] > git config --get commit.template [3ms]
2025-06-02 18:10:46.776 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:10:46.815 [info] > git status -z -uall [18ms]
2025-06-02 18:10:46.819 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:10:51.852 [info] > git config --get commit.template [7ms]
2025-06-02 18:10:51.870 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:10:51.891 [info] > git status -z -uall [8ms]
2025-06-02 18:10:51.892 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:10:56.913 [info] > git config --get commit.template [2ms]
2025-06-02 18:10:56.936 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:10:57.015 [info] > git status -z -uall [40ms]
2025-06-02 18:10:57.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:11:02.048 [info] > git config --get commit.template [4ms]
2025-06-02 18:11:02.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:11:02.088 [info] > git status -z -uall [11ms]
2025-06-02 18:11:02.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:11:07.114 [info] > git config --get commit.template [4ms]
2025-06-02 18:11:07.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-06-02 18:11:07.160 [info] > git status -z -uall [12ms]
2025-06-02 18:11:07.161 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:11:12.185 [info] > git config --get commit.template [1ms]
2025-06-02 18:11:12.199 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:11:12.239 [info] > git status -z -uall [16ms]
2025-06-02 18:11:12.242 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:11:17.266 [info] > git config --get commit.template [10ms]
2025-06-02 18:11:17.269 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:11:17.292 [info] > git status -z -uall [12ms]
2025-06-02 18:11:17.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:11:22.314 [info] > git config --get commit.template [0ms]
2025-06-02 18:11:22.334 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:11:22.363 [info] > git status -z -uall [14ms]
2025-06-02 18:11:22.364 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:11:27.412 [info] > git config --get commit.template [13ms]
2025-06-02 18:11:27.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-06-02 18:11:27.440 [info] > git status -z -uall [15ms]
2025-06-02 18:11:27.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:11:32.473 [info] > git config --get commit.template [16ms]
2025-06-02 18:11:32.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:11:32.501 [info] > git status -z -uall [15ms]
2025-06-02 18:11:32.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:11:37.542 [info] > git config --get commit.template [16ms]
2025-06-02 18:11:37.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:11:37.572 [info] > git status -z -uall [13ms]
2025-06-02 18:11:37.573 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:11:42.653 [info] > git config --get commit.template [3ms]
2025-06-02 18:11:42.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:11:42.693 [info] > git status -z -uall [11ms]
2025-06-02 18:11:42.697 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:11:47.735 [info] > git config --get commit.template [14ms]
2025-06-02 18:11:47.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:11:47.774 [info] > git status -z -uall [21ms]
2025-06-02 18:11:47.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-06-02 18:11:52.813 [info] > git config --get commit.template [9ms]
2025-06-02 18:11:52.814 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:11:52.834 [info] > git status -z -uall [10ms]
2025-06-02 18:11:52.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:11:57.872 [info] > git config --get commit.template [12ms]
2025-06-02 18:11:57.880 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-06-02 18:11:57.909 [info] > git status -z -uall [14ms]
2025-06-02 18:11:57.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-02 18:12:02.943 [info] > git config --get commit.template [10ms]
2025-06-02 18:12:02.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:12:02.974 [info] > git status -z -uall [14ms]
2025-06-02 18:12:02.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:12:08.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:12:08.011 [info] > git config --get commit.template [16ms]
2025-06-02 18:12:08.033 [info] > git status -z -uall [9ms]
2025-06-02 18:12:08.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-06-02 18:12:13.067 [info] > git config --get commit.template [14ms]
2025-06-02 18:12:13.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:12:13.092 [info] > git status -z -uall [12ms]
2025-06-02 18:12:13.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:12:21.751 [info] > git config --get commit.template [9ms]
2025-06-02 18:12:21.752 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:12:21.776 [info] > git status -z -uall [13ms]
2025-06-02 18:12:21.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:12:26.819 [info] > git config --get commit.template [19ms]
2025-06-02 18:12:26.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:12:26.859 [info] > git status -z -uall [22ms]
2025-06-02 18:12:26.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:12:31.902 [info] > git config --get commit.template [20ms]
2025-06-02 18:12:31.906 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:12:31.952 [info] > git status -z -uall [25ms]
2025-06-02 18:12:31.953 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:12:37.001 [info] > git config --get commit.template [1ms]
2025-06-02 18:12:37.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:12:37.216 [info] > git status -z -uall [125ms]
2025-06-02 18:12:37.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [88ms]
2025-06-02 18:12:42.245 [info] > git config --get commit.template [10ms]
2025-06-02 18:12:42.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:12:42.292 [info] > git status -z -uall [13ms]
2025-06-02 18:12:42.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:12:48.949 [info] > git config --get commit.template [0ms]
2025-06-02 18:12:48.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:12:48.997 [info] > git status -z -uall [14ms]
2025-06-02 18:12:48.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:12:54.036 [info] > git config --get commit.template [18ms]
2025-06-02 18:12:54.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:12:54.069 [info] > git status -z -uall [20ms]
2025-06-02 18:12:54.069 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:12:59.106 [info] > git config --get commit.template [16ms]
2025-06-02 18:12:59.108 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:12:59.133 [info] > git status -z -uall [11ms]
2025-06-02 18:12:59.133 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:13:04.154 [info] > git config --get commit.template [2ms]
2025-06-02 18:13:04.171 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-06-02 18:13:04.207 [info] > git status -z -uall [19ms]
2025-06-02 18:13:04.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-06-02 18:13:09.259 [info] > git config --get commit.template [12ms]
2025-06-02 18:13:09.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-06-02 18:13:09.294 [info] > git status -z -uall [16ms]
2025-06-02 18:13:09.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:13:15.087 [info] > git config --get commit.template [22ms]
2025-06-02 18:13:15.090 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:13:15.123 [info] > git status -z -uall [15ms]
2025-06-02 18:13:15.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-02 18:13:20.147 [info] > git config --get commit.template [0ms]
2025-06-02 18:13:20.160 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:13:20.195 [info] > git status -z -uall [20ms]
2025-06-02 18:13:20.195 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-02 18:13:25.238 [info] > git config --get commit.template [18ms]
2025-06-02 18:13:25.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-02 18:13:25.280 [info] > git status -z -uall [19ms]
2025-06-02 18:13:25.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:13:30.305 [info] > git config --get commit.template [2ms]
2025-06-02 18:13:30.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:13:30.418 [info] > git status -z -uall [78ms]
2025-06-02 18:13:30.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-06-02 18:13:35.451 [info] > git config --get commit.template [14ms]
2025-06-02 18:13:35.453 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:13:35.478 [info] > git status -z -uall [11ms]
2025-06-02 18:13:35.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:13:40.510 [info] > git config --get commit.template [8ms]
2025-06-02 18:13:40.513 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-02 18:13:40.541 [info] > git status -z -uall [10ms]
2025-06-02 18:13:40.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:13:45.573 [info] > git config --get commit.template [12ms]
2025-06-02 18:13:45.574 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-02 18:13:45.593 [info] > git status -z -uall [8ms]
2025-06-02 18:13:45.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-02 18:13:50.618 [info] > git config --get commit.template [10ms]
2025-06-02 18:13:50.620 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:13:50.643 [info] > git status -z -uall [16ms]
2025-06-02 18:13:50.644 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-06-02 18:13:55.669 [info] > git config --get commit.template [13ms]
2025-06-02 18:13:55.675 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 18:13:55.706 [info] > git status -z -uall [16ms]
2025-06-02 18:13:55.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:14:00.753 [info] > git config --get commit.template [15ms]
2025-06-02 18:14:00.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-06-02 18:14:00.788 [info] > git status -z -uall [15ms]
2025-06-02 18:14:00.791 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-02 18:14:05.808 [info] > git config --get commit.template [2ms]
2025-06-02 18:14:05.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-02 18:14:05.843 [info] > git status -z -uall [10ms]
2025-06-02 18:14:05.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
