import{t as L,m as O}from"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";var N=Object.defineProperty,M=Object.getOwnPropertyDescriptor,K=Object.getOwnPropertyNames,R=Object.prototype.hasOwnProperty,n={};((e,t,r,l)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of K(t))R.call(e,i)||i===r||N(e,i,{get:()=>t[i],enumerable:!(l=M(t,i))||l.enumerable})})(n,O,"default");var E=class{constructor(e,t){this._modeId=e,this._defaults=t,this._worker=null,this._client=null,this._configChangeListener=this._defaults.onDidChange(()=>this._stopWorker()),this._updateExtraLibsToken=0,this._extraLibsChangeListener=this._defaults.onDidExtraLibsChange(()=>this._updateExtraLibs())}dispose(){this._configChangeListener.dispose(),this._extraLibsChangeListener.dispose(),this._stopWorker()}_stopWorker(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null}async _updateExtraLibs(){if(!this._worker)return;const e=++this._updateExtraLibsToken,t=await this._worker.getProxy();this._updateExtraLibsToken===e&&t.updateExtraLibs(this._defaults.getExtraLibs())}_getClient(){return this._client||(this._client=(async()=>(this._worker=n.editor.createWebWorker({moduleId:"vs/language/typescript/tsWorker",label:this._modeId,keepIdleModels:!0,createData:{compilerOptions:this._defaults.getCompilerOptions(),extraLibs:this._defaults.getExtraLibs(),customWorkerPath:this._defaults.workerOptions.customWorkerPath,inlayHintsOptions:this._defaults.inlayHintsOptions}}),this._defaults.getEagerModelSync()?await this._worker.withSyncedResources(n.editor.getModels().filter(e=>e.getLanguageId()===this._modeId).map(e=>e.uri)):await this._worker.getProxy()))()),this._client}async getLanguageServiceWorker(...e){const t=await this._getClient();return this._worker&&await this._worker.withSyncedResources(e),t}},s={};function C(e,t,r=0){if(typeof e=="string")return e;if(e===void 0)return"";let l="";if(r){l+=t;for(let i=0;i<r;i++)l+="  "}if(l+=e.messageText,r++,e.next)for(const i of e.next)l+=C(i,t,r);return l}function _(e){return e?e.map(t=>t.text).join(""):""}s["lib.d.ts"]=!0,s["lib.decorators.d.ts"]=!0,s["lib.decorators.legacy.d.ts"]=!0,s["lib.dom.asynciterable.d.ts"]=!0,s["lib.dom.d.ts"]=!0,s["lib.dom.iterable.d.ts"]=!0,s["lib.es2015.collection.d.ts"]=!0,s["lib.es2015.core.d.ts"]=!0,s["lib.es2015.d.ts"]=!0,s["lib.es2015.generator.d.ts"]=!0,s["lib.es2015.iterable.d.ts"]=!0,s["lib.es2015.promise.d.ts"]=!0,s["lib.es2015.proxy.d.ts"]=!0,s["lib.es2015.reflect.d.ts"]=!0,s["lib.es2015.symbol.d.ts"]=!0,s["lib.es2015.symbol.wellknown.d.ts"]=!0,s["lib.es2016.array.include.d.ts"]=!0,s["lib.es2016.d.ts"]=!0,s["lib.es2016.full.d.ts"]=!0,s["lib.es2016.intl.d.ts"]=!0,s["lib.es2017.d.ts"]=!0,s["lib.es2017.date.d.ts"]=!0,s["lib.es2017.full.d.ts"]=!0,s["lib.es2017.intl.d.ts"]=!0,s["lib.es2017.object.d.ts"]=!0,s["lib.es2017.sharedmemory.d.ts"]=!0,s["lib.es2017.string.d.ts"]=!0,s["lib.es2017.typedarrays.d.ts"]=!0,s["lib.es2018.asyncgenerator.d.ts"]=!0,s["lib.es2018.asynciterable.d.ts"]=!0,s["lib.es2018.d.ts"]=!0,s["lib.es2018.full.d.ts"]=!0,s["lib.es2018.intl.d.ts"]=!0,s["lib.es2018.promise.d.ts"]=!0,s["lib.es2018.regexp.d.ts"]=!0,s["lib.es2019.array.d.ts"]=!0,s["lib.es2019.d.ts"]=!0,s["lib.es2019.full.d.ts"]=!0,s["lib.es2019.intl.d.ts"]=!0,s["lib.es2019.object.d.ts"]=!0,s["lib.es2019.string.d.ts"]=!0,s["lib.es2019.symbol.d.ts"]=!0,s["lib.es2020.bigint.d.ts"]=!0,s["lib.es2020.d.ts"]=!0,s["lib.es2020.date.d.ts"]=!0,s["lib.es2020.full.d.ts"]=!0,s["lib.es2020.intl.d.ts"]=!0,s["lib.es2020.number.d.ts"]=!0,s["lib.es2020.promise.d.ts"]=!0,s["lib.es2020.sharedmemory.d.ts"]=!0,s["lib.es2020.string.d.ts"]=!0,s["lib.es2020.symbol.wellknown.d.ts"]=!0,s["lib.es2021.d.ts"]=!0,s["lib.es2021.full.d.ts"]=!0,s["lib.es2021.intl.d.ts"]=!0,s["lib.es2021.promise.d.ts"]=!0,s["lib.es2021.string.d.ts"]=!0,s["lib.es2021.weakref.d.ts"]=!0,s["lib.es2022.array.d.ts"]=!0,s["lib.es2022.d.ts"]=!0,s["lib.es2022.error.d.ts"]=!0,s["lib.es2022.full.d.ts"]=!0,s["lib.es2022.intl.d.ts"]=!0,s["lib.es2022.object.d.ts"]=!0,s["lib.es2022.regexp.d.ts"]=!0,s["lib.es2022.sharedmemory.d.ts"]=!0,s["lib.es2022.string.d.ts"]=!0,s["lib.es2023.array.d.ts"]=!0,s["lib.es2023.collection.d.ts"]=!0,s["lib.es2023.d.ts"]=!0,s["lib.es2023.full.d.ts"]=!0,s["lib.es5.d.ts"]=!0,s["lib.es6.d.ts"]=!0,s["lib.esnext.collection.d.ts"]=!0,s["lib.esnext.d.ts"]=!0,s["lib.esnext.decorators.d.ts"]=!0,s["lib.esnext.disposable.d.ts"]=!0,s["lib.esnext.full.d.ts"]=!0,s["lib.esnext.intl.d.ts"]=!0,s["lib.esnext.object.d.ts"]=!0,s["lib.esnext.promise.d.ts"]=!0,s["lib.scripthost.d.ts"]=!0,s["lib.webworker.asynciterable.d.ts"]=!0,s["lib.webworker.d.ts"]=!0,s["lib.webworker.importscripts.d.ts"]=!0,s["lib.webworker.iterable.d.ts"]=!0;var y=class{constructor(e){this._worker=e}_textSpanToRange(e,t){let r=e.getPositionAt(t.start),l=e.getPositionAt(t.start+t.length),{lineNumber:i,column:d}=r,{lineNumber:a,column:o}=l;return{startLineNumber:i,startColumn:d,endLineNumber:a,endColumn:o}}},H=class{constructor(e){this._worker=e,this._libFiles={},this._hasFetchedLibFiles=!1,this._fetchLibFilesPromise=null}isLibFile(e){return!!e&&e.path.indexOf("/lib.")===0&&!!s[e.path.slice(1)]}getOrCreateModel(e){const t=n.Uri.parse(e),r=n.editor.getModel(t);if(r)return r;if(this.isLibFile(t)&&this._hasFetchedLibFiles)return n.editor.createModel(this._libFiles[t.path.slice(1)],"typescript",t);const l=L.getExtraLibs()[e];return l?n.editor.createModel(l.content,"typescript",t):null}_containsLibFile(e){for(let t of e)if(this.isLibFile(t))return!0;return!1}async fetchLibFilesIfNecessary(e){this._containsLibFile(e)&&await this._fetchLibFiles()}_fetchLibFiles(){return this._fetchLibFilesPromise||(this._fetchLibFilesPromise=this._worker().then(e=>e.getLibFiles()).then(e=>{this._hasFetchedLibFiles=!0,this._libFiles=e})),this._fetchLibFilesPromise}},V=class extends y{constructor(e,t,r,l){super(l),this._libFiles=e,this._defaults=t,this._selector=r,this._disposables=[],this._listener=Object.create(null);const i=o=>{if(o.getLanguageId()!==r)return;const c=()=>{const{onlyVisible:b}=this._defaults.getDiagnosticsOptions();b?o.isAttachedToEditor()&&this._doValidate(o):this._doValidate(o)};let u;const h=o.onDidChangeContent(()=>{clearTimeout(u),u=window.setTimeout(c,500)}),p=o.onDidChangeAttached(()=>{const{onlyVisible:b}=this._defaults.getDiagnosticsOptions();b&&(o.isAttachedToEditor()?c():n.editor.setModelMarkers(o,this._selector,[]))});this._listener[o.uri.toString()]={dispose(){h.dispose(),p.dispose(),clearTimeout(u)}},c()},d=o=>{n.editor.setModelMarkers(o,this._selector,[]);const c=o.uri.toString();this._listener[c]&&(this._listener[c].dispose(),delete this._listener[c])};this._disposables.push(n.editor.onDidCreateModel(o=>i(o))),this._disposables.push(n.editor.onWillDisposeModel(d)),this._disposables.push(n.editor.onDidChangeModelLanguage(o=>{d(o.model),i(o.model)})),this._disposables.push({dispose(){for(const o of n.editor.getModels())d(o)}});const a=()=>{for(const o of n.editor.getModels())d(o),i(o)};this._disposables.push(this._defaults.onDidChange(a)),this._disposables.push(this._defaults.onDidExtraLibsChange(a)),n.editor.getModels().forEach(o=>i(o))}dispose(){this._disposables.forEach(e=>e&&e.dispose()),this._disposables=[]}async _doValidate(e){const t=await this._worker(e.uri);if(e.isDisposed())return;const r=[],{noSyntaxValidation:l,noSemanticValidation:i,noSuggestionDiagnostics:d}=this._defaults.getDiagnosticsOptions();l||r.push(t.getSyntacticDiagnostics(e.uri.toString())),i||r.push(t.getSemanticDiagnostics(e.uri.toString())),d||r.push(t.getSuggestionDiagnostics(e.uri.toString()));const a=await Promise.all(r);if(!a||e.isDisposed())return;const o=a.reduce((u,h)=>h.concat(u),[]).filter(u=>(this._defaults.getDiagnosticsOptions().diagnosticCodesToIgnore||[]).indexOf(u.code)===-1),c=o.map(u=>u.relatedInformation||[]).reduce((u,h)=>h.concat(u),[]).map(u=>u.file?n.Uri.parse(u.file.fileName):null);await this._libFiles.fetchLibFilesIfNecessary(c),e.isDisposed()||n.editor.setModelMarkers(e,this._selector,o.map(u=>this._convertDiagnostics(e,u)))}_convertDiagnostics(e,t){const r=t.start||0,l=t.length||1,{lineNumber:i,column:d}=e.getPositionAt(r),{lineNumber:a,column:o}=e.getPositionAt(r+l),c=[];return t.reportsUnnecessary&&c.push(n.MarkerTag.Unnecessary),t.reportsDeprecated&&c.push(n.MarkerTag.Deprecated),{severity:this._tsDiagnosticCategoryToMarkerSeverity(t.category),startLineNumber:i,startColumn:d,endLineNumber:a,endColumn:o,message:C(t.messageText,`
`),code:t.code.toString(),tags:c,relatedInformation:this._convertRelatedInformation(e,t.relatedInformation)}}_convertRelatedInformation(e,t){if(!t)return[];const r=[];return t.forEach(l=>{let i=e;if(l.file&&(i=this._libFiles.getOrCreateModel(l.file.fileName)),!i)return;const d=l.start||0,a=l.length||1,{lineNumber:o,column:c}=i.getPositionAt(d),{lineNumber:u,column:h}=i.getPositionAt(d+a);r.push({resource:i.uri,startLineNumber:o,startColumn:c,endLineNumber:u,endColumn:h,message:C(l.messageText,`
`)})}),r}_tsDiagnosticCategoryToMarkerSeverity(e){switch(e){case 1:return n.MarkerSeverity.Error;case 3:return n.MarkerSeverity.Info;case 0:return n.MarkerSeverity.Warning;case 2:return n.MarkerSeverity.Hint}return n.MarkerSeverity.Info}},j=class v extends y{get triggerCharacters(){return["."]}async provideCompletionItems(t,r,l,i){const d=t.getWordUntilPosition(r),a=new n.Range(r.lineNumber,d.startColumn,r.lineNumber,d.endColumn),o=t.uri,c=t.getOffsetAt(r),u=await this._worker(o);if(t.isDisposed())return;const h=await u.getCompletionsAtPosition(o.toString(),c);if(!(!h||t.isDisposed()))return{suggestions:h.entries.map(p=>{let b=a;if(p.replacementSpan){const k=t.getPositionAt(p.replacementSpan.start),S=t.getPositionAt(p.replacementSpan.start+p.replacementSpan.length);b=new n.Range(k.lineNumber,k.column,S.lineNumber,S.column)}const x=[];return p.kindModifiers!==void 0&&p.kindModifiers.indexOf("deprecated")!==-1&&x.push(n.languages.CompletionItemTag.Deprecated),{uri:o,position:r,offset:c,range:b,label:p.name,insertText:p.name,sortText:p.sortText,kind:v.convertKind(p.kind),tags:x}})}}async resolveCompletionItem(t,r){const l=t,i=l.uri,d=l.position,a=l.offset,o=await this._worker(i),c=await o.getCompletionEntryDetails(i.toString(),a,l.label);return c?{uri:i,position:d,label:c.name,kind:v.convertKind(c.kind),detail:_(c.displayParts),documentation:{value:v.createDocumentationString(c)}}:l}static convertKind(t){switch(t){case m.primitiveType:case m.keyword:return n.languages.CompletionItemKind.Keyword;case m.variable:case m.localVariable:return n.languages.CompletionItemKind.Variable;case m.memberVariable:case m.memberGetAccessor:case m.memberSetAccessor:return n.languages.CompletionItemKind.Field;case m.function:case m.memberFunction:case m.constructSignature:case m.callSignature:case m.indexSignature:return n.languages.CompletionItemKind.Function;case m.enum:return n.languages.CompletionItemKind.Enum;case m.module:return n.languages.CompletionItemKind.Module;case m.class:return n.languages.CompletionItemKind.Class;case m.interface:return n.languages.CompletionItemKind.Interface;case m.warning:return n.languages.CompletionItemKind.File}return n.languages.CompletionItemKind.Property}static createDocumentationString(t){let r=_(t.documentation);if(t.tags)for(const l of t.tags)r+=`

${F(l)}`;return r}};function F(e){let t=`*@${e.name}*`;if(e.name==="param"&&e.text){const[r,...l]=e.text;t+=`\`${r.text}\``,l.length>0&&(t+=` — ${l.map(i=>i.text).join(" ")}`)}else Array.isArray(e.text)?t+=` — ${e.text.map(r=>r.text).join(" ")}`:e.text&&(t+=` — ${e.text}`);return t}var W=class I extends y{constructor(){super(...arguments),this.signatureHelpTriggerCharacters=["(",","]}static _toSignatureHelpTriggerReason(t){switch(t.triggerKind){case n.languages.SignatureHelpTriggerKind.TriggerCharacter:return t.triggerCharacter?t.isRetrigger?{kind:"retrigger",triggerCharacter:t.triggerCharacter}:{kind:"characterTyped",triggerCharacter:t.triggerCharacter}:{kind:"invoked"};case n.languages.SignatureHelpTriggerKind.ContentChange:return t.isRetrigger?{kind:"retrigger"}:{kind:"invoked"};case n.languages.SignatureHelpTriggerKind.Invoke:default:return{kind:"invoked"}}}async provideSignatureHelp(t,r,l,i){const d=t.uri,a=t.getOffsetAt(r),o=await this._worker(d);if(t.isDisposed())return;const c=await o.getSignatureHelpItems(d.toString(),a,{triggerReason:I._toSignatureHelpTriggerReason(i)});if(!c||t.isDisposed())return;const u={activeSignature:c.selectedItemIndex,activeParameter:c.argumentIndex,signatures:[]};return c.items.forEach(h=>{const p={label:"",parameters:[]};p.documentation={value:_(h.documentation)},p.label+=_(h.prefixDisplayParts),h.parameters.forEach((b,x,k)=>{const S=_(b.displayParts),P={label:S,documentation:{value:_(b.documentation)}};p.label+=S,p.parameters.push(P),x<k.length-1&&(p.label+=_(h.separatorDisplayParts))}),p.label+=_(h.suffixDisplayParts),u.signatures.push(p)}),{value:u,dispose(){}}}},B=class extends y{async provideHover(e,t,r){const l=e.uri,i=e.getOffsetAt(t),d=await this._worker(l);if(e.isDisposed())return;const a=await d.getQuickInfoAtPosition(l.toString(),i);if(!a||e.isDisposed())return;const o=_(a.documentation),c=a.tags?a.tags.map(h=>F(h)).join(`  

`):"",u=_(a.displayParts);return{range:this._textSpanToRange(e,a.textSpan),contents:[{value:"```typescript\n"+u+"\n```\n"},{value:o+(c?`

`+c:"")}]}}},U=class extends y{async provideDocumentHighlights(e,t,r){const l=e.uri,i=e.getOffsetAt(t),d=await this._worker(l);if(e.isDisposed())return;const a=await d.getDocumentHighlights(l.toString(),i,[l.toString()]);return a&&!e.isDisposed()?a.flatMap(o=>o.highlightSpans.map(c=>({range:this._textSpanToRange(e,c.textSpan),kind:c.kind==="writtenReference"?n.languages.DocumentHighlightKind.Write:n.languages.DocumentHighlightKind.Text}))):void 0}},$=class extends y{constructor(e,t){super(t),this._libFiles=e}async provideDefinition(e,t,r){const l=e.uri,i=e.getOffsetAt(t),d=await this._worker(l);if(e.isDisposed())return;const a=await d.getDefinitionAtPosition(l.toString(),i);if(!a||e.isDisposed()||(await this._libFiles.fetchLibFilesIfNecessary(a.map(c=>n.Uri.parse(c.fileName))),e.isDisposed()))return;const o=[];for(let c of a){const u=this._libFiles.getOrCreateModel(c.fileName);u&&o.push({uri:u.uri,range:this._textSpanToRange(u,c.textSpan)})}return o}},z=class extends y{constructor(e,t){super(t),this._libFiles=e}async provideReferences(e,t,r,l){const i=e.uri,d=e.getOffsetAt(t),a=await this._worker(i);if(e.isDisposed())return;const o=await a.getReferencesAtPosition(i.toString(),d);if(!o||e.isDisposed()||(await this._libFiles.fetchLibFilesIfNecessary(o.map(u=>n.Uri.parse(u.fileName))),e.isDisposed()))return;const c=[];for(let u of o){const h=this._libFiles.getOrCreateModel(u.fileName);h&&c.push({uri:h.uri,range:this._textSpanToRange(h,u.textSpan)})}return c}},G=class extends y{async provideDocumentSymbols(e,t){const r=e.uri,l=await this._worker(r);if(e.isDisposed())return;const i=await l.getNavigationTree(r.toString());if(!i||e.isDisposed())return;const d=(a,o)=>{var c;return{name:a.text,detail:"",kind:f[a.kind]||n.languages.SymbolKind.Variable,range:this._textSpanToRange(e,a.spans[0]),selectionRange:this._textSpanToRange(e,a.spans[0]),tags:[],children:(c=a.childItems)==null?void 0:c.map(u=>d(u,a.text)),containerName:o}};return i.childItems?i.childItems.map(a=>d(a)):[]}},g,m=(g=class{},g.unknown="",g.keyword="keyword",g.script="script",g.module="module",g.class="class",g.interface="interface",g.type="type",g.enum="enum",g.variable="var",g.localVariable="local var",g.function="function",g.localFunction="local function",g.memberFunction="method",g.memberGetAccessor="getter",g.memberSetAccessor="setter",g.memberVariable="property",g.constructorImplementation="constructor",g.callSignature="call",g.indexSignature="index",g.constructSignature="construct",g.parameter="parameter",g.typeParameter="type parameter",g.primitiveType="primitive type",g.label="label",g.alias="alias",g.const="const",g.let="let",g.warning="warning",g),f=Object.create(null);f[m.module]=n.languages.SymbolKind.Module,f[m.class]=n.languages.SymbolKind.Class,f[m.enum]=n.languages.SymbolKind.Enum,f[m.interface]=n.languages.SymbolKind.Interface,f[m.memberFunction]=n.languages.SymbolKind.Method,f[m.memberVariable]=n.languages.SymbolKind.Property,f[m.memberGetAccessor]=n.languages.SymbolKind.Property,f[m.memberSetAccessor]=n.languages.SymbolKind.Property,f[m.variable]=n.languages.SymbolKind.Variable,f[m.const]=n.languages.SymbolKind.Variable,f[m.localVariable]=n.languages.SymbolKind.Variable,f[m.variable]=n.languages.SymbolKind.Variable,f[m.function]=n.languages.SymbolKind.Function,f[m.localFunction]=n.languages.SymbolKind.Function;var D,A,w=class extends y{static _convertOptions(e){return{ConvertTabsToSpaces:e.insertSpaces,TabSize:e.tabSize,IndentSize:e.tabSize,IndentStyle:2,NewLineCharacter:`
`,InsertSpaceAfterCommaDelimiter:!0,InsertSpaceAfterSemicolonInForStatements:!0,InsertSpaceBeforeAndAfterBinaryOperators:!0,InsertSpaceAfterKeywordsInControlFlowStatements:!0,InsertSpaceAfterFunctionKeywordForAnonymousFunctions:!0,InsertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis:!1,InsertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets:!1,InsertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces:!1,PlaceOpenBraceOnNewLineForControlBlocks:!1,PlaceOpenBraceOnNewLineForFunctions:!1}}_convertTextChanges(e,t){return{text:t.newText,range:this._textSpanToRange(e,t.span)}}},J=class extends w{constructor(){super(...arguments),this.canFormatMultipleRanges=!1}async provideDocumentRangeFormattingEdits(e,t,r,l){const i=e.uri,d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=await this._worker(i);if(e.isDisposed())return;const c=await o.getFormattingEditsForRange(i.toString(),d,a,w._convertOptions(r));return c&&!e.isDisposed()?c.map(u=>this._convertTextChanges(e,u)):void 0}},Q=class extends w{get autoFormatTriggerCharacters(){return[";","}",`
`]}async provideOnTypeFormattingEdits(e,t,r,l,i){const d=e.uri,a=e.getOffsetAt(t),o=await this._worker(d);if(e.isDisposed())return;const c=await o.getFormattingEditsAfterKeystroke(d.toString(),a,r,w._convertOptions(l));return c&&!e.isDisposed()?c.map(u=>this._convertTextChanges(e,u)):void 0}},q=class extends w{async provideCodeActions(e,t,r,l){const i=e.uri,d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=w._convertOptions(e.getOptions()),c=r.markers.filter(p=>p.code).map(p=>p.code).map(Number),u=await this._worker(i);if(e.isDisposed())return;const h=await u.getCodeFixesAtPosition(i.toString(),d,a,c,o);return!h||e.isDisposed()?{actions:[],dispose:()=>{}}:{actions:h.filter(p=>p.changes.filter(b=>b.isNewFile).length===0).map(p=>this._tsCodeFixActionToMonacoCodeAction(e,r,p)),dispose:()=>{}}}_tsCodeFixActionToMonacoCodeAction(e,t,r){const l=[];for(const i of r.changes)for(const d of i.textChanges)l.push({resource:e.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(e,d.span),text:d.newText}});return{title:r.description,edit:{edits:l},diagnostics:t.markers,kind:"quickfix"}}},X=class extends y{constructor(e,t){super(t),this._libFiles=e}async provideRenameEdits(e,t,r,l){const i=e.uri,d=i.toString(),a=e.getOffsetAt(t),o=await this._worker(i);if(e.isDisposed())return;const c=await o.getRenameInfo(d,a,{allowRenameOfImportPath:!1});if(c.canRename===!1)return{edits:[],rejectReason:c.localizedErrorMessage};if(c.fileToRename!==void 0)throw new Error("Renaming files is not supported.");const u=await o.findRenameLocations(d,a,!1,!1,!1);if(!u||e.isDisposed())return;const h=[];for(const p of u){const b=this._libFiles.getOrCreateModel(p.fileName);if(!b)throw new Error(`Unknown file ${p.fileName}.`);h.push({resource:b.uri,versionId:void 0,textEdit:{range:this._textSpanToRange(b,p.textSpan),text:r}})}return{edits:h}}},Y=class extends y{async provideInlayHints(e,t,r){const l=e.uri,i=l.toString(),d=e.getOffsetAt({lineNumber:t.startLineNumber,column:t.startColumn}),a=e.getOffsetAt({lineNumber:t.endLineNumber,column:t.endColumn}),o=await this._worker(l);return e.isDisposed()?null:{hints:(await o.provideInlayHints(i,d,a)).map(c=>({...c,label:c.text,position:e.getPositionAt(c.position),kind:this._convertHintKind(c.kind)})),dispose:()=>{}}}_convertHintKind(e){return e==="Parameter"?n.languages.InlayHintKind.Parameter:n.languages.InlayHintKind.Type}};function te(e){A=T(e,"typescript")}function se(e){D=T(e,"javascript")}function ie(){return new Promise((e,t)=>{if(!D)return t("JavaScript not registered!");e(D)})}function re(){return new Promise((e,t)=>{if(!A)return t("TypeScript not registered!");e(A)})}function T(e,t){const r=[],l=new E(t,e),i=(...a)=>l.getLanguageServiceWorker(...a),d=new H(i);return function(){const{modeConfiguration:a}=e;(function(o){for(;o.length;)o.pop().dispose()})(r),a.completionItems&&r.push(n.languages.registerCompletionItemProvider(t,new j(i))),a.signatureHelp&&r.push(n.languages.registerSignatureHelpProvider(t,new W(i))),a.hovers&&r.push(n.languages.registerHoverProvider(t,new B(i))),a.documentHighlights&&r.push(n.languages.registerDocumentHighlightProvider(t,new U(i))),a.definitions&&r.push(n.languages.registerDefinitionProvider(t,new $(d,i))),a.references&&r.push(n.languages.registerReferenceProvider(t,new z(d,i))),a.documentSymbols&&r.push(n.languages.registerDocumentSymbolProvider(t,new G(i))),a.rename&&r.push(n.languages.registerRenameProvider(t,new X(d,i))),a.documentRangeFormattingEdits&&r.push(n.languages.registerDocumentRangeFormattingEditProvider(t,new J(i))),a.onTypeFormattingEdits&&r.push(n.languages.registerOnTypeFormattingEditProvider(t,new Q(i))),a.codeActions&&r.push(n.languages.registerCodeActionProvider(t,new q(i))),a.inlayHints&&r.push(n.languages.registerInlayHintsProvider(t,new Y(i))),a.diagnostics&&r.push(new V(d,e,t,i))}(),i}export{y as Adapter,q as CodeActionAdaptor,$ as DefinitionAdapter,V as DiagnosticsAdapter,U as DocumentHighlightAdapter,J as FormatAdapter,w as FormatHelper,Q as FormatOnTypeAdapter,Y as InlayHintsAdapter,m as Kind,H as LibFiles,G as OutlineAdapter,B as QuickInfoAdapter,z as ReferenceAdapter,X as RenameAdapter,W as SignatureHelpAdapter,j as SuggestAdapter,E as WorkerManager,C as flattenDiagnosticMessageText,ie as getJavaScriptWorker,re as getTypeScriptWorker,se as setupJavaScript,te as setupTypeScript};
