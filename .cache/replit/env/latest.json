{"environment": {"CFLAGS": "", "COLORTERM": "truecolor", "DISPLAY": ":0", "DOCKER_CONFIG": "/home/<USER>/workspace/.config/docker", "GIT_ASKPASS": "replit-git-askpass", "GIT_EDITOR": "replit-git-editor", "GI_TYPELIB_PATH": "", "HOME": "/home/<USER>", "HOSTNAME": "73390712574e", "LANG": "en_US.UTF-8", "LDFLAGS": "", "LIBGL_DRIVERS_PATH": "/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri", "LOCALE_ARCHIVE": "/usr/lib/locale/locale-archive", "NIXPKGS_ALLOW_UNFREE": "1", "NIX_CFLAGS_COMPILE": "", "NIX_LDFLAGS": "", "NIX_PATH": "nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels", "NIX_PROFILES": "/nix/var/nix/profiles/default /home/<USER>/.nix-profile", "NIX_PS1": "\\[\\033[01;34m\\]\\w\\[\\033[00m\\]\\$ ", "PATH": "/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/sww4j97gzg3wl9rykrmjfy8736iiz11n-pid1/bin:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "PKG_CONFIG_PATH": "", "PKG_CONFIG_PATH_FOR_TARGET": "", "PROMPT_DIRTRIM": "2", "REPLIT_BASHRC": "/nix/store/7mbv5hcsh9cj7pk4maggp301fma07cm0-replit-bashrc/bashrc", "REPLIT_CLI": "/nix/store/kg7y2cbq8jfcs6qj2hikk83q594qnzpc-pid1-0.0.1/bin/replit", "REPLIT_CLUSTER": "worf", "REPLIT_DB_URL": "https://kv.replit.com/v0/eyJhbGciOiJIUzUxMiIsImlzcyI6ImNvbm1hbiIsImtpZCI6InByb2Q6MSIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjb25tYW4iLCJleHAiOjE3NDkwMTAzODEsImlhdCI6MTc0ODg5ODc4MSwiZGF0YWJhc2VfaWQiOiIzMDRlNTA2Mi04MjFkLTQyYjQtOGJmMy01MmNlZTZhMTRmZmQifQ.eEh4r5upqDwit3759K8_LuZ93MsZEiOxEfcpBqR8q8ylB_0aPP3S-N4GeSrxnkIreAkB9i84jQWOxdIgvtOATg", "REPLIT_DEV_DOMAIN": "304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev", "REPLIT_DOMAINS": "304e5062-821d-42b4-8bf3-52cee6a14ffd-00-35plnljz3wwcd.worf.replit.dev", "REPLIT_ENVIRONMENT": "production", "REPLIT_LD_AUDIT": "/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so", "REPLIT_NIX_CHANNEL": "stable-24_05", "REPLIT_PID1_FLAG_NIXMODULES_BEFORE_REPLIT_NIX": "1", "REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER": "1", "REPLIT_PID1_VERSION": "0.0.0-b1d6c11", "REPLIT_RIPPKGS_INDICES": "/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices", "REPLIT_RTLD_LOADER": "1", "REPLIT_SUBCLUSTER": "paid", "REPL_HOME": "/home/<USER>/workspace", "REPL_ID": "304e5062-821d-42b4-8bf3-52cee6a14ffd", "REPL_IDENTITY": "v2.public.Q2lRek1EUmxOVEEyTWkwNE1qRmtMVFF5WWpRdE9HSm1NeTAxTW1ObFpUWmhNVFJtWm1RU0IwTm9aWGQ1TkRJYUIwTm9aWGQ1UVVraUpETXdOR1UxTURZeUxUZ3lNV1F0TkRKaU5DMDRZbVl6TFRVeVkyVmxObUV4TkdabVpEalZ6YjhIV2d3S0JIZHZjbVlTQkhCaGFXUT0h1rHlVlSvktwIIYoRx8o5EZfV9O7DT1wLdFaJCGCa8AMzc96tCoh1w7cXKn07cGzHVYDtxEQF8sBOpO4bl60A.R0FFaUJtTnZibTFoYmhLQkNIWXlMbkIxWW14cFl5NVJNbVF6VTFod2VGcHFVak5WVm14U1pHbDBObUZ0YUVKVFZrNUZVVmRzYTJKcmJFVlJNRXB2VWtkdk0wc3dPVVpSVjJoMlVUQmtRbFpYUmt0YU1qbHlWRmh3UWsxR2NGVldXR1JQWVd0c01GUXdVa3BsUm5CRVRVUkNUbUpWYTNkVVJsSnZZVlp3Y1ZSWVVrOVdSWEJ4VjJ4a1ZrMXNiRlZTVkVKaFlsWndjbEl5WkhKVk1FbDNWRzA1WVZkSFVURlVhMUpLV1ZWS1ZXRnNXalpaYW1oSlVqSmtXbUZWU2tsYVNGcHFZbFpzYUZGdGJIWlNWMDVJVW01Q1lWRXdhM2haV0hCS1pGZE9TVlp0YkdsU01uaHhWRWN4YmsxdFVuSlhhM2hvVWxkb1RWZFhlRUpOUjBwVlUydG9WV0p0ZUhCV2FrcDNZbXhKZDJGRk1XcFdWa3BLVmtkNFQxTXdNVlpsUjJ4YVlsaG9RMVpFU2s5aU1XeFpZa1pzYWxaR1JUbFphSFJFZERaeVgyZDRUbWswWm5OamFrNVdka2xDUWxCYWJqbFRkazFCWVVzdFVGWkxibUpPYW5kRWVtMUtORFJoYURnd1owaFlSemwyVGxBMGNXeFlSVkpoVEhKS2FVTnJTR3Q0VEZndE0wWmxkbHBCVVM1U01FWkdZVlZLZEZSdVdtbGlWRVp2V1cxb1RWbHJSblZYV0d4TlltdEplRmRYTVRSalJtdzFUbFpLVG1KV1NUSldWRVpyVFVkV2RGTnNXbFJpYmtKWFZtMHhORlV4VW5KVmJVWk9WbTVDVjFVeWRFOVdSbHBaWVVWV1ZtVnJTbkpWYWtFeFUxWkdjbE5zV2s1U2JIQlRWbTF3VDFsWFVsZGlNMmhUWWxkb1UxWnFTbTlrVmxaWVpFZDBhV0pGTlZoWmExWlBWbTFLVldKRlZsWmhhMHBJV2tkNGMxWnNTblZTYkVwWFZsaENTbFl5Y0VOak1XUnpVbXhvYUZOR2NGTlVWV1JUVVRGYVIxcEZaRkppVlZwSlYydFZlRlV3TVhSVmEzUlhUVlphVkZWVVNrcGtNVkp5WVVaS1YyRXhjSFpXVmxwcllqSktjMVJ1U21sVFJWcFlXVzEwZDFReGJGZFZiR1JPVFZoQ1NGZHJWakJoYXpGeVYyeHNWMUp0YUZoV1JFWmhaRWRXU1dOR1pGZGlWa3BKVmtaU1MxUXlUWGxUYWxwV1lYcHNXRlJYZUV0aU1WbDVUVlJTVkUxcldrZFVWbFpyVmtkS1JsZHNXbHBXZWtVd1YxWmFjMDVzUmxWU2JYQnBVbGhDTmxaRVJsZFpWMFY1VTJ4c1ZsWkZXbGRaYTFwaFkyeHdTR1ZGV214U2JrSkdWakl4ZDJGSFJYaGpSemxYWVd0YVZGVjZSazVsUmxwelUyeEdWMUpGU2pOV01uUmhWMjFPZEdORk1WQlhSVFI2V2tWV1drNVdjRVZTV0ZKcFlsUldVVlF3WkdGVmJVcFlZVVJLVkZKV2NIaFdhMVp5WkVkU1JXRkZjR2xpVm5CUlYwUkplRlpWTVhSWmVsSnFWMGhDUmxWclpGWk9SbHBGWWtaU2FFMVdTalpYYlhodllsZFdjbUo2UWxoV1ZURTJWMjF6ZDJWc1pGWk9XRXBVVmtkU1dWZFhhM2RPVmtweVZXMDVUMkZVUmt4VWFrazFVa1Y0YzFOWVpGTmhNWEJ2Vm14V2QwMUdXa2hPVjBab1ZqQndWbFZ0TURWWGJVcFlWV3BLVm1GcmNGQlZNVnBQWkZaa2RGSnNUbE5sYldjdw", "REPL_IDENTITY_KEY": "k2.secret.HIw42qMu_Okb76JcPQ044MBstRLfocm5vevBnsVH_f2Hq8UqEcps_ibYY3JtaOAYcuoMc1InUttuUA5yFrJerg", "REPL_IMAGE": "gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9", "REPL_LANGUAGE": "nix", "REPL_OWNER": "Chewy42", "REPL_OWNER_ID": "15722197", "REPL_PUBKEYS": "{\"crosis-ci\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"crosis-ci:1\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"crosis-ci:latest\":\"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=\",\"prod\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"prod:1\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"prod:2\":\"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=\",\"prod:3\":\"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=\",\"prod:4\":\"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=\",\"prod:5\":\"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=\",\"prod:latest\":\"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=\",\"vault-goval-token\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\",\"vault-goval-token:1\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\",\"vault-goval-token:latest\":\"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=\"}", "REPL_SLUG": "workspace", "USER": "runner", "XDG_CACHE_HOME": "/home/<USER>/workspace/.cache", "XDG_CONFIG_HOME": "/home/<USER>/workspace/.config", "XDG_DATA_DIRS": "/nix/store/hp7fmkb1rzmfxisj83c8f8dqz146nm6q-supabase-cli-1.168.1/share:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/share", "XDG_DATA_HOME": "/home/<USER>/workspace/.local/share", "__EGL_VENDOR_LIBRARY_FILENAMES": "/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json", "npm_config_prefix": "/home/<USER>/workspace/.config/npm/node_global"}}