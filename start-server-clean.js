#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to start the server with filtered console output
import { spawn } from 'child_process';

// Set environment variables
process.env.SUPABASE_URL = 'https://hrdjfukhzbzksqaupqie.supabase.co';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';
process.env.VITE_DATABASE_PASSWORD = 'Aloha808!';
process.env.NODE_ENV = 'development';

console.log('🚀 Starting ChewyAI server with filtered output...\n');

// Start the server process
const serverProcess = spawn('npx', ['tsx', 'server/index.ts'], {
  stdio: ['inherit', 'pipe', 'pipe'],
  cwd: process.cwd()
});

// Filter function to reduce noise
function shouldShowLog(line) {
  const noisePatterns = [
    /babel program\.body/,
    /babel.*enter/,
    /babel.*exit/,
    /babel.*Recursing/,
    /vite:config/,
    /express:router/,
    /\[background\/carbon\]/,
    /CarbonApi request/,
    /CONNECTION_REFUSED.*24678/
  ];
  
  return !noisePatterns.some(pattern => pattern.test(line));
}

// Handle stdout
serverProcess.stdout.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim() && shouldShowLog(line)) {
      console.log(line);
    }
  });
});

// Handle stderr
serverProcess.stderr.on('data', (data) => {
  const lines = data.toString().split('\n');
  lines.forEach(line => {
    if (line.trim() && shouldShowLog(line)) {
      console.error(line);
    }
  });
});

// Handle process exit
serverProcess.on('close', (code) => {
  console.log(`\n🛑 Server process exited with code ${code}`);
  process.exit(code);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down server...');
  serverProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down server...');
  serverProcess.kill('SIGTERM');
});
